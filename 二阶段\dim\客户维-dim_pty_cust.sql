SELECT 
'BJHL'||cust_no AS cust_wrd, --	客户关键字
cust_no AS bsn_stm_cust_no, --	业务系统客户编号
cust_nm AS cust_nm, --	客户名称
full_nm AS cust_full_nm, --	客户全称
crdt_cgy AS cert_tp_cd, --	证件类型代码
crdt_cgy_dsc AS cert_tp_cd_dsc, --	证件类型代码描述
crdt_id AS cert_no, --	证件编号
crdt_addr AS cert_addr, --	证件地址
contact_tel AS ctc_tel, --	联系电话
fax AS fax, --	传真
blng_prvc AS blng_prov_cd, --	所属省份代码
T3.admn_rgon_nm AS blng_prov_cd_dsc, --	所属省份代码描述
local_city AS lct_city_cd, --	所在城市代码
T4.admn_rgon_nm AS lct_city_cd_dsc, --	所在城市代码描述
wbt_pope AS lct_regn_cd, --	所在辖区代码
T5.admn_rgon_nm AS lct_regn_cd_dsc, --	所在辖区代码描述
legal_zip_code AS zipcode, --	邮政编码
msg_addr AS post_addr, --	通讯地址
entrst_mod AS etrs_mth_cd, --	委托方式代码
entrst_mod_dsc AS etrs_mth_cd_dsc, --	委托方式代码描述
svc_prj AS svc_prj_cd, --	服务项目代码
svc_prj_dsc AS svc_prj_cd_dsc, --	服务项目代码描述
pd_prj AS pd_prj_cd, --	产品项目代码
pd_prj_dsc AS pd_prj_cd_dsc, --	产品项目代码描述
cust_opn_acc_dt AS cust_opn_acc_dt, --	客户开户日期
cust_cncl_acct_dt AS cust_cncl_acct_dt, --	客户销户日期
cust_sts AS cust_stat_cd, --	客户状态代码
cust_sts_dsc AS cust_stat_cd_dsc, --	客户状态代码描述
biz_dp AS bus_dep_cd, --	营业部代码
biz_dp_dsc AS bus_dep_cd_dsc, --	营业部代码描述
spcl_prmpt_inf AS hot_tip_info, --	特别提示信息
rsk_lvl AS rsk_lvl_cd, --	风险级别代码
rsk_lvl_dsc AS rsk_lvl_cd_dsc, --	风险级别代码描述
ivs_pers_cl AS ivsr_cl_cd, --	投资者分类代码
ivs_pers_cl_dsc AS ivsr_cl_cd_dsc, --	投资者分类代码描述
CASE WHEN T58.IS_CNVR='N' THEN T1.org_flg ELSE CASE WHEN T58.CD_VAL IS NULL THEN 'WZ'||T1.org_flg ELSE T58.CD_VAL END END AS cust_tp_cd, --	客户类型代码
T13.NOTE AS cust_tp_cd_dsc, --	客户类型代码描述
cust_cntl_attr AS cust_cntl_attr_cd, --	客户控制属性代码
cust_cntl_attr_dsc AS cust_cntl_attr_cd_dsc, --	客户控制属性代码描述
ed_dgr_cd AS edu_cd, --	学历代码
ed_dgr_cd_dsc AS edu_cd_dsc, --	学历代码描述
ocp_cd AS ocp_cd, --	职业代码
ocp_cd_dsc AS ocp_cd_dsc, --	职业代码描述
nation_cd AS nat_cd, --	国籍代码
'' AS nat_cd_dsc, --	国籍代码描述
cust_card_no AS cust_card_no, --	客户卡号
cust_acm_pnt AS cust_itgl, -- 客户积分
acm_pnt_beg_dt AS itgl_start_dt, --	积分起始日期
email_addr AS email_addr, --	电子邮件地址
mbl_ph AS phon, --	手机
gnd AS sex_cd, --	性别代码
gnd_dsc AS sex_cd_dsc, --	性别代码描述
rctly_land_dt AS rcty_login_dt, --	最近登录日期
pswd_synz_flg AS pwd_scnt_flg, --	密码同步标志
pswd_mod_dt AS pwd_mod_dt, --	密码修改日期
crdt_beg_dt AS cert_start_dt, --	证件起始日期
crdt_ct_of_dt AS cert_expi_dt, --	证件截止日期
issu_ctf_unit AS issu_authy_nm, --	发证单位名称
crdt_avl_dt AS cert_tmvd, --	证件有效期
brth_dt AS birth_dt	 --	出生日期
FROM std.std_bjhl_tkhxx_d T1
LEFT JOIN (SELECT region_code  ,--代码 
                  admn_rgon_nm  --码值
             FROM std.std_bjhl_txzqydm_d  --行政区域代码表
             WHERE dt= '${dmp_day}'
         ) T3 
          ON T1.blng_prvc=T3.region_code
LEFT JOIN (SELECT region_code  ,--代码 
                  admn_rgon_nm  --码值
             FROM std.std_bjhl_txzqydm_d --行政区域代码表
             WHERE dt= '${dmp_day}'
         ) T4 
          ON T1.local_city=T4.region_code
LEFT JOIN (SELECT region_code  ,--代码 
                  admn_rgon_nm  --码值
             FROM std.std_bjhl_txzqydm_d --行政区域代码表
             WHERE dt= '${dmp_day}'
         ) T5 
         ON T1.wbt_pope=T5.region_code
LEFT JOIN (SELECT cd_val  ,--代码 
                  note  --码值
             FROM std.std_bjhl_txtdm_d  --LIVEBOS数据字典
			  WHERE flmc = '客户机构标志'
			  AND dt = '${dmp_day}'
         ) T13 
	  ON T1.org_flg=T13.cd_val
LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM dim.dim_pub_cd_val_src_trgt_mpng
WHERE SRC_TAB_ENG_NM='customer.tkhxx'
AND PUB_CD_NO= 'CD000009'
AND SRC_COL_ENG_NM = 'JGBZ'
) T58
ON T1.org_flg = T58.SRC_CD_VAL
where dt= '${dmp_day}'