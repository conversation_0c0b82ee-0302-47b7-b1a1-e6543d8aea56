with
  dws_all_trsfer_info as (
    select distinct
      *
    from
      dws.dws_all_trsfer_info x
    where
      x.prj_blng_dept_nm IN ('央企一部', '央企二部', '央企三部', '央企四部', '央企五部', '央企六部')
      and dt=${dmp_day}
      and exch='北交所'
      and prj_bsn_tp_cd in ('1F', '1D', 'GQ', '1C')
  ),
  lit_data AS (
    SELECT
      SUBSTR(info_publ_start_dt, 1, 7) AS month,
      SUBSTR(info_publ_start_dt, 1, 4) AS year,
      CASE
        WHEN prj_bsn_tp_cd_dsc='产权转让' THEN '产权转让'
        WHEN prj_bsn_tp_cd_dsc='企业增资' THEN '企业增资'
        ELSE '实物'
      END AS proj_type,
      prj_blng_dept_nm,
      COUNT(distinct prj_no) AS lit_prj_num,
      SUM(Lit_Amt) AS lit_amt,
      SUM(
        CASE
          WHEN prj_bsn_tp_cd_dsc='产权转让'
          and Prj_Stat_Cd_Dsc IN ('已成交', '已归档')
          and (COALESCE(prj_prt_sown_spvs_org_cd_dsc, '未知') not IN(
            '省级财政部门监管',
            '市级财政部门或金融办监管',
            '省级国资委监管',
            '省级其他部门监管',
            '市级国资委监管',
            '市级其他部门监管'
          ) OR (exch = '北交所' AND prj_prt_sown_spvs_org_cd_dsc IS NULL))
          and prj_no not like 'G0%'
          AND NOT array_contains (x2.distinct_id_array, Blng_Org) THEN 1
          ELSE 0
        END
      ) AS trsfer_prj_num
    FROM
      dws_all_trsfer_info
      cross join (
        SELECT
          collect_set (CTY_CONTRI_CORP_LEAD_DEPT) as distinct_id_array
        FROM
          ods.ods_bl_yqzx_fin_proj_list
      ) x2
    WHERE
      prj_bsn_tp_cd IN ('1C', '1D', '1F', 'GQ')
    GROUP BY
      SUBSTR(info_publ_start_dt, 1, 7),
      SUBSTR(info_publ_start_dt, 1, 4),
      CASE
        WHEN prj_bsn_tp_cd_dsc='产权转让' THEN '产权转让'
        WHEN prj_bsn_tp_cd_dsc='企业增资' THEN '企业增资'
        ELSE '实物'
      END,
      prj_blng_dept_nm
  ),
  trsfer_prj_num as (
    SELECT
      SUBSTR(BSN_REC_DEAL_DT, 1, 7) AS month,
      SUBSTR(BSN_REC_DEAL_DT, 1, 4) AS year,
      '产权转让' AS proj_type,
      prj_blng_dept_nm,
      SUM(
        CASE
          WHEN (COALESCE(prj_prt_sown_spvs_org_cd_dsc, '未知') not IN(
            '省级财政部门监管',
            '市级财政部门或金融办监管',
            '省级国资委监管',
            '省级其他部门监管',
            '市级国资委监管',
            '市级其他部门监管'
          ) OR (exch = '北交所' AND prj_prt_sown_spvs_org_cd_dsc IS NULL))
          AND prj_no not like 'G0%'
          AND NOT array_contains (x2.distinct_id_array, Blng_Org)
          AND prj_blng_dept_nm IN ('央企一部', '央企二部', '央企三部', '央企四部', '央企五部')
          THEN 1
          ELSE 0
        END
      ) AS trsfer_prj_num
    FROM
      dws_all_trsfer_info
      cross join (
        SELECT
          collect_set (CTY_CONTRI_CORP_LEAD_DEPT) as distinct_id_array
        FROM
          ods.ods_bl_yqzx_fin_proj_list
      ) x2
    WHERE
      prj_bsn_tp_cd IN ('GQ')
      and Prj_Stat_Cd_Dsc IN ('已成交', '已归档')
    GROUP BY
      SUBSTR(BSN_REC_DEAL_DT, 1, 7),
      SUBSTR(BSN_REC_DEAL_DT, 1, 4),
      prj_blng_dept_nm
  ),
  deal_data AS (
    SELECT
      SUBSTR(BSN_REC_DEAL_DT, 1, 7) AS month,
      SUBSTR(BSN_REC_DEAL_DT, 1, 4) AS year,
      CASE
        WHEN prj_bsn_tp_cd_dsc='产权转让' THEN '产权转让'
        WHEN prj_bsn_tp_cd_dsc='企业增资' THEN '企业增资'
        ELSE '实物'
      END AS proj_type,
      prj_blng_dept_nm,
      COUNT(distinct prj_no) AS deal_prj_num,
      SUM(deal_amt) AS deal_amt
    FROM
      dws_all_trsfer_info
    WHERE
      prj_bsn_tp_cd IN ('1C', '1D', '1F', 'GQ')
      AND BSN_REC_DEAL_DT is not null
      AND dt='${dmp_day}'
      and exch='北交所'
    GROUP BY
      SUBSTR(BSN_REC_DEAL_DT, 1, 7),
      SUBSTR(BSN_REC_DEAL_DT, 1, 4),
      CASE
        WHEN prj_bsn_tp_cd_dsc='产权转让' THEN '产权转让'
        WHEN prj_bsn_tp_cd_dsc='企业增资' THEN '企业增资'
        ELSE '实物'
      END,
      prj_blng_dept_nm
  ),
  bid_data AS (
    SELECT
      SUBSTR(BSN_REC_DEAL_DT, 1, 7) AS month,
      SUBSTR(BSN_REC_DEAL_DT, 1, 4) AS year,
      CASE
        WHEN prj_bsn_tp_cd_dsc='产权转让' THEN '产权转让'
        WHEN prj_bsn_tp_cd_dsc='企业增资' THEN '企业增资'
        ELSE '实物'
      END AS proj_type,
      prj_blng_dept_nm,
      COUNT(distinct prj_no) AS bid_prj_num
    FROM
      dws_all_trsfer_info
    WHERE
      prj_bsn_tp_cd IN ('GQ')
      AND Prj_Stat_Cd_Dsc IN ('已成交', '已归档')
      AND txn_mth_cd_dsc IN ('网络竞价', '拍卖', '招投标', '其他竞价', '动态报价', '其他竞价(一次报价)')
      and (COALESCE(prj_prt_sown_spvs_org_cd_dsc, '未知') not IN(
        '省级财政部门监管',
        '市级财政部门或金融办监管',
        '省级国资委监管',
        '省级其他部门监管',
        '市级国资委监管',
        '市级其他部门监管'
      ) OR (exch = '北交所' AND prj_prt_sown_spvs_org_cd_dsc IS NULL))
      and prj_no not like 'G0%'
      AND Blng_Org NOT IN(
        SELECT
          CTY_CONTRI_CORP_LEAD_DEPT
        FROM
          ods.ods_bl_yqzx_fin_proj_list
      )
      AND dt='${dmp_day}'
      and prj_blng_dept_nm IN ('央企一部', '央企二部', '央企三部', '央企四部', '央企五部')
      and exch='北交所'
    GROUP BY
      SUBSTR(BSN_REC_DEAL_DT, 1, 7),
      SUBSTR(BSN_REC_DEAL_DT, 1, 4),
      CASE
        WHEN prj_bsn_tp_cd_dsc='产权转让' THEN '产权转让'
        WHEN prj_bsn_tp_cd_dsc='企业增资' THEN '企业增资'
        ELSE '实物'
      END,
      prj_blng_dept_nm
  ),
  valid_bid_data AS (
    SELECT
      SUBSTR(BSN_REC_DEAL_DT, 1, 7) AS month,
      SUBSTR(BSN_REC_DEAL_DT, 1, 4) AS year,
      CASE
        WHEN prj_bsn_tp_cd_dsc='产权转让' THEN '产权转让'
        WHEN prj_bsn_tp_cd_dsc='企业增资' THEN '企业增资'
        ELSE '实物'
      END AS proj_type,
      prj_blng_dept_nm,
      COUNT(distinct prj_no) AS valid_bid_prj_num
    FROM
      dws_all_trsfer_info
    WHERE
      prj_bsn_tp_cd IN ('GQ')
      AND Prj_Stat_Cd_Dsc IN ('已成交', '已归档')
      AND txn_mth_cd_dsc IN ('网络竞价', '拍卖', '招投标', '其他竞价', '动态报价', '其他竞价(一次报价)')
      and (COALESCE(prj_prt_sown_spvs_org_cd_dsc, '未知') not IN(
        '省级财政部门监管',
        '市级财政部门或金融办监管',
        '省级国资委监管',
        '省级其他部门监管',
        '市级国资委监管',
        '市级其他部门监管'
      ) OR (exch = '北交所' AND prj_prt_sown_spvs_org_cd_dsc IS NULL))
      and prj_no not like 'G0%'
      AND Blng_Org NOT IN(
        SELECT
          CTY_CONTRI_CORP_LEAD_DEPT
        FROM
          ods.ods_bl_yqzx_fin_proj_list
      )
      AND deal_amt>tfr_prc
      AND dt='${dmp_day}'
      and prj_blng_dept_nm IN ('央企一部', '央企二部', '央企三部', '央企四部', '央企五部')
      and exch='北交所'
    GROUP BY
      SUBSTR(BSN_REC_DEAL_DT, 1, 7),
      SUBSTR(BSN_REC_DEAL_DT, 1, 4),
      CASE
        WHEN prj_bsn_tp_cd_dsc='产权转让' THEN '产权转让'
        WHEN prj_bsn_tp_cd_dsc='企业增资' THEN '企业增资'
        ELSE '实物'
      END,
      prj_blng_dept_nm
  )
SELECT
  COALESCE(l.month, d.month, b.month) AS month,
  COALESCE(l.year, d.year, b.year) AS year,
  COALESCE(l.proj_type, d.proj_type, b.proj_type) AS proj_type,
  COALESCE(l.prj_blng_dept_nm, d.prj_blng_dept_nm, b.prj_blng_dept_nm) AS prj_blng_dept_nm,
  COALESCE(l.lit_prj_num, 0) AS lit_prj_num,
  COALESCE(l.lit_amt, 0)/10000 AS lit_amt,
  COALESCE(d.deal_prj_num, 0) AS deal_prj_num,
  COALESCE(d.deal_amt, 0)/10000 AS deal_amt,
  COALESCE(b.bid_prj_num, 0) AS bid_num1,
  COALESCE(v.valid_bid_prj_num, 0) AS bid_num2,
  COALESCE(t.trsfer_prj_num, 0) AS seller_num
FROM
  lit_data l FULL OUTER
  JOIN deal_data d ON l.month=d.month
  AND l.year=d.year AND l.prj_blng_dept_nm = d.prj_blng_dept_nm
  AND l.proj_type=d.proj_type FULL OUTER
  JOIN bid_data b ON l.month=b.month
  AND l.year=b.year AND l.prj_blng_dept_nm = b.prj_blng_dept_nm
  AND l.proj_type=b.proj_type FULL OUTER
  JOIN valid_bid_data v ON l.month=v.month
  AND l.year=v.year AND l.prj_blng_dept_nm = v.prj_blng_dept_nm
  AND l.proj_type=v.proj_type full outer
  join trsfer_prj_num t on l.month=t.month
  AND l.year=t.year AND l.prj_blng_dept_nm = t.prj_blng_dept_nm
  AND l.proj_type=t.proj_type