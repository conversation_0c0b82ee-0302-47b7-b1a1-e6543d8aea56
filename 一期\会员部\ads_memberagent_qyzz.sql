with temp_cust as(
    SELECT
			  b.mdlg_usr_wrd,
			  a.agent_no,
			  a.compy_name
			FROM (
			  SELECT
			    *,
			    ROW_NUMBER() OVER (PARTITION BY a.cust_no ORDER BY a.update_time DESC) AS rn
			  FROM ods.ods_bl_agent_info a
			  WHERE a.dt = '${dmp_day}'
			) a
			LEFT JOIN (
			  SELECT * FROM dim.dim_pty_cust_usr_rel
			  WHERE dt='${dmp_day}'
			  AND edw_end_dt='20991231'
			) b ON 'BJHL'||a.cust_no=b.cust_wrd
			WHERE a.rn = 1 --取最新的一条
),
         temp as (
            select  a.prj_id             as proj_no,
                    a.prj_nm             as proj_name,
                    a.prj_stat_cd_dsc    as prj_stat_cd_dsc,
                    a.prj_prt_nm         as seller_fincer_name,
                    d.compy_name    as agent_mem,
                    CASE WHEN e.project_type = '1' THEN sum(c.new_pucpl_cptl)/10000 
                    WHEN e.project_type = '2' THEN e.fnc_tot_amt_w_yuan END as investment_amt, 
                    a.selt_mth_cd_dsc    as preferential_selection_method,
                    b.bsn_rec_deal_dt    as deal_date,
                    a.info_publ_start_dt as info_dclo_begin_dt,
                    a.info_publ_exprt_dt as info_dclo_expire_dt,
                    a.prj_blng_dept_nm  as proj_belong_dept_name,
                    a.prj_prin_nm        as proj_princ_name
                from dwd.dwd_prj_fct a
                        left join dwd.dwd_evt_deal_rec_fct b 
                            on a.bsn_prj_wrd = b.bsn_prj_wrd 
                            and b.dt = '${dmp_day}'
                        left join dwd.dwd_entp_incptl_deal_rec_fct c 
                            on a.bsn_prj_wrd=c.bsn_prj_wrd 
                            and b.bsn_deal_rec_id=cast(c.bsn_deal_rec_id as string) 
                            and c.dt= '${dmp_day}'
                        left join temp_cust d on 'BJHL'||a.txn_svc_mber_id=d.mdlg_usr_wrd
                        left join (
                            SELECT project_code,project_type,fnc_tot_amt_w_yuan 
                                FROM std.std_bjhl_tcgq_zzzsgpxm_d 
                                WHERE dt = '${dmp_day}') e
                        ON a.prj_id = e.project_code
                where a.dt = '${dmp_day}'
                and a.prj_bsn_tp_cd = '1C'
                and b.bsn_rec_deal_dt is not null
                and d.agent_no is not null
                GROUP BY a.prj_id,a.prj_nm,a.prj_stat_cd_dsc,a.prj_prt_nm,d.compy_name,e.project_type,e.fnc_tot_amt_w_yuan,
                         a.selt_mth_cd_dsc,b.bsn_rec_deal_dt,a.info_publ_start_dt,a.info_publ_exprt_dt,
                         a.prj_blng_dept_nm,a.prj_prin_nm
         )
select proj_no,proj_name,prj_stat_cd_dsc,seller_fincer_name,agent_mem,preferential_selection_method,deal_date,
         info_dclo_begin_dt,info_dclo_expire_dt,proj_belong_dept_name,proj_princ_name,investment_amt
         from temp