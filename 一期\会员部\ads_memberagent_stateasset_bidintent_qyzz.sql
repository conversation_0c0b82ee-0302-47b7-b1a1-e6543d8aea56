with temp_buyer as (SELECT  distinct bsn_prj_wrd
                                  ,bsn_buyer_id
                                  ,txn_svc_mber_id  -- 融资方经纪会员ID
                                  ,txn_svc_mber_nm
                                  ,itrsfee_repst_nm
                                  ,fnl_qua_cfrm_rslt_cd_dsc -- 获得资格确认结果描述
                                  ,fnl_qua_cfrm_rslt_cd -- 获得资格确认结果代码
                                  ,case when is_fnl_trsfee = '1' then '是' else '否' end  as is_fnl_trsfee --是否最终受让方
                            FROM dwd.dwd_ittn_buyer_fct
                            WHERE dt = '${dmp_day}'
                            -- AND mrgn_stat_cd = '1'
                      )
select 
    a.prj_id as proj_no, -- 项目编号
    a.prj_nm as proj_name, -- 项目名称
    a.prj_stat_cd_dsc as prj_stat_cd_dsc, -- 项目状态描述
    a.prj_prt_nm as seller_fincer_name, -- 转让方名称
    a.txn_svc_mber_nm as agent_mem, -- 转让方经纪会员名称
    e.itrsfee_repst_nm as buyer_name, -- 受让方代表名称
    e.txn_svc_mber_nm as transferee_name, -- 受让方经纪会员名称
    case when e.bsn_buyer_id = b.bsn_buyer_id then '是' else '否' end as is_transferee, -- 是否为受让方
    0 as trans_fee_amt, -- 转让方服务费总金额（元）
    0 as transferee_fee_amt, -- 受让方服务费总金额（元）
    0 as fee_amt, -- 总服务费金额（元）
    0 as cbex_fee_amt, -- 北交所收入（元）
    a.tfr_prc / 10000 as sell_price, -- 转让底价（万元）
    b.deal_amt / 10000 as deal_value, -- 成交金额（万元）
    a.txn_mth_cd_dsc as trans_type, -- 交易方式描述
    b.actl_txn_mth_cd_dsc as deal_way_name, -- 实际交易方式描述  
    b.bsn_rec_deal_dt as deal_date, -- 成交日期
    a.selt_mth_cd_dsc as preferential_selection_method, -- 择优选择方式描述
    a.info_publ_start_dt as info_dclo_begin_dt, -- 信息披露开始日期
    a.info_publ_exprt_dt as info_dclo_expire_dt, -- 信息披露结束日期
    a.prj_blng_dept_nm as proj_belong_dept_name, -- 项目所属部门名称
    a.prj_prin_nm as proj_princ_name, -- 项目负责人名称
    case when e.bsn_buyer_id=b.bsn_buyer_id then '是' else '否' end as is_fnl_trsfee, -- 是否最终受让方
    e.fnl_qua_cfrm_rslt_cd_dsc as fnl_qua_cfrm_rslt_cd_dsc -- 获得资格确认结果描述
from dwd.dwd_prj_fct a
left join temp_buyer e on a.bsn_prj_wrd = e.bsn_prj_wrd
left join dwd.dwd_evt_deal_rec_fct b on a.bsn_prj_wrd = b.bsn_prj_wrd and b.bsn_buyer_id = e.bsn_buyer_id and b.dt = '${dmp_day}'
-- left join dwd.dwd_prpt_rec_fct d on a.bsn_prj_wrd = d.bsn_prj_wrd and d.dt = '${dmp_day}'
where a.dt = '${dmp_day}' 
  and a.prj_bsn_tp_cd = '1C' 
  and b.bsn_rec_deal_dt is not null -- 成交日期不为空
  and e.txn_svc_mber_id is not null -- 融资方/受让方经纪会员ID不为空
  and e.fnl_qua_cfrm_rslt_cd = '1' -- 获得资格确认结果代码为1