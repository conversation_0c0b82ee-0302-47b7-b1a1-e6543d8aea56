from impala.dbapi import connect
import json

from openpyxl.workbook import Workbook

# 配置连接参数
host = '*************'
port = 10000  # Hive Server 2 默认端口
user = 'root'
database = 'ods'
auth_mechanism='PLAIN'

# 建立连接
conn = connect(host=host, port=port, user=user, database=database, auth_mechanism=auth_mechanism)

# 定义数据库和表名的替换规则
source_db = 'ods'
target_db = 'odm'
table_prefix = 'o_'
source_prefix = 'ods_'


# 获取ods数据库下的所有表名
def get_tables_in_ods():
    cursor = conn.cursor()
    cursor.execute(f"SHOW TABLES IN {source_db}")
    tables = [row[0] for row in cursor.fetchall()]
    cursor.close()
    return tables


# 获取表的字段名、数据类型
def get_columns_info(table_name):
    cursor = conn.cursor()
    try:
        cursor.execute(f"DESCRIBE {source_db}.{table_name}")
        columns_info = []
        for row in cursor.fetchall():
            if len(row) < 2 or  row[0].strip() =="# col_name" or row[0].strip() =="describe":
                continue
            column_name = row[0].strip() if row[0] is not None else ''
            data_type = row[1].strip() if row[1] is not None else ''
            data_comment = row[2].strip() if row[2] is not None else ''
            if column_name.lower() != 'dt' and column_name != '':  # 排除分区字段 dt
                columns_info.append((column_name, data_type,data_comment))
        cursor.close()
        return columns_info
    except Exception as e:
        print(f"Error fetching columns for table {source_db}.{table_name}: {str(e)}")
        cursor.close()
        return []




# 主程序逻辑
if __name__ == "__main__":
    wb = Workbook()
    ws = wb.active
    ws.title = "数据字段和注释"
    # 写入表头
    ws.append(["数据表", "字段名", "字段类型","字段注释"])
    try:
        tables = get_tables_in_ods()
        for table in tables:
            columns_info=get_columns_info(table)
            for column_name, data_type,data_comment in columns_info:
                ws.append([table,column_name, data_type,data_comment])


    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        conn.close()
    wb.save("数据表导出.xlsx")