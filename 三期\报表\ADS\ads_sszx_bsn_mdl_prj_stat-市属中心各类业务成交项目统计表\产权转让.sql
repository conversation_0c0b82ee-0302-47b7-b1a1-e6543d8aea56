WITH data_all AS (
    SELECT
        DISTINCT
         A.project_no                            AS proj_no -- 项目编号 
        ,A.project_name                          AS proj_name -- 项目名称 
        ,'产权转让'                               AS proj_type -- 业务类型 
        ,substr(A.transaction_date,0,7)          AS deal_date -- 成交日期 
        ,A.proj_type                             AS proj_category -- 项目类别(公开/非公开)
        ,A.project_status                        AS proj_status -- 项目状态
        ,A.transaction_amount                    AS deal_value -- 成交价格(万元)
    FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
    LEFT JOIN (
        SELECT *
        FROM (
            SELECT *,
                ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
            FROM std.std_bl_eqty_prj_data_d
            WHERE dt = '${dmp_day}'
        ) t 
        WHERE rn = 1
    ) B -- 产权转让项目数据纠正表-补录 
    ON A.project_no = B.proj_no AND A.dt = B.dt
    WHERE A.dt = '${dmp_day}'
    AND COALESCE(B.state_owned_asset_regulator, A.transferor_regulatory) in ('省级国资委监管','省级其他部门监管','省级财政部门监管','市级国资委监管','市级其他部门监管','市级财政部门或金融办监管')
    AND A.transferor_area = '北京市'
    AND A.department REGEXP '市属|行政司法'
    AND A.transaction_date IS NOT NULL
),
data_all_ss AS(
        SELECT
         DISTINCT
         A.project_no                            AS proj_no -- 项目编号 
        ,A.project_name                          AS proj_name -- 项目名称 
        ,'产权转让'                               AS proj_type -- 业务类型 
        ,substr(A.transaction_date,0,7)          AS deal_date -- 成交日期 
        ,A.proj_type                             AS proj_category -- 项目类别(公开/非公开)
        ,A.project_status                        AS proj_status -- 项目状态
        ,A.transaction_amount                    AS deal_value -- 成交价格(万元)
    FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
    LEFT JOIN (
        SELECT *
        FROM (
            SELECT *,
                ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
            FROM std.std_bl_eqty_prj_data_d
            WHERE dt = '${dmp_day}'
        ) t 
        WHERE rn = 1
    ) B -- 产权转让项目数据纠正表-补录 
    ON A.project_no = B.proj_no AND A.dt = B.dt
    WHERE A.dt = '${dmp_day}'
    AND COALESCE(B.state_owned_asset_regulator, A.transferor_regulatory) ='省级国资委监管'
    AND A.transferor_area = '北京市'
    AND A.department REGEXP '市属'
    AND A.transaction_date IS NOT NULL
)
SELECT  proj_type       AS proj_type -- 业务类型 
       ,COUNT(DISTINCT proj_no)  AS deal_num -- 成交数量 
       ,SUM(deal_value) AS deal_value -- 成交金额 
       ,deal_date       AS deal_date -- 成交日期 
       ,proj_category   AS proj_category -- 项目类别 
       ,proj_status     AS proj_status -- 项目状态 
       ,'北京市国企'        AS cagetory -- 类别 
FROM data_all
GROUP BY proj_type,deal_date,proj_category,proj_status
UNION ALL 
SELECT  proj_type       AS proj_type -- 业务类型 
       ,COUNT(DISTINCT proj_no)  AS deal_num -- 成交数量 
       ,SUM(deal_value) AS deal_value -- 成交金额 
       ,deal_date       AS deal_date -- 成交日期 
       ,proj_category   AS proj_category -- 项目类别 
       ,proj_status     AS proj_status -- 项目状态 
       ,'市属企业'       AS cagetory -- 类别 
FROM data_all_ss
GROUP BY proj_type,deal_date,proj_category,proj_status
