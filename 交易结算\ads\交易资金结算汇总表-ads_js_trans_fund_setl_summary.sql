SELECT  regexp_replace(k.pay_success_date,'^(\\d{4})(\\d{2})(\\d{2})$','$1-$2-$3') AS trans_date --交易日期 
       ,k.cptl_lo                                                                  AS cptl_lo --资金位置 
       ,k.bsn_type                                                                 AS proj_type --业务类型 
       ,''                                                                         AS opening_balance --期初余额（万元） 
       ,SUM(CASE WHEN k.crj_flag = '入金' THEN amt ELSE 0 END)                     AS deposit_amount --入金金额 
       ,SUM(CASE WHEN k.crj_flag = '出金' THEN amt ELSE 0 END)                     AS withdrawal_amount --出金金额 
       ,''                                                                         AS closing_balance --期末余额（万元） 
FROM
(
SELECT 
A.pay_success_date, --支付成功日期
A.cptl_lo, --资金位置
A.bsn_type, --业务类型
A.crj_flag, --出入金标志
A.amt
FROM dws.dws_trans_setl_info A
WHERE A.DT = ${dmp_day} 
AND A.amt != 0
AND coalesce(A.setl_type,'-') != '场外结算'
-- AND `cptl_type_lrgcls` != '退票' 
--AND `cptl_type_lrgcls` != '利息相关' 
-- and cptl_type_smlcls != '服务费'
--2.当订单大类=服务费和分佣时，只取项目类型为珍品、房屋出租、小宗实物和机动车的记录
--9.项目类型为诉讼，订单大类=服务费，入金，订单小类为服务费交纳，订单类型为受让方服务费的订单类型纳入计算
--10.当项目类型为诉讼，订单大类=分佣，出金，订单小类为服务费划出的订单类型纳入计算
AND (
cptl_type_lrgcls NOT IN ('服务费','分佣')
OR CONCAT(cptl_type_lrgcls,bsn_type) IN ('服务费珍品','服务费房屋出租','服务费小宗实物','服务费机动车',
'分佣珍品','分佣房屋出租','分佣小宗实物','分佣机动车')
OR CONCAT(cptl_type_lrgcls,bsn_type,ast_type,cptl_type_smlcls) IN ('服务费诉讼资产受让方服务费服务费交纳')
OR CONCAT(cptl_type_lrgcls,bsn_type,ast_type,cptl_type_smlcls) IN ('分佣诉讼资产中台出金服务费划出')
  
)
--3.当订单小类=保证金转价款时，所有记录都不统计  
--5.当订单小类=保证金抵扣服务费（服务费入金）时，均不统计
--7.当订单小类=价款抵扣服务费（服务费入金）时，均不统计
AND cptl_type_smlcls NOT IN  ('保证金抵扣服务费（服务费入金）','价款抵扣服务费（服务费入金）')
AND substr(cptl_type_smlcls,1,6) != '保证金转价款'
--4.当订单小类=保证金抵扣服务费（保证金出金）时，仅取项目类型为企业增资的记录，房屋出租的不统计
AND (
	cptl_type_smlcls != '保证金抵扣服务费（保证金出金）'
	OR concat(cptl_type_smlcls,bsn_type) = '保证金抵扣服务费（保证金出金）企业增资'
)
--6.当订单小类=价款抵扣服务费（价款出金）时，仅取项目类型为产权转让和大宗实物的记录，房屋出租的不统计  
AND (
	cptl_type_smlcls != '价款抵扣服务费（价款出金）'
	OR concat(cptl_type_smlcls,bsn_type) in ('价款抵扣服务费（价款出金）产权转让','价款抵扣服务费（价款出金）大宗实物')
)
UNION ALL
-- 线下出金数据
SELECT  
        offline_withdr_time                     AS trans_date --线下出金时间/支付成功日期
        ,'北交所'                                AS cptl_lo --资金位置
        ,CASE project_type  
	    WHEN 'OA'  THEN '珍品'
		WHEN '1B'  THEN '机动车'
		WHEN '1C'  THEN '企业增资'
		WHEN '1D'  THEN '大宗实物'
		WHEN '1E'  THEN '诉讼资产'
		WHEN '1F'  THEN '小宗实物'
		WHEN '1G'  THEN '房屋出租'
		WHEN '2C'  THEN '企业融资'
		WHEN 'FC'  THEN '房产'
		WHEN 'FP'  THEN '司法拍卖'
		WHEN 'FY'  THEN '诉讼资产-法院'
		WHEN 'GQ'  THEN '产权转让'
		WHEN 'JP'  THEN '京牌小客车'
		WHEN 'PC'  THEN '诉讼资产-破产'
		WHEN 'QT'  THEN '诉讼资产-其他'
		WHEN 'Z1'  THEN '招商'
		WHEN 'ZP'  THEN '房屋租赁'
		WHEN 'ZQ'  THEN '债权转让'
		WHEN 'TJ'  THEN '项目推介'
		WHEN 'TZ'  THEN '投资意向'
		WHEN 'TY'  THEN '体育'
		WHEN 'H1'  THEN '混改意向'
	     ELSE NULL
	     END            			 			 AS proj_type --业务类型 
        ,'出金'                                   AS crj_flag --出入金标志
        ,offline_withdr_amt_yuan                 AS amt --线下出金金额（元） 
FROM std.std_bjhl_tbid_lsxxcjjl_d 
WHERE dt = '${dmp_day}' 
AND offline_withdr_amt_yuan != 0
) k
GROUP BY regexp_replace(pay_success_date, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3'),cptl_lo,bsn_type