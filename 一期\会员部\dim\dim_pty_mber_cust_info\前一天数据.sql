SELECT	
		'bjhl'					AS bsn_src					   --业务来源，来源系统编码
		,'pcxt.market_project'	AS  edw_dt_src_tbl				--ODS数据来源主表名
		,'${dmp_2_day}' 		AS EDW_DATA_DT						--EDW数据日期
		,DATE_FORMAT(current_timestamp(),'yyyy-MM-dd') AS EDW_STM_DT --EDW系统日期
		,'BOT'||T1.project_code			AS MBER_WRD				--1.会员关键字
		,NULL                           AS BLNG_ORG_WRD         --2.所属机构关键字
		,NULL                           AS MBER_NO              --3.会员编号
		,NULL                           AS CUST_NO              --4.客户编号
		,NULL                           AS ACC_BANK_CD          --5.开户银行代码
		,NULL                           AS ACC_BANK_CD_DSC      --6.开户银行代码描述
		,NULL                           AS SUBBR_NM             --7.支行名称
		,NULL                           AS ACC_BANK_INT_NO      --8.开户行联行号
		,NULL                           AS BANK_CARD_NO         --9.银行卡号
		,NULL                           AS LOC_CD               --10.地域代码
		,NULL                           AS LOC_CD_DSC           --11.地域代码描述
		,NULL                           AS MBER_CGY_CD          --12.会员类别代码
		,NULL                           AS MBER_CGY_CD_DSC      --13.会员类别代码描述
		,1                            AS MBER_TP_CD             --14.会员类型代码
		,'交易服务会员'                 AS MBER_TP_CD_DSC       --15.会员类型代码描述
		,T1.committee_mbsh_org_nm                   AS MBER_NM              --16.会员名称
		,NULL                           AS ORIG_SUB_RL_ID       --17.原隶属角色ID
		,NULL                           AS SUB_RL_ID            --18.隶属角色ID
		,NULL                           AS REL_INSTID           --19.关联INSTID
		,NULL                           AS MEBS_DT              --20.入会日期
		,NULL                           AS TXPR_TP_CD           --21.纳税人类型代码
		,NULL                           AS TXPR_TP_CD_DSC       --22.纳税人类型代码描述
		,NULL                           AS VAT_RATE             --23.增值税率
		,NULL                           AS TMVD                 --24.有效期
		,NULL                           AS MBER_STAT_CD         --25.会员状态代码
		,NULL                           AS MBER_STAT_CD_DSC     --26.会员状态代码描述
		,NULL                           AS MEBS_LIST_CD         --27.入会清单代码
		,NULL                           AS MEBS_LIST_CD_DSC     --28.入会清单代码描述
		,NULL                           AS IS_KEY_MBER          --29.是否重点会员
		,T1.committee_mbsh_org_ctc_psn				AS CTACTS_NM            --30.联系人名称
		,T1.committee_mbsh_contact_tel                AS CTC_TEL              --31.联系电话
		,NULL                			AS CTC_PHONE_NO         --32.联系手机号
    ,NULL                			AS prof_srv_mem_type_dsc  -- 专业服务会员类别
FROM std.std_bot_otherinfo_d T1
WHERE T1.DT = ${dmp_2_day}
union all
SELECT	
		'bjhl'										    --业务来源，来源系统编码
		,'bid.tbid_fwhyfsxx'						   	--ODS数据来源主表名
		,'${dmp_2_day}' 								--EDW数据日期
		,DATE_FORMAT(current_timestamp(),'yyyy-MM-dd')  --EDW系统日期
		,'BJHL'||T1.cust_no                                 --1.会员关键字
		,'BJHL'||T1.blng_org                               --2.所属机构关键字
		,T1.id                                        --3.会员编号
		,T1.cust_no                                         --4.客户编号
		,T1.opn_acc_bnk --5.开户银行代码
		,T1.opn_acc_bnk_dsc                                       --6.开户银行代码描述
		,T1.subbr_nm                                        --7.支行名称
		,T1.dep_bnk_bnk_cd                                       --8.开户行联行号
		,T1.bnk_card_no                                        --9.银行卡号
		,T1.loc --10.地域代码
		,T1.loc_dsc                                        --11.地域代码描述
		,T1.mbsh_cgy  --12.会员类别代码
		,T1.mbsh_cgy_dsc							            --13.会员类别代码描述
		,T1.mbsh_tp   --14.会员类型代码
		,T1.mbsh_tp_dsc --15.会员类型代码描述
		,T1.mbsh_nm                                        --16.会员名称
		,T1.orig_sub_rl                                      --17.原隶属角色ID
		,T1.sub_rl                                       --18.隶属角色ID
		,T1.rltv_instid                                    --19.关联INSTID
		,T1.enroll_dt                                        --20.入会日期
		,T1.tax_pymt_psn_tp  --21.纳税人类型代码
		,T1.tax_pymt_psn_tp_dsc --22.纳税人类型代码描述
		,T1.addval_tax_rate										--23.增值税率
		,T1.avl_dt                                         --24.有效期
		,T1.mbsh_sts  --25.会员状态代码
		,T1.mbsh_sts_dsc		--26.会员状态代码描述
		,T1.enroll_list								       	--27.入会清单代码
		,T1.enroll_list_dsc                                        --28.入会清单代码描述
		,T1.key_mbsh                                        --29.是否重点会员
		,T6.optr_nm                                       --30.联系人名称
		,T8.tel                                       --31.联系电话
		,T8.mbl_ph                                      --32.联系手机号
    ,t1.prof_srv_mem_type_dsc                       -- 专业服务会员类别
FROM std.std_bjhl_tbid_fwhyfsxx_d T1	--任务接收方信息
LEFT JOIN (SELECT optr_nm,cust_no
	   FROM std.std_bjhl_tjgkhxx_d	   --机构客户信息表
	   WHERE DT = ${dmp_2_day} )T6
	   ON T1.cust_no = T6.cust_no
LEFT JOIN (SELECT cust_no,rltv
		  FROM std.std_bjhl_tbid_fwhydy_d --服务会员对应关系
		 WHERE DT = ${dmp_2_day}
		  AND is_pan = 1
		  AND cust_tp = 1) T7
		  ON T1.cust_no = T7.cust_no
LEFT JOIN (SELECT ID,tel,mbl_ph
			FROM std.std_bjhl_tuser_d 	--用户管理
			WHERE DT = ${dmp_2_day}) T8
			ON T7.rltv = T8.ID
WHERE T1.DT = ${dmp_2_day}