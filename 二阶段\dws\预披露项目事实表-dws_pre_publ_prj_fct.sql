-- Active: 1725412082987@@10.254.99.145@10000@dws
SELECT
  a.pre_publ_prj_id,  -- 预披露项目ID
  a.pre_publ_prj_wrd,  -- 预披露项目关键字
  a.prj_wrd,  -- 项目关键字
  a.prj_id,  -- 项目编号
  a.prj_nm,  -- 项目名称
  a.prj_stat_cd,  -- 项目状态代码
  a.prj_stat_cd_dsc,  -- 项目状态代码描述
  a.mber_org_id,  -- 会员机构ID
  a.mber_org_nm,  -- 会员机构名称
  a.txn_svc_mber_id,  -- 交易服务会员ID
  a.txn_svc_mber_nm,  -- 交易服务会员名称
  a.mber_ctacts_nm,  -- 会员联系人名称
  a.mber_ctc_tel,  -- 会员联系电话
  a.catr_id,  -- 创建人ID
  a.catr_nm,  -- 创建人名称
  a.crt_tm,  -- 创建时间
  a.catr_blng_dept_id,  -- 创建人所属部门ID
  a.catr_blng_dept_nm,  -- 创建人所属部门名称
  a.prj_blng_dept_id,  -- 项目所属部门ID
  a.prj_blng_dept_nm,  -- 项目所属部门名称
  a.prj_prin_id,  -- 项目负责人ID
  a.prj_prin_nm,  -- 项目负责人名称
  a.dept_prin_id,  -- 部门负责人ID
  a.dept_prin_nm,  -- 部门负责人名称
  a.reviser_id,  -- 修改人ID
  a.reviser_nm,  -- 修改人名称
  a.mod_tm,  -- 修改时间
  a.prj_end_dt,  -- 项目终结日期
  a.eff_tm,  -- 生效时间
  a.oprtr_id,  -- 经办人ID
  a.oprtr_nm,  -- 经办人名称
  a.prj_info,  -- 项目信息
  a.prj_info_atch,  -- 项目信息附件
  a.lit_tm,  -- 挂牌时间
  a.wdrl_tm,  -- 撤牌时间
  a.handl_bus_dep_id,  -- 经办营业部ID
  a.handl_bus_dep_nm,  -- 经办营业部名称
  a.clm_psn_id,  -- 认领人ID
  a.clm_psn_nm,  -- 认领人名称
  a.clm_dept_id,  -- 认领部门ID
  a.clm_dept_nm,  -- 认领部门名称
  a.clm_cetr_id,  -- 认领中心ID
  a.clm_cetr_nm,  -- 认领中心名称
  a.clm_tm,  -- 认领时间
  a.prj_clm_cetr_id,  -- 项目认领中心ID
  a.prj_clm_cetr_nm,  -- 项目认领中心名称
  a.prj_clm_dept_id,  -- 项目认领部门ID
  a.prj_clm_dept_nm,  -- 项目认领部门名称
  a.cetr_dictor_id,  -- 中心主任ID
  a.cetr_dictor_nm,  -- 中心主任名称
  a.notc_mth_cd,  -- 公告方式代码
  a.notc_mth_cd_dsc,  -- 公告方式代码描述
  a.pre_publ_anct_prod_opt_cd,  -- 预披露公告期选项代码
  a.pre_publ_anct_prod_opt_cd_dsc,  -- 预披露公告期选项代码描述
  a.pre_publ_start_dt,  -- 预披露起始日期
  a.pre_puep_dt,  -- 预披露期满日期
  a.pre_pupd_wkdy,  -- 预披露期（工作日）
  a.pre_publ_anct_prod_apntmt_wkdy,  -- 预披露公告期约定工作日
  a.pre_publ_ntep_opt_cd,  -- 预披露公告期满选项代码
  a.pre_publ_ntep_opt_cd_dsc,  -- 预披露公告期满选项代码描述
  a.pre_publ_ntep_expi_dt,  -- 预披露公告期满截止日期
  a.actl_publ_end_tm,  -- 实际披露结束时间
  a.trsfee_qua_cond,  -- 受让方资格条件
  a.trsfee_qua_cond_atch,  -- 受让方资格条件附件
  a.othr_publ_itm,  -- 其他披露事项
  a.othr_publ_itm_atch,  -- 其他披露事项附件
  a.prj_ctacts_nm,  -- 项目联系人名称
  a.prj_ctacts_tel,  -- 项目联系人电话
  a.prj_ctacts_nm_ipubl,  -- 项目联系人名称（外网披露）
  a.prj_ctacts_tel_ipubl,  -- 项目联系人电话（外网披露）
  a.dept_prin_nm_ipubl,  -- 部门负责人名称（外网披露）
  a.dept_prin_ctc_tel_ipubl,  -- 部门负责人联系电话（外网披露）
  a.sms_ntc_rcpt_nm,  -- 短信通知接收人姓名
  a.sms_ntc_rcpt_phone_no,  -- 短信通知接收人手机号
  a.inr_pcds_sttn_cd,  -- 内部决策情况代码
  a.inr_pcds_sttn_cd_dsc,  -- 内部决策情况代码描述
  a.inr_pcds_sttn_othr_cmnt,  -- 内部决策情况其他说明
  a.tfr_prc,  -- 转让底价
  a.is_need_eswb,  -- 是否需要电签
  a.is_prj_prt_einfo_ipubl,  -- 是否项目方基本信息在外网披露
  a.is_ipubl_mber_info,  -- 是否在外网显示会员信息
  b.industry_tp,  -- 所属行业类型
  b.industry_tp_dsc,  -- 所属行业类型描述
  b.prov as obj_prov,  -- 项目所属省
  b.prov_dsc as obj_prov_dsc,  -- 项目所属省描述
  b.intnd_new_cptl_mod,  --拟新增资本方式
  b.intnd_rs_cptl_tot_amt_min_val, --拟募集资金总额最小值
  b.intnd_rs_cptl_tot_amt_max_val, --拟募集资金总额最大值
  b.intnd_rs_cptl_tot_amt  --拟募集资金总额
FROM dwd.dwd_pre_publ_prj_fct a
LEFT JOIN 
(
	SELECT 
	project_code,
	project_name,
	max(intnd_new_cptl_mod) as intnd_new_cptl_mod,  --拟新增资本方式
  max(intnd_rs_cptl_tot_amt_min_val) as intnd_rs_cptl_tot_amt_min_val, --拟募集资金总额最小值
  max(intnd_rs_cptl_tot_amt_max_val) as intnd_rs_cptl_tot_amt_max_val, --拟募集资金总额最大值
  max(intnd_rs_cptl_tot_amt) as intnd_rs_cptl_tot_amt,  --拟募集资金总额
	max(industry_tp) as industry_tp,
	max(industry_tp_dsc) as industry_tp_dsc,
	max(prov) as prov,
	max(prov_dsc) as prov_dsc
	FROM (
	SELECT
		project_code,
		project_name,
		industry_tp,
		industry_tp_dsc,
		prov,
		b.xzqymc AS prov_dsc,
    NULL AS intnd_new_cptl_mod,  --拟新增资本方式
    NULL AS intnd_rs_cptl_tot_amt_min_val, --拟募集资金总额最小值
    NULL AS intnd_rs_cptl_tot_amt_max_val, --拟募集资金总额最大值
    NULL AS intnd_rs_cptl_tot_amt  --拟募集资金总额
	FROM
		std.std_bjhl_tcqzr_cqzrxxypl_d a
		--产权转让信息预披露
	LEFT JOIN (SELECT xzqydm  ,-- 代码 
		              xzqymc  -- 码值
		       FROM ods.ods_bjhl_txzqydm  -- 行政区域代码表
		   	   WHERE dt='${dmp_day}' 
		         ) b 
		   ON a.prov=b.xzqydm
	WHERE a.dt= '${dmp_day}'
	UNION ALL 
	SELECT
		project_code,
		project_name,
		fincer_industry_tp AS industry_tp,
		fincer_industry_tp_dsc AS industry_tp_dsc,
		fincer_zone_prov AS prov,
		b.xzqymc AS prov_dsc,
    a.intnd_new_cptl_mod,  --拟新增资本方式
    a.intnd_rs_cptl_tot_amt_min_val, --拟募集资金总额最小值
    a.intnd_rs_cptl_tot_amt_max_val, --拟募集资金总额最大值
    a.intnd_rs_cptl_tot_amt  --拟募集资金总额
	FROM
		std.std_bjhl_tcgq_zzygpxm_d a
		--增资预批露项目
	LEFT JOIN (SELECT xzqydm  ,-- 代码 
		              xzqymc  -- 码值
		       FROM ods.ods_bjhl_txzqydm  -- 行政区域代码表
		   	   WHERE dt='${dmp_day}' 
		         ) b 
		   ON a.fincer_zone_prov=b.xzqydm
	WHERE  project_code IS NOT NULL
	AND a.dt= '${dmp_day}'
	) T1
	WHERE T1.project_code IS NOT NULL AND project_code != '0'
  GROUP BY project_code,project_name
) b 
ON a.prj_id = b.project_code
WHERE a.dt = '${dmp_day}'