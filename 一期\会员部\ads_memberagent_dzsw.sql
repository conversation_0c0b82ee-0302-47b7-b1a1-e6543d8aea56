with temp_cust as(
  SELECT
        b.mdlg_usr_wrd,
        a.agent_no,
        a.compy_name
        FROM (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY a.cust_no ORDER BY a.update_time DESC) AS rn
        FROM ods.ods_bl_agent_info a
        WHERE a.dt = '${dmp_day}'
        ) a
        LEFT JOIN (
        SELECT * FROM dim.dim_pty_cust_usr_rel
        WHERE dt='${dmp_day}'
        AND edw_end_dt='20991231'
        ) b ON 'BJHL'||a.cust_no=b.cust_wrd
        WHERE a.rn = 1 --取最新的一条
),
     sfrxx as (
select
    bsn_prj_wrd,
    max(case when sfzzsrf='是' then srfmc else null end) as srfmc,
    max(case when sfzzsrf='是' then srfhy else null end) as srfhy,
    max(case when sfzzsrf='否' then srfmc else null end) as yxsrfmc,
    max(case when sfzzsrf='否' then srfhy else null end) as yxsrfhy
  from (
select bsn_prj_wrd,
       nullif(CONCAT_ws('\n',COLLECT_list(itrsfee_repst_nm)),'') srfmc,
       nullif(CONCAT_ws('\n',COLLECT_list(txn_svc_mber_nm)),'')  srfhy,
       sfzzsrf
from(
select distinct t1.bsn_prj_wrd,
                      t1.txn_svc_mber_id,
                      t1.bsn_buyer_id,
                      t1.txn_svc_mber_nm,
                      t1.itrsfee_repst_nm,
                      (case
                           when t1.bsn_buyer_id = t2.bsn_buyer_id then '是'
                           else '否'
                          end) sfzzsrf
      from dwd.dwd_ittn_buyer_fct t1
               left join dwd.dwd_evt_deal_rec_fct t2 on
          t1.bsn_prj_wrd = t2.bsn_prj_wrd
              and t1.bsn_buyer_id = t2.bsn_buyer_id
              and t2.dt = '${dmp_day}'
      where t1.dt = '${dmp_day}')
group by bsn_prj_wrd,sfzzsrf)
group by bsn_prj_wrd)
select
  a.prj_id as proj_no,
  a.prj_nm as proj_name,
  a.prj_stat_cd_dsc as prj_stat_cd_dsc,
  a.prj_prt_nm as seller_fincer_name,
  x.compy_name as agent_mem,
  c.itrsfee_repst_nm as buyer_name,
  c.mber_org_nm as transferee_name,
  a.tfr_prc/10000 as sell_price,
  b.deal_amt/10000 as deal_value,
  a.bid_mth_cd_dsc as trans_type,
  b.actl_txn_mth_cd_dsc as deal_way_name,
  b.bsn_rec_deal_dt as deal_date,
  a.info_publ_start_dt as info_dclo_begin_dt,
  a.info_publ_exprt_dt as info_dclo_expire_dt,
  a.prj_blng_dept_nm as proj_belong_dept_name,
  a.prj_prin_nm as proj_princ_name,
  d.ast_cgy_dsc as asset_type,
  t1.yxsrfhy as buyer_name_y
from
  dwd.dwd_prj_fct a
  left join dwd.dwd_evt_deal_rec_fct b on a.bsn_prj_wrd=b.bsn_prj_wrd
  and b.dt='${dmp_day}'
  left join dwd.dwd_ittn_buyer_fct c on b.bsn_buyer_id=c.bsn_buyer_id
  and a.bsn_prj_wrd=c.bsn_prj_wrd
  and c.dt='${dmp_day}'
  left join dwd.dwd_bulk_obj_prj_fct d on a.bsn_prj_wrd=d.bsn_prj_wrd and d.dt='${dmp_day}'
  left join temp_cust x on 'BJHL'||a.txn_svc_mber_id=x.mdlg_usr_wrd
  left join sfrxx t1 on a.bsn_prj_wrd=t1.bsn_prj_wrd
where
  a.dt='${dmp_day}'
  and a.prj_bsn_tp_cd='1D'
  and b.bsn_rec_deal_dt is not null
  and x.agent_no is not null