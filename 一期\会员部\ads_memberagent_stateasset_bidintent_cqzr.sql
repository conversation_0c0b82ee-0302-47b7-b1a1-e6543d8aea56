with temp_buyer as (SELECT  distinct bsn_prj_wrd
                                  ,bsn_buyer_id
                                  ,txn_svc_mber_id
                                  ,txn_svc_mber_nm
                                  ,itrsfee_repst_nm
                                  ,fnl_qua_cfrm_rslt_cd_dsc
                                  ,fnl_qua_cfrm_rslt_cd
                                  ,case when is_fnl_trsfee = '1' then '是' else '否' end  as is_fnl_trsfee --是否最终受让方
                            FROM dwd.dwd_ittn_buyer_fct
                            WHERE dt = '${dmp_day}'
                            -- AND mrgn_stat_cd = '1'
                      )
select
    a.prj_id as proj_no,
    a.prj_nm as proj_name,
    a.prj_stat_cd_dsc as prj_stat_cd_dsc,
    a.prj_prt_nm as seller_fincer_name,
    a.txn_svc_mber_nm as agent_mem,
    e.itrsfee_repst_nm as buyer_name,
    e.txn_svc_mber_nm as transferee_name, 
    case when e.bsn_buyer_id=b.bsn_buyer_id then '是' else '否' end is_transferee,
    d.prj_prt_svfee_tot_amt as trans_fee_amt,
    d.buyer_svfee_tot_amt as transferee_fee_amt,
    d.prj_prt_svfee_tot_amt+d.buyer_svfee_tot_amt as fee_amt,
    d.cbex_income as cbex_fee_amt,
    a.tfr_prc/10000 as sell_price,
    b.deal_amt/10000 as deal_value,
    a.txn_mth_cd_dsc as trans_type,
    b.actl_txn_mth_cd_dsc as deal_way_name,
    b.bsn_rec_deal_dt as deal_date,
    a.info_publ_start_dt as info_dclo_begin_dt,
    a.info_publ_exprt_dt as info_dclo_expire_dt,
    a.prj_blng_dept_nm as proj_belong_dept_name,
    a.prj_prin_nm as proj_princ_name,
    case when e.bsn_buyer_id=b.bsn_buyer_id then '是' else '否' end as is_fnl_trsfee -- 是否最终受让方
from dwd.dwd_prj_fct a
left join temp_buyer e on a.bsn_prj_wrd=e.bsn_prj_wrd
left join dwd.dwd_evt_deal_rec_fct b on a.bsn_prj_wrd=b.bsn_prj_wrd and b.bsn_buyer_id = e.bsn_buyer_id and b.dt='${dmp_day}'
left join dwd.dwd_prpt_rec_fct d on a.bsn_prj_wrd=d.bsn_prj_wrd and d.dt='${dmp_day}'
where a.dt='${dmp_day}' 
  and a.prj_bsn_tp_cd='GQ' 
  and b.bsn_rec_deal_dt is not null
  and b.actl_txn_mth_cd in('2','3','10')
  and e.fnl_qua_cfrm_rslt_cd = '1'  -- 获得资格