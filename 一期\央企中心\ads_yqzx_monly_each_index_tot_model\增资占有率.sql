WITH
  base_data AS (
    SELECT DISTINCT
      x.exch,
      x.info_publ_start_dt, --挂牌日期
      x.Blng_Org AS blng_grp, -- 所属集团
      x.prj_blng_dept_nm AS proj_belong_dept_name, --所属部门
      COALESCE(x.Lit_Amt, 0)/10000 AS ht_amt, --挂牌金额（万元）
      SUBSTR(x.info_publ_start_dt, 1, 4) AS year, -- 挂牌年份
      SUBSTR(x.info_publ_start_dt, 6, 2) AS month, -- 挂牌月份
      x.prj_no, -- 项目编号
      x.prj_prt_sown_spvs_org_cd_dsc, -- 项目监管机构
      x.prj_stat_cd_dsc -- 项目状态
    FROM
      dws.dws_all_trsfer_info x
    WHERE
      x.prj_bsn_tp_cd_dsc='企业增资'
      and x.is_repeat ='否'
      AND (x.prj_prt_sown_spvs_org_cd_dsc not IN ('省级财政部门监管','市级财政部门或金融办监管','省级国资委监管','省级其他部门监管','市级国资委监管','市级其他部门监管') 
           OR (x.exch = '北交所' AND x.prj_prt_sown_spvs_org_cd_dsc is null))
      AND COALESCE(x.blng_org,'未知') NOT IN (
            SELECT CTY_CONTRI_CORP_LEAD_DEPT FROM ods.ods_bl_yqzx_fin_proj_list  where dt='${dmp_day}'
            )
  and x.prj_no not like 'G0%'
      AND dt='${dmp_day}'
      and x.exch is not null
      and x.prj_blng_dept_nm is not null
      and x.info_publ_start_dt is not null
      and x.prj_blng_dept_nm in ('央企一部','央企二部','央企三部','央企四部','央企五部')
  ),
  month_data1 as (
    select
      year,
      month,
      exch,
      proj_belong_dept_name,
      count(1) as month1y_count
    from
      base_data
    group by
      year,
      month,
      exch,
      proj_belong_dept_name
  ),
    month_data as (
    select
      year,
      month,
      exch,
      proj_belong_dept_name,
      month1y_count
    from
      month_data1
    union all
    select
      year,
      month,
      exch,
      '中心' proj_belong_dept_name,
      sum(month1y_count) as month1y_count
    from
      month_data1
    group by 
      year,
      month,
      exch
  ),
  date_data AS (
    -- 从日期维表中选择所需的月份
    SELECT DISTINCT
      SUBSTR(date_id, 1, 4) AS year,
      SUBSTR(date_id, 5, 2) AS month,
      year_month
    FROM
      dim.dim_date
    WHERE
      year_month>='201901'
      AND year_month<=DATE_FORMAT(CURRENT_DATE(), 'YYYYMM')
  ),
  total_data as (
    select 
    t.*,t2.month1y_count
    from (
    select
      t.year,
      t.month,
      t1.exch,
      t1.proj_belong_dept_name
--       t2.month1y_count
    from
      date_data t
      cross join (
        select DISTINCT
          exch,
          proj_belong_dept_name
        from
          month_data
      ) t1) t
      left join month_data t2 on t.year=t2.year
      and t.month=t2.month
      and t.exch=t2.exch
      and t.proj_belong_dept_name=t2.proj_belong_dept_name
  ),
  dept_m_data (
    select
      year,
      month,
      proj_belong_dept_name,
      sum(month1y_count) as monthly_count_dept
    from
      month_data
    group by
      year,
      month,
      proj_belong_dept_name
  ),
  total_data_year (
    select
      year,
      month,
      exch,
      proj_belong_dept_name,
      month1y_count,
      sum(month1y_count) over (
        PARTITION by
          year,
          exch,
          proj_belong_dept_name
        order by
          month
      ) as yearly_count
    from
      total_data
  ),
  dept_y_data (
    select
      year,
      month,
      proj_belong_dept_name,
      sum(yearly_count) as yearly_count_dept
    from
      total_data_year
    group by
      year,
      month,
      proj_belong_dept_name
  )
select
  CONCAT(t.year, '-', t.month) AS data_dt,
  '增资数量占有率' AS etl_grp_desc,
  t.exch,
  t.proj_belong_dept_name AS proj_belong_dept_name,
  t.month1y_count/t1.monthly_count_dept as monly_ind_val,
  t.yearly_count/t2.yearly_count_dept as year_ind_val
from
  total_data_year t
  left join dept_m_data t1 on t.proj_belong_dept_name=t1.proj_belong_dept_name
  and t.year=t1.year
  and t.month=t1.month
  left join dept_y_data t2 on t.proj_belong_dept_name=t2.proj_belong_dept_name
  and t.year=t2.year
  and t.month=t2.month