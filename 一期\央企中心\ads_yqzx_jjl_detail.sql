SELECT DISTINCT
  x.prj_no as proj_no, -- 项目编号
  x.prj_nm as proj_name, --项目名称
  (
    case
      when x.tfr_prc is null then 0
      else x.tfr_prc
    end
  )/10000 as sell_price, --转让底价
  x.BSN_REC_DEAL_DT as deal_date, -- 成交日期
  x.txn_mth_cd_dsc as deal_way_name, --成交方式
  (
    case
      when x.deal_amt is null then 0
      else x.deal_amt
    end
  )/10000 as deal_value, --成交金额
  case
    when x.deal_amt-x.tfr_prc>0 then '是'
    else '否'
  end as is_incre, --是否增值
  x.prj_blng_dept_nm as proj_belong_dept_name, --所属部门
  x.prj_prin_nm as proj_princ_name --项目负责人
FROM
  dws.dws_all_trsfer_info x
where
  x.prj_tp_cd_dsc='正式披露'
  AND x.prj_bsn_tp_cd_dsc='产权转让'
  AND x.dt='${dmp_day}'
  AND x.prj_stat_cd_dsc IN ('已成交', '已归档')
  AND (
    x.prj_prt_sown_spvs_org_cd_dsc not IN(
      '省级财政部门监管',
      '市级财政部门或金融办监管',
      '省级国资委监管',
      '省级其他部门监管',
      '市级国资委监管',
      '市级其他部门监管'
    )
    OR (x.exch = '北交所' AND x.prj_prt_sown_spvs_org_cd_dsc IS NULL)
  )
  AND COALESCE(x.blng_org, '未知') NOT IN(
   SELECT CTY_CONTRI_CORP_LEAD_DEPT
 FROM ods.ods_bl_yqzx_fin_proj_list where dt='${dmp_day}'
  )
  AND prj_blng_dept_nm in ('央企一部', '央企二部', '央企三部', '央企四部', '央企五部')