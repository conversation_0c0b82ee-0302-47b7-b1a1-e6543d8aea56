with temp_prj as (select t.*
                  from dwd.dwd_prj_fct t
                  inner join (
                    SELECT  'BJHL'||ID||'CQZR' AS bsn_prj_wrd
                    FROM std.std_bjhl_tcqzr_cqzrxxpl_d
                    WHERE dt = '${dmp_day}'
                    AND xmclass = '0'
                    ) ts on t.bsn_prj_wrd = ts.bsn_prj_wrd
                  where dt = '${dmp_day}'
                    and t.prj_bsn_tp_cd = 'GQ'
                    and t.prj_tp_cd = '1'
                  union all
                  select t.*
                  from dwd.dwd_prj_fct t
                           left join dwd.dwd_bulk_obj_prj_fct t1
                                     on t.bsn_prj_wrd = t1.bsn_prj_wrd and t1.dt = '${dmp_day}'
                  where t.dt = '${dmp_day}'
                    and t.prj_bsn_tp_cd = '1D'
                    and t1.ast_src in ('A17001', 'A17002')
                    and t1.is_gz = '1'
                  union all
                  select t.*
                  from dwd.dwd_prj_fct t
                           left join dwd.dwd_bid_task_list_info t1 on t.prj_wrd = t1.rltv_prj and t1.dt = '${dmp_day}'

                  where t.dt = '${dmp_day}'
                    and t.prj_bsn_tp_cd = '1B'
                    and (t1.ast_src like '企业资产%' or t1.ast_src like '行政事业%')
                    and t.txn_mth_cd = '3'),
     deal_prj as (select prj_wrd, bsn_prj_wrd, sum(deal_amt) as deal_amt, max(bsn_rec_deal_dt) as bsn_rec_deal_dt
                  from dwd.dwd_evt_deal_rec_fct
                  where dt = '${dmp_day}'
                  group by prj_wrd, bsn_prj_wrd),
     --平台月累积开评标数量
     avg_daily_tenders as (select '国有产权交易'                              as market_type,
                                  substr((case when a.prj_bsn_tp_cd = '1B' then b.delvry_dt else b.bsn_rec_deal_dt end),
                                         1,
                                         7)                                           data_dt,
                                  count(distinct a.prj_id) /
                                  count(distinct (case
                                                      when a.prj_bsn_tp_cd = '1B' then b.delvry_dt
                                                      else b.bsn_rec_deal_dt end)) as avg_daily_tenders
                           from temp_prj a
                                    left join dwd.dwd_evt_deal_rec_fct b on a.prj_wrd = b.prj_wrd and b.dt = '${dmp_day}'
                           where (case
                                      when a.prj_bsn_tp_cd = '1B' then b.delvry_dt
                                      else b.bsn_rec_deal_dt end) is not null
                           group by substr((case when a.prj_bsn_tp_cd = '1B' then b.delvry_dt else b.bsn_rec_deal_dt end),
                                           1, 7)),
     avg_daily_login_count as (select logincount /day(last_day(CONCAT(stat_date, '-01'))) as avg_daily_login_count,
                                      stat_date                                                                   as data_dt,
                                      '国有产权交易'                                                              as market_type
                               from ods.ods_bl_logincount),
     largest_transaction_project as (select substr(bsn_rec_deal_dt, 1, 7)      as data_dt,
                                            '国有产权交易'                  as market_type,
                                            concat_ws(';',collect_list(prj_nm|| ',' || deal_amt || '万元')) as largest_transaction_project
                                     from (select a.prj_id,
                                                  a.prj_nm,
                                                  a.prj_bsn_tp_cd_dsc,
                                                  cast(round(b.deal_amt / 10000, 2) as String) as deal_amt,
                                                  b.bsn_rec_deal_dt,
                                                  rank() over(PARTITION by substr(b.bsn_rec_deal_dt,1,7) order by b.deal_amt desc ) rn
                                           from temp_prj a
                                                    left join deal_prj b on a.prj_wrd = b.prj_wrd
                                           where a.prj_bsn_tp_cd in ('GQ', '1D')
                                             and b.bsn_rec_deal_dt is not null)
                                     where rn = 1
                                     group by substr(bsn_rec_deal_dt, 1, 7)),
     largest_awardee as (select substr(bsn_rec_deal_dt, 1, 7)                as data_dt,
                                '国有产权交易'                               as market_type,
                                concat_ws(';',collect_list(distinct itrsfee_repst_nm)) as largest_awardee
                         from (select a.prj_id,
                                      a.prj_nm,
                                      cast(round(d.deal_amt / 10000, 2) as String) as deal_amt,
                                      d.bsn_rec_deal_dt,
                                      c.itrsfee_repst_nm,
                                      rank() over(PARTITION by substr(d.bsn_rec_deal_dt,1,7) order by d.deal_amt desc ) rn
                               from temp_prj a
                                        left join deal_prj d on a.bsn_prj_wrd = d.bsn_prj_wrd
                                        left join dwd.dwd_evt_deal_rec_fct b
                                                  on a.bsn_prj_wrd = b.bsn_prj_wrd and b.dt = '${dmp_day}'
                                        left join dwd.dwd_ittn_buyer_fct c
                                                  on a.bsn_prj_wrd = c.bsn_prj_wrd and c.dt = '${dmp_day}' and
                                                     b.bsn_buyer_id = c.bsn_buyer_id
                               where a.prj_bsn_tp_cd in ('GQ', '1D')
                                 and b.bsn_rec_deal_dt is not null)
                         where rn = 1
                         group by substr(bsn_rec_deal_dt, 1, 7)),
     max_bidders_per_project as (select substr(x.bsn_rec_deal_dt, 1, 7) as data_dt,
                                        '国有产权交易'             as market_type,

                                        concat_ws(';',collect_list(x.sl||','||x.prj_nm)) as max_bidders_per_project
                                 from (select a.prj_id,
                                              a.prj_nm,
                                              a.prj_bsn_tp_cd_dsc,
                                              c.bsn_rec_deal_dt,
                                              cast(b.sl as String) sl,
                                              rank()                over(PARTITION by substr(c.bsn_rec_deal_dt,1,7) order by b.sl desc ) as rn
                                       from temp_prj a
                                                left join (select bsn_prj_wrd, count(distinct bsn_buyer_id) as sl
                                                           from dwd.dwd_ittn_buyer_fct
                                                           where dt = '${dmp_day}'
                                                             and fnl_qua_cfrm_rslt_cd = '1'
                                                           group by bsn_prj_wrd) b on a.bsn_prj_wrd = b.bsn_prj_wrd
                                                left join dwd.dwd_evt_deal_rec_fct c
                                                          on a.bsn_prj_wrd = c.bsn_prj_wrd and c.dt = '${dmp_day}'
                                       where c.bsn_rec_deal_dt is not null
                                         and a.prj_bsn_tp_cd in ('GQ', '1D')) x
                                 where x.rn = 1 group by substr(x.bsn_rec_deal_dt, 1, 7)),
     market_openness as (select substr(b.bsn_rec_deal_dt, 1, 7) as data_dt,
                                '国有产权交易'             as market_type,
                                sum(case when c.prov_cd_dsc ='北京市' then 0 else b.deal_amt end) /
                                sum(b.deal_amt)                 as market_openness
                         from temp_prj a
                                  left join dwd.dwd_evt_deal_rec_fct b
                                            on a.bsn_prj_wrd = b.bsn_prj_wrd and b.dt = '${dmp_day}'
                                  left join (select distinct bsn_prj_wrd, bsn_buyer_id, prov_cd_dsc
                                             from dwd.dwd_ittn_buyer_fct
                                             where dt = '${dmp_day}') c
                                            on a.bsn_prj_wrd = c.bsn_prj_wrd and b.bsn_buyer_id = c.bsn_buyer_id
                         where b.bsn_rec_deal_dt is not null
                           and a.prj_bsn_tp_cd in ('GQ', '1D')
                         group by substr(b.bsn_rec_deal_dt, 1, 7)),
     market_concentration as (
       select 
           substr(bsn_rec_deal_dt, 1, 7) as data_dt,
           '国有产权交易' as market_type,
           case when max_rn = 1 then '' else
                sum(case when rn > 
                (case when max_rn > 1 and max_rn <= 5 then max_rn-1
                        when  max_rn >= 6 and max_rn <= 20 then 5 
                        else 20 end)      
                then 0 else deal_amt end) / sum(deal_amt) 
           end as market_concentration
       from (
            select prj_id,
               prj_bsn_tp_cd_dsc,
               substr(bsn_rec_deal_dt, 1, 7) as bsn_rec_deal_dt,
               deal_amt,
               rn,
               max(rn) over (PARTITION by substr(t.bsn_rec_deal_dt, 1, 7)) as max_rn
            from (
                select a.prj_id,
                    a.prj_bsn_tp_cd_dsc,
                    b.bsn_rec_deal_dt,
                    b.deal_amt,
                    rank() over (PARTITION by substr(b.bsn_rec_deal_dt, 1, 7) order by b.deal_amt desc) as rn
                from temp_prj a
                left join deal_prj b on a.prj_wrd = b.prj_wrd
                where b.bsn_rec_deal_dt is not null
                and a.prj_bsn_tp_cd in ('GQ', '1D')
            ) t
        ) k
        group by substr(bsn_rec_deal_dt, 1, 7), max_rn        
                             )
select distinct a.data_dt,
                a.market_type,
                0                       as avg_experts,
                a.avg_daily_tenders,
                f.avg_daily_login_count as avg_daily_login_count,
                b.largest_transaction_project,
                g.largest_awardee       as largest_awardee,
                c.max_bidders_per_project,
                d.market_openness,
                e.market_concentration


from avg_daily_tenders a
         left join largest_transaction_project b on a.data_dt = b.data_dt and a.market_type = b.market_type
         left join max_bidders_per_project c on a.data_dt = c.data_dt and a.market_type = c.market_type
         left join market_openness d on a.data_dt = d.data_dt and a.market_type = d.market_type
         left join market_concentration e on a.data_dt = e.data_dt and a.market_type = e.market_type
         left join avg_daily_login_count f on a.data_dt = f.data_dt and a.market_type = f.market_type
         left join largest_awardee g on a.data_dt = g.data_dt and a.market_type = g.market_type
order by a.data_dt