with tem_a as (    
select t1.keyid            as keyid                          ,--keyid                   
       t1.xmbh             as project_code                   ,--项目编号                
       t1.xmmc             as project_name                   ,--项目名称                
       t1.khh_gpf          as listg_cust_no                  ,--挂牌方客户号            
       t1.bz               as ccy                            ,--币种                    
       t1.zjzh_gpf         as fund_acct                      ,--资金账户                
       t1.lsh_bzjdj_gpf    as deposit_frz_jrnl_no            ,--保证金冻结流水号        
       t1.class            as project_type                   ,--项目类型                
       t1.subclass         as prj_subcls                     ,--项目子类                
       t1.xmxl             as prj_smlcls                     ,--项目小类                
       t1.gpfs             as listg_mod                      ,--挂牌方式                
       t1.jyzt             as txn_sts                        ,--交易状态                
       t1.jyfs             as exchange_type                  ,--交易方式                
       t1.gpjg             as project_price                  ,--挂牌价格                
       t1.gpsl             as listg_num                      ,--挂牌数量(手)            
       t1.jyjs             as txn_crdnlt                     ,--交易基数(每手单位)      
       t1.dw               as unit                           ,--单位                    
       t1.gpje             as listg_amt                      ,--挂牌金额                
       t1.sfyxzj           as is_intnt_coll                  ,--是否意向征集            
       t1.gpksrq           as listg_strt_dt                  ,--挂牌开始日期            
       t1.gpkssj           as listg_strt_tm                  ,--挂牌开始时间            
       t1.gpjsrq           as listg_end_dt                   ,--挂牌结束日期            
       t1.gpjssj           as listg_end_tm                   ,--挂牌结束时间            
       t1.bjsfyxbm         as ofr_is_rgst                    ,--报价时是否需要报名      
       t1.sfyxtzjyfs       as is_prmt_adj_exchange_type      ,--是否允许调整交易方式    
       t1.sfyq             as is_pstp                        ,--是否延期                
       t1.yqts             as pstp_dys                       ,--延期天数                
       t1.gpts             as listg_dys                      ,--挂牌天数                
       t1.bzj_bm           as rgst_deposit                   ,--报名保证金(元)          
       t1.bzj_bl_bj        as ofr_deposit_pct                ,--报价保证金比例          
       t1.bzj_jnfs         as deposit_py_mod                 ,--保证金缴纳方式          
       t1.fwf_bl_srf       as buyer_serv_fee_pct             ,--受让方服务费比例        
       t1.fwf_bl_gpf       as seller_serv_fee_pct            ,--转让方服务费比例        
       t1.bjssfyxbm        as ofr_is_prmt_rgst               ,--报价时是否允许报名      
       t1.yyb              as src_biz_dp                     ,--来源营业部              
       t1.jbyyb            as hdl_biz_dp                     ,--经办营业部              
       t1.sfyxzyfk         as is_prmt_free_py                ,--是否允许自由付款        
       t1.fllb             as fee_rate_scm                   ,--费率方案                
       t1.fylb             as cc_scm                         ,--分佣方案                
       t1.xyfllb           as agt_scm                        ,--协议方案                
       t1.sfdjf_zrf        as seller_is_idp_py_serv_fee      ,--转让方是否单独缴纳服务费
       t1.fkts             as py_dys                         ,--付款天数                
       t1.jjcc             as ltst_bid_rnd                   ,--最新竞价场次            
       t1.yqcs             as pstp_cnt                       ,--延期次数                
       t1.jyxz             as txn_rst                        ,--交易限制                
       t1.xmbh_bak         as project_code_bak               ,--项目编号_备份           
       t1.zcly             as ast_src                        ,--资产来源                
       t1.bzj_gp           as listg_side_deposit             ,--挂牌方保证金(元)        
       t1.qzjsrq           as qzjsrq                         ,--                        
       t1.qzjssj           as qzjssj                         ,--                        
       t1.bmrsxx           as bmrsxx                         ,--                        
       t1.bzj_jnjzsj       as bzj_jnjzsj                     ,--                        
       t1.jsjg             as jsjg                           ,--                        
       t1.ssjg             as ssjg                           ,--                        
       t1.yzjdyxfyqrssx    as yzjdyxfyqrssx                  ,--                        
       t1.yzjdyxfyqts      as yzjdyxfyqts                    ,--                        
       t1.yzjdyxfyqcs      as yzjdyxfyqcs                    ,--                        
       t1.zrfmc            as zrfmc                          ,--                        
       case when t1.class = '1D' and t10.id is null and t3.srf is null then null 
            when t1.class = '1D' and t10.id = t3.srf then '是'
            when t1.class = '1D' and t10.id <> t3.srf then '否' 
            when t1.class = '1C' and T5.t_tzf is null and t6.id is null then null 
            when t1.class = '1C' and T5.t_tzf = t6.id then '是'
            when t1.class = '1C' and T5.t_tzf <> t6.id then '否'
            when t1.class = 'GQ' and t8.srfid is null and t9.id is null then null 
            when t1.class = 'GQ' and t8.srfid = t9.id then '是'
            when t1.class = 'GQ' and t8.srfid <> t9.id then '否' end as  is_buyer,--是否受让方
       case when t1.class = '1D' then t10.srfmc
            when t1.class = '1C' then t6.mc
            when t1.class = 'GQ' then t9.srfmc end as buyer_anm,--受让方或意向受让方名称
       case when t1.class = '1D' then t10.province
            when t1.class = '1C' then t6.province
            when t1.class = 'GQ' then t9.province end as province_code, --省
       t9.sflhsr as  is_joint_transferee, --是否联合受让
       case when t1.class = '1D' then t10.id
            when t1.class = '1C' then t6.id
            when t1.class = 'GQ' then t9.id end as buyer_id --受让方id
  from ods.ods_bjhl_tbid_gpxmxx         t1
  left join ods.ods_bjhl_tdzsw_xxpl     t2
    on t1.keyid =t2.xmid and t2.dt = '${dmp_day}'
  left join ods.ods_bjhl_tdzsw_cjjl     t3
    on t2.id=t3.xm and t3.dt = '${dmp_day}'
  left join ods.ods_bjhl_tdzsw_yxsrfxx  t10  
    on t3.srf = t10.id and t10.dt = '${dmp_day}'
  left join ods.ods_bjhl_tcgq_zzzsgpxm  t4
    on t1.keyid = t4.xmid and t4.dt = '${dmp_day}'
  left join ods.ods_bjhl_tcgq_cjjl      t5             
    on t4.id=t5.t_xm and t5.dt = '${dmp_day}'
  left join ods.ods_bjhl_tcgq_yxtzfxx   t6
    on t5.t_tzf=t6.id and t6.dt = '${dmp_day}'
  left join ods.ods_bjhl_tcqzr_cqzrxxpl t7
    on t1.keyid = t7.xmid and t7.dt = '${dmp_day}'
  left join ods.ods_bjhl_tcqzr_cjjlxx   t8
    on t7.id=t8.xmid and t8.dt = '${dmp_day}'
  left join ods.ods_bjhl_tcqzr_yxsrfxx  t9  
    on t8.srfid = t9.id and t8.xmid=t9.xmid and t9.dt = '${dmp_day}'
 where t1.dt = '${dmp_day}'
   and t1.class in ('1C','1D','GQ') ) 
 select t1.*,
        t2.xzqymc as province_name
   from tem_a t1
   left join ods.ods_bjhl_txzqydm t2
     on t1.province_code = t2.xzqydm and t2.dt = '${dmp_day}'