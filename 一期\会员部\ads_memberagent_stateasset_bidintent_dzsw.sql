with temp_buyer as (select distinct bsn_prj_wrd, bsn_buyer_id, txn_svc_mber_id, txn_svc_mber_nm,itrsfee_repst_nm
                    ,fnl_qua_cfrm_rslt_cd_dsc
                    ,fnl_qua_cfrm_rslt_cd
                    ,case when is_fnl_trsfee = '1' then '是' else '否' end  as is_fnl_trsfee --是否最终受让方
                    from dwd.dwd_ittn_buyer_fct
                    where dt = '${dmp_day}'
                    --  and mrgn_stat_cd = '1'
                      )
select 
    a.prj_id as proj_no,  -- 项目编号
    a.prj_nm as proj_name,  -- 项目名称
    a.prj_stat_cd_dsc as prj_stat_cd_dsc,  -- 项目状态描述
    a.prj_prt_nm as seller_fincer_name,  -- 转让方名称
    a.txn_svc_mber_nm as agent_mem,  -- 转让方经纪会员名称
    e.itrsfee_repst_nm as buyer_name,  -- （意向）受让方名称
    e.txn_svc_mber_nm as buyer_agent_name,  -- （意向）受让方经纪会员名称
    case when e.bsn_buyer_id = b.bsn_buyer_id then '是' else '否' end as is_buyer,  -- 是否为受让方经纪会员
    d.prj_prt_svfee_tot_amt as seller_fee_amt,  -- 转让方服务费总金额（元）
    d.buyer_svfee_tot_amt as buyer_fee_amt,  -- 受让方服务费总金额（元）
    d.prj_prt_svfee_tot_amt + d.buyer_svfee_tot_amt as fee_amt,  -- 收费总额（元）
    d.cbex_income as cbex_fee_amt,  -- 北交所收入（元）
    a.tfr_prc / 10000 as sell_price,  -- 转让底价（万元）
    b.deal_amt / 10000 as deal_value,  -- 成交金额（万元）
    a.txn_mth_cd_dsc as trans_type,  -- 交易方式
    b.actl_txn_mth_cd_dsc as deal_way_name,  -- 成交方式
    b.bsn_rec_deal_dt as deal_date,  -- 成交日期
    a.info_publ_start_dt as info_dclo_begin_dt,  -- 信息披露起始日期
    a.info_publ_exprt_dt as info_dclo_expire_dt,  -- 信息披露期满日期
    a.prj_blng_dept_nm as proj_belong_dept_name,  -- 所属部门
    a.prj_prin_nm as proj_princ_name,  -- 项目负责人
    case when e.bsn_buyer_id=b.bsn_buyer_id then '是' else '否' end as is_fnl_trsfee  -- 是否最终受让方
from dwd.dwd_prj_fct a
left join temp_buyer e on a.bsn_prj_wrd = e.bsn_prj_wrd
left join dwd.dwd_evt_deal_rec_fct b on a.bsn_prj_wrd = b.bsn_prj_wrd and b.bsn_buyer_id = e.bsn_buyer_id and b.dt = '${dmp_day}'
left join dwd.dwd_prpt_rec_fct d on a.bsn_prj_wrd = d.bsn_prj_wrd and d.dt = '${dmp_day}'
left join dwd.dwd_bulk_obj_prj_fct f on a.bsn_prj_wrd = f.bsn_prj_wrd and f.dt = '${dmp_day}'
where a.dt = '${dmp_day}'
and a.prj_bsn_tp_cd = '1D'
and b.bsn_rec_deal_dt is not null
and b.actl_txn_mth_cd in('2','3','10')
and e.fnl_qua_cfrm_rslt_cd = '1'  -- 获得资格
and f.is_gz = 1