import httpx
import json
import pymysql
from datetime import datetime
from impala.dbapi import connect
import time

# 定义接口访问方法

def get_response(org_name):
    url = "http://10.3.1.37:80/uap-api/enterprise-information-full"
    
    headers = {
        "key": "0e1c14c58f8502501eab4ebaca77b852",
        "channel": "PC", 
        "userID": "bjzt", 
        "userName": "bjzt"  
    }
    
    json_data = {
        "name": [org_name],
        "code":[]
    }
    
    try:
        response = httpx.post(url, headers=headers, json=json_data, timeout=10)
        response.raise_for_status()  

        data = response.json()

        if not data.get("data"):
            return {
                "status": "fail",
                "message": "查询无结果",
                "data": []
            }
        
        return data

    except httpx.HTTPStatusError as e:
        # Handle HTTP errors
        print(f"HTTP error occurred: {e}")
        return {
            "status": "fail",
            "message": f"HTTP error: {e.response.status_code}",
            "data": []
        }

    except httpx.RequestError as e:
        # 返回错误
        print(f"Request error occurred: {e}")
        return {
            "status": "fail",
            "message": "Request error, possible network issue",
            "data": []
        }

    except httpx.TimeoutException:
        # 超时错误
        print("Request timed out")
        return {
            "status": "fail",
            "message": "Request timed out",
            "data": []
        }


# 数据库连接配置
db_config = {
    'host': '*************',      # 数据库主机地址
    'user': 'root',  # 数据库用户名
    'password': 'Cbexsg#202206',  # 数据库密码
    'database': 'bl',  # 数据库名称
    'charset': 'utf8mb4'
}


# 获取base信息
def get_base_info(org_name):
    
    response = get_response(org_name)
    
    if not response.get('data'):
        return {}  # 如果 data 是空数组，返回一个空字典
    
    response_data = response.get('data')[0]
    data_base_str = response_data.get('base')
    data_base_dict = json.loads(data_base_str)
    return data_base_dict


# 获取上级信息
def find_max_stock_percent(partners_list):
    # 第一步，找出最大股权占比是多少
    max_percent = max(float(partner['StockPercent']) for partner in partners_list)
    # print(max_percent)
    return max_percent

def find_companies_with_max_percent(partners_list, max_percent):
    # 第二步，找出所有最大值相同的公司
    max_companies = [partner for partner in partners_list if float(partner['StockPercent']) == max_percent]
    return max_companies

def get_all_upper_companies(initial_company):
    # 第三步，递归获取上级公司
    all_companies = set()  # 使用集合来去重

    def recursive_search(company_name):
        # 为了防止接口调用太块，所以需要加一些延迟
        time.sleep(0.5)
        response = get_response(company_name)
        response_data = response.get('data')

        if not response_data or len(response_data) == 0:
            return
        
        data_partners_str = response_data[0].get('partners')
        print(data_partners_str)
        if not data_partners_str:
            return
        partners_list = json.loads(data_partners_str)

        # 查找最大股权占比和对应的公司
        if partners_list:
            max_percent = find_max_stock_percent(partners_list)
            max_companies = find_companies_with_max_percent(partners_list, max_percent)
            for company in max_companies:
                stock_name = company['StockName']
                print(f"查询: {stock_name},本次上级为：{company_name}")
                if stock_name == company_name:
                    # 如果查询的公司等于自己，则退出递归循环
                    return
                all_companies.add(stock_name)
                # 递归调用，获取上级公司
                recursive_search(stock_name)
    
    # 从初始公司开始递归搜索
    recursive_search(initial_company)
    all_companies_list = list(all_companies)
    # 删除字符串为 '国务院国有资产监督管理委员会','中华人民共和国国务院' 的项
    filtered_companies = [company for company in all_companies_list if company != '国务院国有资产监督管理委员会' and company != '中华人民共和国国务院']

    # 将列表中的值组合成一个字符串，用英文逗号链接
    final_result = ','.join(filtered_companies)
    
    return final_result

# 插入数据的函数
def insert_company_info(data_dict,initial_company):
    # 创建数据库连接
    try:
        connection = pymysql.connect(**db_config)
        print("数据库连接成功")
    except pymysql.MySQLError as e:
        print(f"数据库连接失败: {e}")
        return False

    try:
        with connection.cursor() as cursor:
            # SQL 插入语句
            sql = """
            INSERT INTO bl.api_full_company_info (
                SearchCompanyName,BusinessDateFrom, Authority, CompanyStatus, BusinessScope,
                IssueDate, BusinessDateTo, Capital, CompanyType,
                LegalPerson, EstablishDate, Province, KeyNo, 
                CompanyAddress, CompanyName, OrgCode, IsOnStock, 
                CreditNo, CompanyCode, UpperCompanies, DataSource, ETLDate
            ) VALUES (
                %(SearchCompanyName)s,%(BusinessDateFrom)s, %(Authority)s, %(CompanyStatus)s, %(BusinessScope)s,
                %(IssueDate)s, %(BusinessDateTo)s, %(Capital)s, %(CompanyType)s,
                %(LegalPerson)s, %(EstablishDate)s, %(Province)s, %(KeyNo)s, 
                %(CompanyAddress)s, %(CompanyName)s, %(OrgCode)s, %(IsOnStock)s, 
                %(CreditNo)s, %(CompanyCode)s, %(UpperCompanies)s, %(DataSource)s, %(ETLDate)s
            )
            """
            
            # 添加固定值到字典中
            data_dict['SearchCompanyName'] = initial_company
            data_dict['DataSource'] = "enterprise-information-full"
            data_dict['ETLDate'] = datetime.now().strftime('%Y-%m-%d')
            
            # 插入数据前先删除当天的数据
            delete_sql = f"DELETE FROM bl.api_full_company_info WHERE SearchCompanyName = '{initial_company}' AND ETLDate = '{data_dict['ETLDate']}'"
            cursor.execute(delete_sql)
            connection.commit()
            print(f"删除当天数据成功: {initial_company}")

            # 执行插入操作
            cursor.execute(sql, data_dict)
            connection.commit()
            company_name = data_dict.get('CompanyName')
            print(f"数据插入成功: {company_name}")
            return True

    except pymysql.MySQLError as e:
        print(f"数据插入失败: {e}")
        connection.rollback()
        return False

    finally:
        # 关闭连接
        connection.close()
        print("数据库连接已关闭")

def get_final_result(initial_company):
    # 获取公司基本信息
    data_base_dict = get_base_info(initial_company)
    
    if not data_base_dict:
        return {}
   
    # 获取所有上级公司(因为用到了递归所以需要单独获取)
    resulting_companies = get_all_upper_companies(initial_company)
    # 组合字典
    data_base_dict['UpperCompanies'] = resulting_companies
    
    # 确保字典中包含所有需要的键
    required_keys = ['BusinessDateFrom', 'Authority', 'CompanyStatus', 'BusinessScope',
                     'IssueDate', 'BusinessDateTo', 'Capital', 'CompanyType',
                     'LegalPerson', 'EstablishDate', 'Province', 'KeyNo', 
                     'CompanyAddress', 'CompanyName', 'OrgCode', 'IsOnStock', 
                     'CreditNo', 'CompanyCode', 'UpperCompanies']
    for key in required_keys:
        if key not in data_base_dict:
            data_base_dict[key] = None  # 默认值

    return data_base_dict


def get_hive_data():
    cursor = None
    conn = None
    try:
        # 连接到 Impala
        conn = connect(host='*************', port=10000,user = 'root',auth_mechanism='PLAIN')
        cursor = conn.cursor()
        
        # 获取所有分区
        partition_query = """
        SHOW PARTITIONS dws.dws_entp_incptl_trsfer_d
        """
        cursor.execute(partition_query)
        partitions = cursor.fetchall()
        
        # 提取分区值并找出最新的分区
        partition_values = [partition[0].split('dt=')[-1] for partition in partitions]
        latest_partition = max(partition_values)
        print(f"最新分区: {latest_partition}")
        
        # 根据最新分区查询数据
        query = f"""
        SELECT 
            DISTINCT fincer_nm
        FROM dws.dws_entp_incptl_trsfer_d 
        WHERE dt = '{latest_partition}' 
        AND info_esr_beg_dt >= date_format(add_months(current_date, -2), 'yyyy-MM-01')
        AND info_esr_beg_dt < date_format(add_months(current_date, -1), 'yyyy-MM-01')
        UNION 
        SELECT 
            DISTINCT transferor_name AS fincer_nm 
        FROM dws.dws_prtrigt_trsfer_d 
        WHERE dt = '{latest_partition}' 
        AND disclosure_start >= date_format(add_months(current_date, -2), 'yyyy-MM-01')
        AND disclosure_start < date_format(add_months(current_date, -1), 'yyyy-MM-01')
        UNION 
        SELECT 
            DISTINCT target_name AS fincer_nm 
        FROM dws.dws_prtrigt_trsfer_d 
        WHERE dt = '{latest_partition}' 
        AND disclosure_start >= date_format(add_months(current_date, -2), 'yyyy-MM-01')
        AND disclosure_start < date_format(add_months(current_date, -1), 'yyyy-MM-01')
        """
        cursor.execute(query)
        results = cursor.fetchall()
        data_list = [result[0] for result in results]
        print(f"查询结果: {data_list}")
        
        return data_list
        

    except Exception as e:
        print(f"发生错误: {e}")
        return []

    finally:
        # 确保连接关闭
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            


# 从hive数据库获取需要查询的公司列表
company_list = get_hive_data()

# 主循环循环company_list列表 查询接口数据并插入数据库
for company in company_list:
    # 查询接口数据
    try:
        data_dict = get_final_result(company)
        
        if not data_dict:
            print(f"查询无结果: {company}")
            continue
        else:
            print(f"{company}-查询结果：{data_dict}")
    except Exception as e:
        print(f"查询失败: {company}, 错误: {e}")
        continue
    
    # 插入数据到数据库
    try:
        insert_company_info(data_dict,initial_company=company)
    except Exception as e:
        print(f"插入数据失败: {company}, 错误: {e}")
        continue
print("全部公司查询结束")
