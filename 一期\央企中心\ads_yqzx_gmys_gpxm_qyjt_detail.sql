with
  temp as (
    SELECT
      belong_group,
      belong_group1,
      PROJ_BELONG_DEPT_NAME,
      PROJ_PRINC_NAME,
      udt_tm
    from
      (
        SELECT
          belong_group,
          belong_group1,
          PROJ_BELONG_DEPT_NAME,
          PROJ_PRINC_NAME,
          udt_tm,
          ROW_NUMBER() OVER (
            PARTITION BY
              belong_group
            ORDER BY
              udt_tm DESC
          ) AS rn
        FROM
          ods.ods_bl_center_cust_bel_dept
        where
          dt='${dmp_day}'
      ) a
    where
      a.rn=1
  ),
  temp1 as (
    SELECT DISTINCT
      substr(x.info_publ_start_dt, 1, 7) as data_dt,
      case
        when x.prj_bsn_tp_cd_dsc='产权转让' then '产权转让'
        when x.prj_bsn_tp_cd_dsc='企业增资' then '企业增资'
        else '实物'
      end as proj_type, -- 项目类型
      COALESCE(x.Lit_Amt, 0)/10000 as ht_amt, --挂牌金额（万元）
      x.is_repeat as is_repeat_ht, --是否重复挂牌
      x.prj_no as proj_no, -- 项目编号
      x.prj_nm as proj_name, --项目名称
      x.info_publ_start_dt as info_dclo_begin_dt, --信息披露起始日期
      x.Info_Publ_Exprt_Dt as info_dclo_expire_dt, --信息披露期满日期
      x1.belong_group1 as blng_grp, -- 所属集团
      x.prj_prt_nm as seller_fincer_name, --转让方名称/融资方名称
      x.prj_prt_sown_spvs_org_cd_dsc as oasset_custd_org, --国资监管机构
      --央企三部三部的数据需要映射码表取具体的所属部门
      case when x.prj_blng_dept_nm ='央企三部' then x1.PROJ_BELONG_DEPT_NAME else x.prj_blng_dept_nm end  as proj_belong_dept_name, --所属部门
      case when x.prj_blng_dept_nm ='央企三部' then x1.PROJ_PRINC_NAME else x.prj_prin_nm end  as proj_princ_name, --项目负责人
      x.txn_mth_cd_dsc as deal_way_name, --成交方式
      case
        when x.deal_amt-x.tfr_prc>0 then '是'
        else '否'
      end as is_incre, --是否增值
      x.exch -- 挂牌交易所
    FROM
      dws.dws_all_trsfer_info x
      left join temp x1 on x.Blng_Org=x1.belong_group
    where
      --  prj_tp_cd_dsc='正式披露'
      -- and prj_prt_sown_spvs_org_cd_dsc in('国务院国资委监管','中央其他部委监管')and   
      prj_blng_dept_nm in ('央企一部', '央企二部', '央企三部', '央企四部', '央企五部', '央企六部')
      and x.dt=${dmp_day}
      and x.Lit_Amt>=100000000
      and (
        x.prj_bsn_tp_cd_dsc not in ('产权转让', '企业增资')
        OR (
          x.prj_bsn_tp_cd_dsc in ('产权转让', '企业增资')
          AND (x.prj_prt_sown_spvs_org_cd_dsc not IN(
            '省级财政部门监管',
            '市级财政部门或金融办监管',
            '省级国资委监管',
            '省级其他部门监管',
            '市级国资委监管',
            '市级其他部门监管'
          )
          OR (x.exch = '北交所' AND x.prj_prt_sown_spvs_org_cd_dsc IS NULL))
        )
      )
  )
  
,
  temp2 as(
SELECT 
        data_dt,
        proj_type,
        ht_amt,
        is_repeat_ht,
        proj_no,
        proj_name,
        exch,
        blng_grp,
        proj_belong_dept_name,
        proj_no,
         ROW_NUMBER() OVER (
            PARTITION BY data_dt, SPLIT(proj_no, '-')[0]
            ORDER BY ht_amt DESC, 
                     CAST(SPLIT(proj_no, '-')[1] AS INT) ASC
        ) AS rn
    FROM 
        temp1
    WHERE 
        is_repeat_ht = '是'
  )
select
  a.data_dt,
  a.is_repeat_ht,
  a.exch,
  a.ht_amt as lit_amt,
  a.proj_type,
  a.blng_grp,
  a.proj_belong_dept_name
from
  temp1 a where is_repeat_ht = '否'
  union all
select
  a.data_dt,
  a.is_repeat_ht,
  a.exch,
  a.ht_amt as lit_amt,
  a.proj_type,
  a.blng_grp,
  a.proj_belong_dept_name
from
  temp2 a where rn=1
