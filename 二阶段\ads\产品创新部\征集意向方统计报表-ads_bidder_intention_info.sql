-- Active: 1725412082987@@10.254.99.145@10000@dwd
SELECT
    DISTINCT
    cust_name, -- 客户名称
    contact_person_name, -- 联系人
    phone, -- 联系电话
    email, -- 电子邮件
    CASE WHEN disclosure_type = '预披漏' THEN '预披露' ELSE disclosure_type END AS disclosure_type, -- 披露类型
    prj_type, -- 项目类型
    prj_no, -- 项目编号
    prj_name, -- 项目名称
    regexp_replace(replace(disclosure_start_time,'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS disclosure_start_time, -- 披露起始日期
    regexp_replace(replace(disclosure_end_time,'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS disclosure_end_time, -- 披露结束日期
    prj_responsible_person, -- 项目负责人
    intention_desc, -- 意向描述
    increase_in_registered_capital, -- 拟新增注册资本（万元）
    investment_amt, -- 拟投资金额（万元）
    investment_pct, -- 拟投资比例（%）
    prj_dep -- 项目部门
FROM
    dws.dws_bidder_intention_info
WHERE dt= '${dmp_day}'