-- Active: 1725412082987@@10.254.99.145@10000@dws
-- 预披露
SELECT 
a.prj_id AS proj_no, --项目id
a.prj_nm AS proj_name, --项目名称
a.prj_stat_cd_dsc AS proj_status, --项目状态
CASE WHEN a.intnd_new_cptl_mod = '2' THEN '区间' ELSE '固定值' END AS sell_price_compute_mth, --转让底价计算方式
CASE WHEN substr(a.pre_publ_prj_wrd,-4) = 'QYZZ' THEN COALESCE(intnd_rs_cptl_tot_amt,0) ELSE COALESCE(a.tfr_prc,0)/10000 END AS sell_price, --转让底价
COALESCE(a.intnd_rs_cptl_tot_amt_min_val,0) AS sell_price_min, --转让底价最小值
COALESCE(a.intnd_rs_cptl_tot_amt_max_val,0) AS sell_price_max, --转让底价最大值
regexp_replace(replace(substr(a.pre_publ_start_dt,1,10),'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS info_dclo_begin_dt, --披漏起始日期
regexp_replace(replace(substr(a.pre_puep_dt,1,10),'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS info_dclo_expire_dt, --信息披露截止日期
CASE WHEN substr(a.pre_publ_prj_wrd,-4) = 'QYZZ' THEN '企业增资'
	   WHEN substr(a.pre_publ_prj_wrd,-4) = 'CQZR' THEN '产权转让' 
END AS source,	--来源系统
'预披露' AS disclosure_type,	 --披露类型
a.industry_tp_dsc AS industry_type, --所属行业类型
a.obj_prov_dsc AS proj_location --项目所属地
FROM dws.dws_pre_publ_prj_fct a
WHERE substr(a.pre_publ_prj_wrd,-4) IN ('CQZR','QYZZ')
AND (
 a.prj_nm RLIKE '文化|文物|文体|旅游|旅行|体育|运动|休闲|景区|酒店|宾馆|民宿|度假|康养|养老|乡村|传媒|影视|影业|影院|影城|电影|影音|公园|作品|活动|动漫'
 OR a.industry_tp_dsc IN (
 	'文化、体育和娱乐业','住宿和餐饮业','住宿业','餐饮业',
 	'广播、电视、电影和影视录音制作业','新闻和出版业','广播、电视、电影和录音制作业',
 	'文化艺术业','体育','娱乐业'
 )
)
AND a.pre_publ_start_dt IS NOT NULL 
AND a.pre_publ_start_dt != '0'
AND a.prj_stat_cd_dsc NOT IN ('待认领','待业务经办提交','已退回','已提交','待提交')
AND a.dt = '${dmp_day}'
UNION ALL 
-- 正式披露
SELECT 
a.prj_id AS proj_no, --项目id
a.prj_nm AS proj_name, --项目名称
a.prj_stat_cd_dsc AS proj_status, --项目状态
COALESCE(b.plan_new_cptl_mth_cd_dsc,'固定值') AS sell_price_compute_mth, --转让底价计算方式
CASE WHEN a.prj_bsn_tp_cd = '1C' THEN COALESCE(b.plan_raise_cptl_amt,0)/10000 ELSE COALESCE(a.tfr_prc,0)/10000 END AS sell_price, --转让底价
COALESCE(b.plan_raise_cptl_amt_min,0)/10000 AS sell_price_min, --转让底价最小值
COALESCE(b.plan_raise_cptl_amt_max,0)/10000 AS sell_price_max, --转让底价最大值
regexp_replace(replace(substr(a.lit_star_dt,1,10),'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS info_dclo_begin_dt,  --披漏起始日期
regexp_replace(replace(substr(a.lit_end_dt,1,10),'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS info_dclo_expire_dt, --信息披露截止日期
CASE WHEN a.prj_bsn_tp_cd = '1D' THEN '大宗实物' ELSE a.prj_bsn_tp_cd_dsc END AS source, --来源系统
'正式披露' AS disclosure_type,	 --披露类型
a.industry_tp_dsc AS industry_type, --所属行业类型
a.obj_prov_dsc AS proj_location --项目所属地
FROM dwd.dwd_prj_fct a
LEFT JOIN 
(
	SELECT 
	bsn_prj_wrd,plan_new_cptl_mth_cd,plan_new_cptl_mth_cd_dsc,
	plan_raise_cptl_amt,plan_raise_cptl_amt_min,plan_raise_cptl_amt_max 
	FROM dim.dim_incptl_info b
	WHERE dt = '${dmp_day}' AND b.edw_end_dt = '20991231'
) b
ON b.bsn_prj_wrd = a.bsn_prj_wrd
WHERE a.prj_bsn_tp_cd IN ('1D','1C','GQ')
AND (
 a.prj_nm RLIKE '文化|文物|文体|旅游|旅行|体育|运动|休闲|景区|酒店|宾馆|民宿|度假|康养|养老|乡村|传媒|影视|影业|影院|影城|电影|影音|公园|作品|活动|动漫'
 OR a.industry_tp_dsc IN (
 	'文化、体育和娱乐业','住宿和餐饮业','住宿业','餐饮业',
 	'广播、电视、电影和影视录音制作业','新闻和出版业','广播、电视、电影和录音制作业',
 	'文化艺术业','体育','娱乐业'
 )
)
AND a.lit_star_dt IS NOT NULL 
AND a.lit_star_dt!='0'
AND a.prj_stat_cd_dsc NOT IN ('待认领','待业务经办提交','已退回','已提交','待提交')
AND SUBSTR(a.prj_id,0,2) != 'CP' --CP开头的来源系统是信息披露的,不在这里取
AND SUBSTR(a.prj_id,0,2) != 'P-' --不取预披露
AND SUBSTR(prj_id,-2) !='-0' --不取预披露
AND a.dt = '${dmp_day}'
UNION ALL
-- 信息披露
select 
project_code as proj_no,    --项目编号
project_name as proj_name,    --项目名称
prj_sts_dsc as proj_status,         --项目状态
'固定值' as sell_price_compute_mth, --转让底价计算方式
NULL as sell_price, --转让低价，信息披露的转让底价都留空
NULL as sell_price_min, --转让低价最小值，信息披露的转让底价都留空
NULL as sell_price_max, --转让低价最大值，信息披露的转让底价都留空
regexp_replace(cast(esr_beg_dt as string), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') as info_dclo_begin_dt,      --信息披露起始日期
regexp_replace(cast(esr_exp_dt as string), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') as info_dclo_expire_dt,      --信息披露截止日期
'统一信息披露' as source,    --来源系统
case when esr_tp = 1 then '预披露'
      when esr_tp = 2 then '正式披露'
      when esr_tp = 3 then '综合招商'
      when esr_tp = 4 then '项目推介'
end as disclosure_type,          --披露类型
industry_tp_dsc as industry_type, --所属行业类型
b.admn_rgon_nm as proj_location    --项目所属地
from dwd.dwd_information_disclosure_info a 
LEFT JOIN (
    SELECT region_code,  -- 代码 
           admn_rgon_nm  -- 码值
    FROM std.std_bjhl_txzqydm_d  -- 行政区域代码表
    WHERE dt='${dmp_day}'
) b 
ON a.wbt_prov = b.region_code
where esr_beg_dt is not NULL
and (
 project_name RLIKE '文化|文物|文体|旅游|旅行|体育|运动|休闲|景区|酒店|宾馆|民宿|度假|康养|养老|乡村|传媒|影视|影业|影院|影城|电影|影音|公园|作品|活动|动漫'
 OR industry_tp_dsc IN (
 	'文化、体育和娱乐业','住宿和餐饮业','住宿业','餐饮业',
 	'广播、电视、电影和影视录音制作业','新闻和出版业','广播、电视、电影和录音制作业',
 	'文化艺术业','体育','娱乐业'
 )
)
and prj_sts_dsc not in ('待认领','待业务经办提交','已退回','已提交','待提交')
and dt = '${dmp_day}'