SELECT 
 DATE_FORMAT(CURRENT_TIMESTAMP(),'yyyy-MM-dd HH:mm:ss')     as ETL_LOAD_DT                     --ETL载入时间
,T.PRJ_ID                                                   as PROJECT_CODE                    --1.项目编号
,T.<PERSON><PERSON>_<PERSON><PERSON>                                                   as PROJECT_NAME                    --2.项目名称
,T2.PRJ_PRT_NM                                              as FINCER_NM                       --3.融资方名称
,T2.SOWN_SPVS_ORG_CD_DSC                                    as FINCER_OASSET_REG_ORG           --4.融资方国资监管机构
,T4.APRV_RCRD_ORG                                           as APPRV_REC_ORG                   --5.核准（备案）机构
,T2.APRV_UNIT_NM                                            as AUTHORIZE_UNIT_NM               --6.批准单位名称
,T2.BLNG_IDY_TP_CD_DSC                                      as FINCER_IDY_TP                   --7.融资方行业类型
,T2.BLNG_IDY_CD_DSC                                         as FINCER_INDUSTRY                 --8.融资方所属行业
,T6.ANUL                                                    as AUDT_RPT_ANUL                   --9.审计报告_年度
,T6.NET_PFT /10000                                          as AUDT_RPT_NETPROFIT              --10.审计报告_净利润（万元）
,T7.RPT_TP_CD_DSC                                           as RCTLY_FIN_RPT_STMT_TYPE         --11.最近一期财务报表_报表类型
,T7.RPT_DT                                                  as RCTLY_FIN_RPT_STMT_DATE         --12.最近一期财务报表_报表日期
,T7.NET_PFT /10000                                          as RCTLY_FIN_RPT_NETPROFIT         --13.最近一期财务报表_净利润（万元）
,T5.PRAISE_CPTL_HSHARE_PCT *100                             as INTND_RS_CPTL_CRPND_HOLD_SHR_PCT--14.拟募集资金对应持股比例（%）
,T3.NETAST_BOOK_VAL /10000                                  as EQUITY_BOOK_VAL                 --15.净资产账面价值（万元）
,T3.NETAST_ASES_VAL /10000                                  as EVALUATE_EQUITY                 --16.净资产评估值（万元）
,T5.PLAN_RAISE_CPTL_AMT /10000                              as INTND_RS_CPTL_AMT               --17.拟募集资金金额（万元）
,T.INFO_PUBL_START_DT                                       as INFO_ESR_BEG_DT                 --18.信息披露起始日期
,T.INFO_PUBL_EXPRT_DT                                       as INFO_ESR_EXP_DT                 --19.信息披露期满日期
,T11.IVS_CPTL_TOT_AMT /10000                                as IVS_AMT                         --20.投资金额（万元）
,T9.AVY_CGY_CD_DSC                                          as MDL_MOD                         --21.成交方式
,SUBSTR(T8.BSN_REC_DEAL_DT,1,10)                            as DEAL_DATE                       --22.成交日期
,COALESCE(T10.ITRSFEE_REPST_NM,T8.trsfee_nm)                                       as BUYER_NAME                      --23.投资方名称
,T11.ocpy_af_incptl_pct *100                                as INVESTOR_IVS_PCT                --24.投资方投资所占比例（%）
,T10.ECN_TP_CD_DSC                                          as INVESTOR_ECONOMY_TYPE           --25.投资方经济类型
,T.PRJ_BLNG_DEPT_NM                                         as PRJ_BLNG_DEPT                   --26.项目所属部门
,T.PRJ_PRIN_NM                                              as PRJ_PNP                         --27.项目负责人
,T.DEPT_PRIN_NM                                             as DEPT_PNP                        --28.部门负责人
,T.PRJ_STAT_CD_DSC                                          as PRJ_STS                         --29.项目状态
,T2.PROV_CD_DSC                                             as FINCER_SITE_PROV                --30.融资方所在地（省）
,T2.CITY_CD_DSC                                             as FINCER_SITE_CITY                --31.融资方所在地（市）
,T2.REGN_CNTY_CD_DSC                                        as FINCER_SITE_ZON_CNTY            --32.融资方所在地（区/县）
,T2.RGST_ADDR                                               as FINCER_ZONE                     --33.融资方所在地区
,T2.ECN_TP_CD_DSC                                           as FINCER_ECONOMY_TYPE             --34.融资方经济类型
,T2.CNTRY_SFEP_OR_MGR_DEPT_NM                               as FINCER_CTY_CONTRI_CORP_LEAD_DEPT--35.融资方国家出资企业或主管部门
,T.TXN_SVC_MBER_NM                                          as FINCER_BRKR_MBSH_NM             --36.融资方经纪会员名称
,T2.APRV_UNIT_FILE_TP_CD_DSC                                as AUTHORIZE_FILE_TYPE             --37.批准文件类型
,T5.PLAN_NEW_CPTL_MTH_CD_DSC                                as ICAP_MOD                        --38.增资方式
,T5.RAISE_CPTL_USE                                          as USE_OF_RAISED_FUNDS             --39.募集资金用途
,T5.PLAN_NEW_RGST_CPTL /10000                               as PREP_NEW_REG_CAPITAL            --40.拟新增注册资本（万元）
,T5.PCOLT_IVSR_NUM                                          as INTND_NEW_INVESTOR_NUM          --41.拟新增投资方数量
,T3.AF_INCPTL_ENTP_EQTY_STRUC                               as ICAP_AF_ENTP_STOCK              --42.增资后企业股权结构
,T2.RGST_CPTL /10000                                        as REG_CAPITAL                     --43.注册资本（万元）
,T2.REAL_INCM_CPTL /10000                                   as PAICL_CAPITAL                   --44.实收资本（万元）
,T3.SHRHDR_CNT                                              as SHRH_NUM                        --45.股东个数
,T3.WOKER_PNUM                                              as WORKERS_NUM                     --46.职工人数
,T3.OPRT_SCOP                                               as BUSINESS_SCOPE                  --47.经营范围
,CASE WHEN T3.IS_ORGN_SHRHDR_PCP_INCPTL = 0 THEN '否' WHEN T3.IS_ORGN_SHRHDR_PCP_INCPTL = 1 THEN '是' END	as ORIG_SHRH_IS_PCP_ICAP  --48.原股东是否参与增资
,CASE WHEN T3.IS_WOKER_PCP_INCPTL = 0 THEN '否' WHEN T3.IS_WOKER_PCP_INCPTL = 1 THEN '是' END   as EMP_IS_PCP_ICAP  --49.职工是否参与增资
,T1.INCPTL_SCHE_MAIN_CNTNT                                  as CAPITAL_PLAN                --50.增资方案主要内容
,T1.INCPTL_REAC_OR_TMT_COND                                 as ICAP_RCH_OR_SUSPSN_CD       --51.增资达成或中止条件
,T.INFO_PUBL_EXPRT_SHD_CMNT                                 as INFO_ANC_EXP_AR             --52.信息发布期满安排
,T3.FINAC_PRT_PCDS_FILE_TP_CD_DSC                           as FINCER_DS_MK_FILE_TP        --53.融资方决策文件类型
,T3.UNIT_RGST_CPTL_CRSP_EVAL                                as UNIT_REG_CAPITAL_CRPND_EVALU--54.单位注册资本对应评估值(元)
,T.OTHR_PUBL_ITM                                            as OTHR_ESR_ITM                --55.其他披露事项
,T.TRNSFR_QUA_COND                                          as INVESTOR_POSTULATE          --56.投资方资格条件
,T1.INCPTL_COND                                             as ICAP_CD                     --57.增资条件
,CASE WHEN T.MRGN_AMT IS NULL THEN T.MRGN_PCT *100||'%' ELSE T.MRGN_AMT||'万元'  END	 as   DEPOSIT_OR_PCT--58.保证金金额或比例（%）
,T.PAY_TM_CD_DSC							                              as PAY_TM                          --59.交纳时间
,T.MRGN_DISPL_MTH_CD_DSC                                    as DEPOSIT_PCSG_MOD                --60.保证金处理方式
,T.SELT_MTH_CD_DSC                                          as PREFER_MOD                      --61.择优方式
,T.SELT_SCHE_MAIN_CNTNT                                     as PREFER_SCM                      --62.择优方案
,SUBSTR(T8.RSLT_PUBTY_START_DT,1,10)                        as RSLT_PBC_STRT_TM                --63.结果公示开始时间
,SUBSTR(T8.RSLT_PUBTY_END_DT,1,10)                          as RSLT_PBC_END_TM                 --64.结果公示结束时间
,T14.COUNTT                                                 as INTNT_INVESTOR_NUM              --65.意向投资方数量
,T15.COUNTT                                                 as INTNT_INVESTOR_NUM_PAYED        --66.意向投资方数量（已交保）
,T6.OPRT_REVN /10000                                        as AUDT_RPT_BSN_INCM               --67.审计报告_营业收入（万元）
,T6.OPRT_PFT /10000                                         as AUDT_RPT_BSN_PFT                --68.审计报告_营业利润（万元）
,T6.TOT_AST /10000                                          as AUDT_RPT_ASSET_SUM              --69.审计报告_资产总计（万元）
,T6.TOT_LBY /10000                                          as AUDT_RPT_LIAB_SUM               --70.审计报告_负债总计（万元）
,T6.OWN_EQTY /10000                                         as AUDT_RPT_OWNER_EQUITY           --71.审计报告_所有者权益（万元）
,T7.OPRT_REVN /10000                                        as RCTLY_1_PRD_FIN_RPT_BSN_INCM    --72.最近一期财务报表_营业收入（万元）
,T7.OPRT_PFT /10000                                         as RCTLY_1_PRD_FIN_RPT_BSN_PFT     --73.最近一期财务报表_营业利润（万元）
,T7.TOT_AST /10000                                          as RCTLY_1_PRD_FIN_RPT_ASSET_SUM   --74.最近一期财务报表_资产总计（万元）
,T7.TOT_LBY /10000                                          as RCTLY_1_PRD_FIN_RPT_LIAB_SUM    --75.最近一期财务报表_负债总计（万元）
,T7.OWN_EQTY /10000                                         as RCTLY_1_PRD_FIN_RPT_OWNER_EQUITY--76.最近一期财务报表_所有者权益（万元）
,T10.STAT_CD_DSC                                            as INVESTOR_STS                    --77.投资方状态
,T11.INVST_MODE_CD_DSC                                      as IVS_WAY                         --78.投资方式
,T12.PY_MTH_CD_DSC                                          as PAY_MODE                        --79.支付方式
,T10.ITRSFEE_REPST_TP_CD_DSC                                as INVESTOR_TP                     --80.投资方类型
,T10.PROV_CD_DSC                                            as INVESTOR_SITE_PROV              --81.投资方所在地（省）
,T10.CITY_CD_DSC                                            as INVESTOR_SITE_CITY              --82.投资方所在地（市）
,T10.REGN_CNTY_CD_DSC                                       as INVESTOR_SITE_ZON_CNTY          --83.投资方所在地（区/县）
,T10.RGST_CPTL /10000                                       as INVESTOR_REG_CAPITAL            --84.投资方注册资本（万元）
,T10.OPRT_SCOP                                              as INVESTOR_BUSINESS_SCOPE         --85.投资方经营范围
,T10.MRGN_STAT_CD_DSC                                       as IS_DEPOSIT                      --86.是否交纳保证金
,T10.TXN_SVC_MBER_NM                                        as INVESTOR_BRKR_MBSH_NM           --87.投资方经纪会员名称
,T10.ORG_AUDIT_RSLT_CD_DSC                                  as INVESTOR_ACCEPT_RESULT          --88.投资方机构审核结果
,T11.NEW_PUCPL_CPTL /10000                                  as ADD_FNDD_AMT                    --89.增加出资额(万元)
,SUBSTR(T8.CONT_SIGN_DT,1,10)                               as SIGN_DT                         --90.签约日期
,CASE WHEN T3.IS_CENT_FINC_ENTP = '0' THEN '否' WHEN T3.IS_CENT_FINC_ENTP = '1' THEN '是' END	as  IS_CNTR_ENT  --91.是否央企
,T3.TOT_AST_ASES_VAL /10000                                 as ASSET_SUM_EVALU_VAL--92.资产总计/评估价值（万元）
,T13.SHRHDR_NM                                              as SHRH_NM            --93.股东名称
,T13.SHRHDR_TP_CD_DSC                                       as SHRH_TP            --94.股东类型
,T13.SRHD_RATIO_F *100                                      as HOLDER_PERCENT     --95.增资前出资比例（%）
,T13.SRHD_RATIO_A *100                                      as FINAL_RATIO        --96.增资后出资比例（%）
,T12.PLAN_IVS_CPTL_TOT_AMT /10000                           as PREP_INVEST_TOTAL  --97.拟投资资金总额(万元)
,T16.IVS_CPTL_TOT_AMT /10000                                as MDL_TOT_AMT        --98.成交总金额(万元)
,T17.OCPY_AF_INCPTL_PCT *100                                as TOT_RLSE_EQTY_PCT  --99.释放股权比例之和(%)
,NULL                                                       as PRJ_LBL            --100.项目标签
,CASE WHEN T10.ITRSFEE_REPST_NM LIKE '%基金%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%有限合伙%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%银行%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%证券%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%保险%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%信托%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%私募%' THEN '是'
	--WHEN T10.ITRSFEE_REPST_NM LIKE '%私募股权%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%风投%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%风险投资%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%资产管理公司%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%金融%' THEN '是'
	  WHEN T10.ITRSFEE_REPST_NM LIKE '%融资租赁%' THEN '是'
	  ELSE '否' END							                           as IS_FNC_CPTL_INVESTOR--101.是否为金融资本投资方
	  
FROM dwd.dwd_prj_fct T	--项目事实
LEFT JOIN (SELECT BSN_PRJ_WRD
				  ,INCPTL_SCHE_MAIN_CNTNT
				  ,INCPTL_REAC_OR_TMT_COND
				  ,INCPTL_COND
			FROM dwd.dwd_entp_incptl_prj_fct  --企业增资项目事实
			WHERE DT = '${dmp_day}') T1 
	ON T.BSN_PRJ_WRD=T1.BSN_PRJ_WRD
	
LEFT JOIN (SELECT BSN_PRJ_WRD
				  ,PRJ_PRT_NM
				  ,SOWN_SPVS_ORG_CD_DSC
				  ,PRJ_PRT_WRD
				  ,APRV_UNIT_NM
				  ,BLNG_IDY_TP_CD_DSC
				  ,BLNG_IDY_CD_DSC
				  ,PROV_CD_DSC
				  ,CITY_CD_DSC
				  ,REGN_CNTY_CD_DSC
				  ,RGST_ADDR
				  ,ECN_TP_CD_DSC
				  ,CNTRY_SFEP_OR_MGR_DEPT_NM
				  ,RGST_CPTL
				  ,REAL_INCM_CPTL
				  ,APRV_UNIT_FILE_TP_CD_DSC
				  
			FROM dim.dim_trsfer_info  --项目方信息维
			WHERE DT = '${dmp_day}'
			--AND EDW_STAR_DT <= '${dmp_day}'  
			--AND '${dmp_day}' <  EDW_END_DT 
			--AND  EDW_DLT_FLG <> 'D'
			) T2 
	ON T.BSN_PRJ_WRD=T2.BSN_PRJ_WRD

LEFT JOIN (SELECT PRJ_PRT_WRD
				  ,NETAST_BOOK_VAL
				  ,NETAST_ASES_VAL
				  ,AF_INCPTL_ENTP_EQTY_STRUC
				  ,SHRHDR_CNT
				  ,WOKER_PNUM
				  ,OPRT_SCOP
				  ,IS_ORGN_SHRHDR_PCP_INCPTL
				  ,IS_WOKER_PCP_INCPTL
				  ,FINAC_PRT_PCDS_FILE_TP_CD_DSC
				  ,UNIT_RGST_CPTL_CRSP_EVAL
				  ,IS_CENT_FINC_ENTP
				  ,TOT_AST_ASES_VAL
			FROM DIM.dim_finac_prt_info  --融资方信息维
			WHERE DT = '${dmp_day}'
			AND EDW_STAR_DT <= '${dmp_day}'  
			AND '${dmp_day}' <  EDW_END_DT 
			--AND  EDW_DLT_FLG <> 'D'
			) T3
	ON T2.PRJ_PRT_WRD=T3.PRJ_PRT_WRD
	
LEFT JOIN (SELECT BSN_PRJ_WRD
				  ,APRV_RCRD_ORG
				  ,PRJ_PRT_AST_WRD
			FROM dim.dim_prj_prt_ast_info  --项目方资产维
			WHERE DT = '${dmp_day}') T4
	ON T.BSN_PRJ_WRD=T4.BSN_PRJ_WRD
 
LEFT JOIN (SELECT PRJ_PRT_AST_WRD
				  ,PRAISE_CPTL_HSHARE_PCT
				  ,PLAN_RAISE_CPTL_AMT
				  ,PLAN_NEW_CPTL_MTH_CD_DSC
				  ,RAISE_CPTL_USE
				  ,PLAN_NEW_RGST_CPTL
				  ,PCOLT_IVSR_NUM
			FROM dim.dim_incptl_info  --增资维
			WHERE DT = '${dmp_day}'
			AND EDW_STAR_DT <= '${dmp_day}'  
			AND '${dmp_day}' <  EDW_END_DT 
			--AND  EDW_DLT_FLG <> 'D'
			) T5
	ON T4.PRJ_PRT_AST_WRD=T5.PRJ_PRT_AST_WRD
	
LEFT JOIN (SELECT  ROW_NUMBER ()OVER(PARTITION BY BSN_PRJ_WRD ORDER BY ANUL DESC) RN
				  ,BSN_PRJ_WRD
				  ,ANUL
				  ,NET_PFT
				  ,OPRT_REVN
				  ,OPRT_PFT
				  ,TOT_AST
				  ,TOT_LBY
				  ,OWN_EQTY
			FROM dim.dim_trgt_entp_audit_rpt_info  --标的企业审计报告维
			WHERE DT = '${dmp_day}'
			AND  EDW_STAR_DT <= '${dmp_day}'  
			AND '${dmp_day}' <  EDW_END_DT 
			--AND  EDW_DLT_FLG <> 'D'
			) T6		
	ON T.BSN_PRJ_WRD=T6.BSN_PRJ_WRD
	AND T6.RN =1 								--取年度最大的数据

LEFT JOIN (SELECT  ROW_NUMBER ()OVER(PARTITION BY BSN_PRJ_WRD ORDER BY RPT_DT DESC) RN
				  ,BSN_PRJ_WRD
				  ,SUBSTR(RPT_DT,1,10) AS RPT_DT
				  ,RPT_TP_CD_DSC
				  ,NET_PFT
				  ,OPRT_REVN
				  ,OPRT_PFT
				  ,TOT_AST
				  ,TOT_LBY
				  ,OWN_EQTY
			FROM dim.dim_fin_rpt_info  --标的企业财务报表维
			WHERE DT = '${dmp_day}'
			and EDW_STAR_DT <= '${dmp_day}'  
			AND '${dmp_day}' <  EDW_END_DT 
			--AND  EDW_DLT_FLG <> 'D'
			) T7
	ON T.BSN_PRJ_WRD=T7.BSN_PRJ_WRD
	AND T7.RN =1 								--取报表日期最大的数据

LEFT JOIN (SELECT BSN_PRJ_WRD
          ,trsfee_nm
				  ,BSN_BUYER_ID
				  ,BSN_DEAL_REC_ID
				  ,BSN_REC_DEAL_DT
				  ,RSLT_PUBTY_START_DT
				  ,RSLT_PUBTY_END_DT
				  ,CONT_SIGN_DT
			FROM dwd.dwd_evt_deal_rec_fct  --成交记录事实
			WHERE DT = '${dmp_day}') T8
	ON T.BSN_PRJ_WRD=T8.BSN_PRJ_WRD

LEFT JOIN (SELECT BSN_PRJ_WRD
				  ,AVY_CGY_CD_DSC
			FROM dwd.dwd_selt_check_rec_fct  --遴选勾选记录事实
			WHERE DT = '${dmp_day}') T9
	ON T.BSN_PRJ_WRD=T9.BSN_PRJ_WRD


LEFT JOIN (SELECT BSN_PRJ_WRD
				  ,BSN_BUYER_ID
				  ,ITRSFEE_REPST_NM
				  ,ECN_TP_CD_DSC
				  ,STAT_CD_DSC
				  ,ITRSFEE_REPST_TP_CD_DSC
				  ,PROV_CD_DSC
				  ,CITY_CD_DSC
				  ,REGN_CNTY_CD_DSC
				  ,RGST_CPTL
				  ,OPRT_SCOP
				  ,MRGN_STAT_CD_DSC
				  ,TXN_SVC_MBER_NM
				  ,ORG_AUDIT_RSLT_CD_DSC
			FROM dwd.dwd_ittn_buyer_fct  --意向买受方事实
			WHERE DT = '${dmp_day}') T10
	ON T8.BSN_PRJ_WRD=T10.BSN_PRJ_WRD 
	AND T8.BSN_BUYER_ID=T10.BSN_BUYER_ID

LEFT JOIN (SELECT BSN_DEAL_REC_ID
				  ,IVS_CPTL_TOT_AMT
				  ,NEW_PUCPL_CPTL
				  ,INVST_MODE_CD_DSC
          ,ocpy_af_incptl_pct
			FROM dwd.dwd_entp_incptl_deal_rec_fct  --企业增资成交记录事实
			WHERE DT = '${dmp_day}') T11
	ON T8.BSN_DEAL_REC_ID=T11.BSN_DEAL_REC_ID
	
LEFT JOIN (SELECT BSN_BUYER_ID
				  ,NEW_HOLD_SHARE_PCT
				  ,PY_MTH_CD_DSC
				  ,PLAN_IVS_CPTL_TOT_AMT
			FROM dwd.dwd_ittn_ivsr_fct  --意向投资方事实
			WHERE DT = '${dmp_day}') T12
	ON T10.BSN_BUYER_ID=T12.BSN_BUYER_ID

LEFT JOIN (SELECT 
				 'BJHL'||project_id||'QYZZ'               AS BSN_PRJ_WRD --项目ID/业务项目关键字 
				,shrh_nm                                  AS SHRHDR_NM --股东名称/股东名称 
				,shrh_tp_dsc                              AS SHRHDR_TP_CD_DSC --码值/股东类型代码描述 
				,fndd_pct*0.01                            AS SRHD_RATIO_F --出资比例（%）/持股比例(前) 
				,final_ratio*0.01                         AS SRHD_RATIO_A --增资后出资比例（%）/持股比例（后） 
				,icap_af_subscript_rgst_cptl_w_yuan*10000 AS AF_INCPTL_SUBRP_RGST_CPTL --增资后认缴注册资本（万元）
			FROM std.std_bjhl_tcgq_zzhgqjg_d  --增资挂牌项目-增资后融资方股权结构 
			WHERE dt = '${dmp_day}'
			) T13
	ON T10.BSN_PRJ_WRD=T13.BSN_PRJ_WRD
	AND T10.ITRSFEE_REPST_NM=T13.SHRHDR_NM

----------------------------------------------------------------------------
LEFT JOIN (SELECT T1.PLFORM_PRJ_ID
	  ,COUNT(*) AS COUNTT
	  ,T1.PRJ_ID

FROM dwd.dwd_prj_fct T1 --项目事实
INNER JOIN dwd.dwd_ittn_buyer_fct T3 --意向买受方事实
		ON  T1.BSN_PRJ_WRD=T3.BSN_PRJ_WRD
		AND T3.DT = '${dmp_day}' 
		AND T3.BSN_PRJ_WRD LIKE '%QYZZ'
WHERE T1.DT = '${dmp_day}' 
AND  T1.BSN_PRJ_WRD LIKE '%QYZZ'
GROUP BY T1.PLFORM_PRJ_ID,T1.PRJ_ID) T14
ON T.PLFORM_PRJ_ID = T14.PLFORM_PRJ_ID

LEFT JOIN (SELECT T1.PLFORM_PRJ_ID
	  ,COUNT(*) AS COUNTT
	  ,T1.PRJ_ID
FROM dwd.dwd_prj_fct T1 --项目事实
INNER JOIN dwd.dwd_ittn_buyer_fct T3 --意向买受方事实
		ON  T1.BSN_PRJ_WRD=T3.BSN_PRJ_WRD
		AND T3.DT = '${dmp_day}' 
		AND T3.BSN_PRJ_WRD LIKE '%QYZZ'
		AND T3.MRGN_STAT_CD_DSC='1'--已缴纳
WHERE T1.DT = '${dmp_day}' 
AND  T1.BSN_PRJ_WRD LIKE '%QYZZ'
GROUP BY T1.PLFORM_PRJ_ID,T1.PRJ_ID) T15
ON T.PLFORM_PRJ_ID = T15.PLFORM_PRJ_ID

-----------------------------------------------------------
LEFT JOIN (SELECT T1.PLFORM_PRJ_ID
	  ,SUM(T2.IVS_CPTL_TOT_AMT) AS IVS_CPTL_TOT_AMT	--所有投资方成交金额加和。按项目编号汇总，如果一个项目编号有多条数据，每条数据的“成交总金额”相同
	  ,T1.PRJ_ID
FROM dwd.dwd_prj_fct T1 --项目事实
INNER JOIN  dwd.dwd_entp_incptl_deal_rec_fct T2 --企业增资成交记录事实
		ON  T1.BSN_PRJ_WRD=T2.BSN_PRJ_WRD
		AND T2.DT = '${dmp_day}' 
WHERE T1.DT = '${dmp_day}' 
AND  T1.BSN_PRJ_WRD LIKE '%QYZZ'
GROUP BY T1.PLFORM_PRJ_ID,T1.PRJ_ID) T16
	ON T.PLFORM_PRJ_ID = T16.PLFORM_PRJ_ID
-----------------------------------------------------------
LEFT JOIN (SELECT T1.PLFORM_PRJ_ID
	  ,SUM(NVL(T2.OCPY_AF_INCPTL_PCT,0)) AS OCPY_AF_INCPTL_PCT--所有投资方的股权比例加和。按项目编号汇总，如果一个项目编号有多条数据，每条数据的“释放股权比例之和”相同
	  ,T1.PRJ_ID
FROM dwd.dwd_prj_fct T1 --项目事实
INNER JOIN  dwd.dwd_ittn_ivsr_fct T2 --意向投资方事实
		ON  T1.BSN_PRJ_WRD=T2.BSN_PRJ_WRD
		AND T2.DT = '${dmp_day}'
WHERE T1.DT = '${dmp_day}'
AND  T1.BSN_PRJ_WRD LIKE '%QYZZ'
GROUP BY T1.PLFORM_PRJ_ID,T1.PRJ_ID) T17
	ON T.PLFORM_PRJ_ID = T17.PLFORM_PRJ_ID

WHERE T.DT = '${dmp_day}'
AND  T.BSN_PRJ_WRD LIKE '%QYZZ'
-- AND T10.ITRSFEE_REPST_NM IS NOT NULL