-- Active: 1725412082987@@10.254.99.145@10000@std
WITH TMP_A AS ( 
----产权转让
SELECT       T1.XMID                 AS DEAL_REC_ID                      --成交记录号/成交记录ID
			,NULL                    AS PRJ_WRD                          --项目ID/项目关键字
            ,'BJHL'||T2.XMID||'CQZR' AS BSN_PRJ_WRD                      --项目/业务项目关键字
            ,TT.KHH                    AS CUST_WRD                         --客户号/客户关键字
            ,T2.SRFID                AS BSN_BUYER_ID                     --受让方/业务买受方ID
            ,T2.ID                   AS BSN_DEAL_REC_ID                  --ID/业务成交记录ID
            ,NULL                    AS DEAL_NO                          --成交编号/成交编号
            ,NULL                    AS ETRS_CGY_CD                      --委托类别/委托类别代码
            ,NULL                    AS ETRS_CGY_CD_DSC                  --码值/委托类别代码描述
            ,NULL                    AS CURR_CD                          --币种/币种代码
            ,NULL                    AS CURR_CD_DSC                      --码值/币种代码描述
            ,NULL                    AS CPTL_ACC                         --资金账户/资金账户
            ,NULL                    AS TXN_PRC                          --成交价格(元)/成交价格
            ,NULL                    AS DEAL_NUM                         --成交数量(手)/成交数量
            ,NULL                    AS DEAL_AMT                         --成交金额/成交金额
            ,NULL                    AS DEAL_DT                          --成交日期/成交日期
            ,NULL                    AS DEAL_TM                          --成交时间/成交时间
            ,SUBSTR(T2.CJRQ,1,10)                 AS BSN_REC_DEAL_DT                  --成交日期/业务记录成交日期
            ,NULL                    AS CFRM_DT                          --确认日期/确认日期
            ,NULL                    AS CFRM_TM                          --确认时间/确认时间
            ,NULL                    AS DELVRY_DT                        --交割日期/交割日期
            ,NULL                    AS DELVRY_TM                        --交割时间/交割时间
            ,NULL                    AS DEAL_STAT_CD                     --成交状态/成交状态代码
            ,NULL                    AS DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
            ,NULL                    AS IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
            ,NULL                    AS BROGE                            --手续费/手续费
            ,NULL                    AS THIRD_DEPST_SERIAL_NO            --第三方入金流水号/第三方入金流水号
            ,NULL                    AS PAY_STAT_CD                      --付款状态/付款状态代码
            ,NULL                    AS PAY_STAT_CD_DSC                  --码值/付款状态代码描述
            ,NULL                    AS CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
            ,NULL                    AS DELVER_ID                        --交割人/交割人ID
            ,NULL                    AS DELVER_NM                        --交割人姓名/交割人姓名
            ,T2.SFKYDY               AS IS_SURE_PRNT                     --是否可以打印/是否可以打印
            ,T2.DYFS                 AS TXN_VCHR_PRNT_PAGNMB             --交易凭证打印页数/交易凭证打印页数
            ,NULL                    AS PRNT_COPS                        --打印份数/打印份数
            ,T2.XMMC                 AS PRJ_NM                           --项目名称/项目名称
            ,T2.SRFMC                AS TRSFEE_NM                        --受让方名称/受让方名称
            ,REGEXP_REPLACE(T2.HTQDRQ ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                
			                         AS CONT_SIGN_DT                     --合同签订日期/合同签订日期
            ,REGEXP_REPLACE(T2.HTSXRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                 
			                         AS CONT_EFF_DT                      --合同生效日期/合同生效日期
            ,REGEXP_REPLACE(T2.JGGSQSRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')               
			                         AS RSLT_PUBTY_START_DT              --结果公示起始时间/结果公示起始日期
            ,REGEXP_REPLACE(T2.JGGSJSRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                
			                         AS RSLT_PUBTY_END_DT                --结果公示结束时间/结果公示结束日期
            ,T2.JGGSZQ               AS RSLT_PUBTY_PRD_WKDY              --结果公示周期（工作日）/结果公示周期（工作日）
            ,T2.ZZSRBL               AS FNL_TRNSFR_PCT                   --最终受让比例/最终受让比例
            ,NULL                    AS DEAL_UNIT_PRIC                   --NULL/成交单价
            ,T2.BZJZJK               AS IS_MRGN_OF_TXN_PRC               --保证金是否转交易价款/是否保证金转交易价款
            ,NULL                    AS MRGN_OF_TXN_PRC_AMT              --NULL/保证金转交易价款金额
            ,T2.BZJZJKJE*10000       AS MRGN_TF_PRC_AMT                  --保证金转价款金额(万元)/保证金转价款金额
            ,T2.BZJZJKSD             AS MRGN_TF_PRC_TMPOT                --保证金转价款时点/保证金转价款时点
            ,REGEXP_REPLACE(T2.BZJZJKRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                
			                         AS MRGN_TF_PRC_DT                         --保证金转价款日期/保证金转价款日期
            ,NULL                    AS AVALB_MRGN_AMT                   --NULL/可使用保证金金额
            ,NULL                    AS TOACCT_PRC_AMT                   --NULL/到账价款金额
            ,T2.BZJJE*10000          AS MRGN_AMT                         --保证金金额（万元）/保证金金额
            ,NULL                    AS MRGN_DISPL_MTH_CD                --NULL/保证金处置方式代码
            ,NULL                    AS MRGN_DISPL_MTH_CD_DSC            --NULL/保证金处置方式代码描述
            ,T2.BZJZEKDD             AS MRGN_TF_PRC_ORDER_ID             --保证金转价款订单ID/保证金转价款订单ID
            ,T2.BZJZJKSJ             AS MRGN_TF_PRC_TM                   --保证金转价款时间/保证金转价款时间
            ,T2.BZJTQHC              AS IS_MRGN_ADV_TSFT                 --保证金提前划出/是否保证金提前划出
            ,CASE WHEN T50.IS_CNVR='N' THEN T2.ZFFS ELSE CASE WHEN T50.CD_VAL IS NULL THEN 'WZ'||T2.ZFFS ELSE T50.CD_VAL END END  AS PY_MTH_CD --支付方式代码
            ,T6.NOTE                 AS PY_MTH_CD_DSC                    --码值/支付方式代码描述
            ,CASE WHEN T51.IS_CNVR='N' THEN T2.SYJKJSFS ELSE CASE WHEN T51.CD_VAL IS NULL THEN 'WZ'||T2.SYJKJSFS ELSE T51.CD_VAL END END AS SURPL_PRC_SETL_MTH_CD --剩余价款结算方式
            ,T7.NOTE                 AS SURPL_PRC_SETL_MTH_CD_DSC        --码值/剩余价款结算方式代码描述
            ,T2.SFJE*10000           AS DPAMT_AMT                        --首付金额(万元)/首付金额
            ,T2.SQFKBFB*0.01         AS FISSU_PAY_PECT                   --首期付款百分比/首期付款百分比
            ,REGEXP_REPLACE(T2.WKJZRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3') AS BLPT_EXPI_DT                     --尾款截止日期/尾款截止日期
            ,T2.SYYJNJK*10000        AS PAYB_PRC                         --应支付价款（万元）/应支付价款
            ,T2.KHCJE*10000          AS SCROT_PRC_AMT                    --可划出价款金额（万元）/可划出价款金额
            ,T2.QTYY                 AS OTHR_REASON                      --其他原因/其他原因
            ,CASE WHEN T52.IS_CNVR='N' THEN T2.JSFS ELSE CASE WHEN T52.CD_VAL IS NULL THEN 'WZ'||T2.JSFS ELSE T52.CD_VAL END END AS SETL_MTH_CD --结算方式
            ,T8.NOTE                 AS SETL_MTH_CD_DSC                  --码值/结算方式代码描述
            ,T2.SFWBJS               AS IS_FRN_CUR_SETL                  --是否外币结算/是否外币结算
            ,CASE WHEN T53.IS_CNVR='N' THEN T2.WBBZ ELSE CASE WHEN T53.CD_VAL IS NULL THEN 'WZ'||T2.WBBZ ELSE T53.CD_VAL END END AS FRN_CUR_CURR_CD --外币币种代码
            ,T9.BZMC                 AS FRN_CUR_CURR_CD_DSC              --码值/外币币种代码描述
            ,T2.YDRHL                AS AGDT_EXRT                        --约定日汇率/约定日汇率
            ,T2.ZSWBJE*10000         AS CONVT_FRN_CUR_AMT                --折算外币金额(万元)/折算外币金额
            ,CASE WHEN T54.IS_CNVR='N' THEN T2.SJJYFS ELSE CASE WHEN T54.CD_VAL IS NULL THEN 'WZ'||T2.SJJYFS ELSE T54.CD_VAL END END AS ACTL_TXN_MTH_CD  --实际交易方式代码
            ,T10.NOTE                AS ACTL_TXN_MTH_CD_DSC              --码值/实际交易方式代码描述
            ,T2.GGJYFSYY             AS CHAG_TXN_MTH_REASON              --更改交易方式原因/更改交易方式原因
            ,T2.JYJGSHYJ             AS TXN_ORG_AUDIT_OPIN               --交易机构审核意见/交易机构审核意见
            ,CASE WHEN T55.IS_CNVR='N' THEN T2.CJLX ELSE CASE WHEN T55.CD_VAL IS NULL THEN 'WZ'||T2.CJLX ELSE T55.CD_VAL END END AS DEAL_TP_CD --成交类型代码
            ,T11.NOTE                AS DEAL_TP_CD_DSC                   --码值/成交类型代码描述
            ,CASE WHEN T56.IS_CNVR='N' THEN T2.JKHCBS ELSE CASE WHEN T56.CD_VAL IS NULL THEN 'WZ'||T2.JKHCBS ELSE T56.CD_VAL END END AS PRC_TSFT_FLG_CD --价款划出标识代码
            ,T12.NOTE                AS PRC_TSFT_FLG_CD_DSC              --码值/价款划出标识代码描述
            ,T2.YHCJE*10000          AS ATSFT_AMT                        --已划出金额（万元）/已划出金额
            ,T2.JKHRDDID             AS PRC_GOIN_BSN_ORDER_ID            --价款划入业务订单ID/价款划入业务订单ID
            ,T2.JKHCDDBH             AS ORDER_NO_PLFORM                  --订单编号（平台）/订单编号（平台）
            ,T2.CJR                  AS CATR_ID                          --创建人/创建人ID
            ,T13.NAME                AS CATR_NM                          --名称/创建人名称
            ,T2.CJSJ                 AS CRT_TM                           --创建时间/创建时间
            ,T2.GXR                  AS UPD_PSN_ID                       --更新人/更新人ID
            ,T14.NAME                AS UPD_PSN_NM                       --名称/更新人名称
            ,T2.GXSJ                 AS MOD_TM                           --更新时间/更新时间
            ,NULL                    AS PRJ_PRIN_ID                      --NULL/项目负责人ID
            ,NULL                    AS PRJ_PRIN_NM                      --NULL/项目负责人名称
            ,NULL                    AS PRJ_PRIN_PASS_DT                 --NULL/项目负责人通过日期
            ,NULL                    AS BSN_DEPT_PRIN_ID                 --NULL/业务部门负责人ID
            ,NULL                    AS BSN_DEPT_PRIN_NM                 --NULL/业务部门负责人名称
            ,NULL                    AS BSN_DEPT_PRIN_PASS_DT            --NULL/业务部门负责人通过日期
            ,NULL                    AS TXN_AUDT_DEP_AUDITOR_ID          --NULL/交易审核部审核人ID
            ,NULL                    AS TXN_AUDT_DEP_AUDITOR_NM          --NULL/交易审核部审核人名称
            ,NULL                    AS TXN_AUDT_DEP_AUDITOR_PASS_DT     --NULL/交易审核部审核人通过日期
            ,NULL                    AS TRAS_DEP_PRIN_ID                 --NULL/交易部负责人ID
            ,NULL                    AS TRAS_DEP_PRIN_NM                 --NULL/交易部负责人名称
            ,NULL                    AS TRAS_DEP_PRIN_PASS_DT            --NULL/交易部负责人通过日期
            ,NULL                    AS EXG_AUDITOR_ID                   --NULL/交易所审核人ID
            ,NULL                    AS EXG_AUDITOR_NM                   --NULL/交易所审核人名称
            ,NULL                    AS EXG_AUDITOR_PASS_DT              --NULL/交易所审核人通过日期
            ,NULL                    AS CETR_PRIN_ID                     --NULL/中心负责人ID
            ,NULL                    AS CETR_PRIN_NM                     --NULL/中心负责人名称
            ,NULL                    AS CETR_PRIN_PASS_DT                --NULL/中心负责人通过日期
            ,T2.HTFJ                 AS CONT_ATCH                        --合同附件/合同附件
            ,T2.DYCSHSFTG            AS IS_FST_TM_PRC_TSFT_AUDIT_PASS    --第一次价款划出是否审核通过/是否第一次价款划出审核通过
            ,NULL                    AS DEAL_REC_STAT_CD                 --NULL/成交记录状态代码
            ,NULL                    AS DEAL_REC_SERIAL_NO               --NULL/成交记录流水号
            ,NULL                    AS DEAL_OVRL_RMRK                   --NULL/成交总体备注
            ,NULL                    AS RMRK                             --备注/备注
            ,T1.DT                   AS DT
  FROM ODS.ODS_BJHL_TCQZR_CQZRXXPL    T1  --
  LEFT JOIN (SELECT *
  		     FROM ODS.ODS_BJHL_TCQZR_CJJLXX
  		    WHERE DT = ${dmp_day} ) T2       --产权转让成交记录信息
    ON T1.ID=T2.XMID			
  LEFT JOIN (SELECT *
  		     FROM ODS.ODS_BJHL_TCQZR_YXSRFXX   --意向受让方信息(取客户号)
  		    WHERE  DT = ${dmp_day} ) TT
    ON T2.SRFID = TT.ID AND T2.XMID=TT.XMID
  LEFT JOIN (SELECT IBM  --代码 
                    ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='CQZR_CJ_ZFFS' ) T6
    ON T2.ZFFS=T6.IBM
  LEFT JOIN (SELECT IBM  --代码 
                    ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='CQZR_CJ_SYJKJSFS' ) T7
    ON T2.SYJKJSFS=T7.IBM
  LEFT JOIN (SELECT IBM  --代码 
                     ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='CQZR_JSFS' )  T8
    ON T2.JSFS=T8.IBM
  LEFT JOIN (SELECT BZ  ,--代码 
                    BZMC  --码值
               FROM ODS.ODS_BJHL_TBZ  --币种定义
  			WHERE DT = ${dmp_day} ) T9 
    ON T2.WBBZ=T9.BZ	
  LEFT JOIN (SELECT IBM  --代码 
                    ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLMC = '产权转让_实际交易方式' ) T10
    ON T2.SJJYFS=T10.IBM
  LEFT JOIN (SELECT IBM  --代码 
                     ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='CQZR_CJJL_CJLX'
             ) T11
    ON T2.CJLX=T11.IBM
  LEFT JOIN  (SELECT IBM  --代码 
                     ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='CQZR_JKHCBS'  ) T12
  ON         T2.JKHCBS=T12.IBM
  LEFT JOIN (SELECT ID  --代码 
                    ,NAME  --码值
               FROM ODS.ODS_BJHL_TUSER  --用户管理
  			WHERE DT = ${dmp_day} ) T13	   		  		   
    ON T2.CJR=T13.ID
  LEFT JOIN (SELECT ID  --代码 
                    ,NAME  --码值
               FROM ODS.ODS_BJHL_TUSER  --用户管理
  			WHERE DT = ${dmp_day}  ) T14	   		  		   
    ON T2.GXR=T14.ID
  -----------------------------------------------------------
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM  DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='cqzr.tcqzr_cjjlxx'   
                AND PUB_CD_NO= 'CD000039'
                AND SRC_COL_ENG_NM = 'ZFFS' ) T50
    ON T2.ZFFS = T50.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM  DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='cqzr.tcqzr_cjjlxx'    
                AND PUB_CD_NO= 'CD000146'
                AND SRC_COL_ENG_NM = 'SYJKJSFS') T51
  ON T2.SYJKJSFS = T51.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM  DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='cqzr.tcqzr_cjjlxx'    
                AND PUB_CD_NO= 'CD000146'
                AND SRC_COL_ENG_NM = 'JSFS') T52
    ON T2.JSFS = T52.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM  DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='cqzr.tcqzr_cjjlxx'    
                AND PUB_CD_NO= 'CD000016'
                AND SRC_COL_ENG_NM = 'WBBZ') T53
    ON T2.WBBZ = T53.SRC_CD_VAL 
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM  DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='cqzr.tcqzr_cjjlxx'    
                AND PUB_CD_NO= 'CD000035'
                AND SRC_COL_ENG_NM = 'SJJYFS') T54
    ON T2.SJJYFS = T54.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM  DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='cqzr.tcqzr_cjjlxx'    
                AND PUB_CD_NO= 'CD000158'
                AND SRC_COL_ENG_NM = 'CJLX' ) T55
    ON T2.CJLX = T55.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM  DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='cqzr.tcqzr_cjjlxx'    
                AND PUB_CD_NO= 'CD000159'
                AND SRC_COL_ENG_NM = 'JKHCBS') T56
    ON T2.JKHCBS = T56.SRC_CD_VAL 
 WHERE T1.DT = ${dmp_day}
 ------------------------------------------------------------------------------- -------------------------------------------------------------------------------
 union ALL
 ----大宗实物
SELECT      T1.XMID                 AS DEAL_REC_ID                      --成交记录号/成交记录ID
            ,NULL                      AS PRJ_WRD                          --项目ID/项目关键字
            ,'BJHL'||T2.XM||'DZSW'     AS BSN_PRJ_WRD                      --项目/业务项目关键字
            ,TT.KHH                      AS CUST_WRD                         --客户号/客户关键字
            ,T2.SRF                    AS BSN_BUYER_ID                     --受让方/业务买受方ID
            ,T2.ID                     AS BSN_DEAL_REC_ID                  --ID/业务成交记录ID
            ,NULL                      AS DEAL_NO                          --成交编号/成交编号
            ,NULL                      AS ETRS_CGY_CD                      --委托类别/委托类别代码
            ,NULL                      AS ETRS_CGY_CD_DSC                  --码值/委托类别代码描述
            ,NULL                      AS CURR_CD                          --币种/币种代码
            ,NULL                      AS CURR_CD_DSC                      --码值/币种代码描述
            ,NULL                      AS CPTL_ACC                         --资金账户/资金账户
            ,NULL                      AS TXN_PRC                          --成交价格(元)/成交价格
            ,NULL                      AS DEAL_NUM                         --成交数量(手)/成交数量
            ,NULL                      AS DEAL_AMT                         --成交金额/成交金额
            ,NULL                      AS DEAL_DT                          --成交日期/成交日期
            ,NULL                      AS DEAL_TM                          --成交时间/成交时间
            ,REGEXP_REPLACE(T2.CJRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                   
			                           AS BSN_REC_DEAL_DT                  --成交日期/业务记录成交日期
            ,NULL                      AS CFRM_DT                          --确认日期/确认日期
            ,NULL                      AS CFRM_TM                          --确认时间/确认时间
            ,NULL                      AS DELVRY_DT                        --交割日期/交割日期
            ,NULL                      AS DELVRY_TM                        --交割时间/交割时间
            ,NULL                      AS DEAL_STAT_CD                     --成交状态/成交状态代码
            ,NULL                      AS DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
            ,NULL                      AS IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
            ,NULL                      AS BROGE                            --手续费/手续费
            ,NULL                      AS THIRD_DEPST_SERIAL_NO            --第三方入金流水号/第三方入金流水号
            ,NULL                      AS PAY_STAT_CD                      --付款状态/付款状态代码
            ,NULL                      AS PAY_STAT_CD_DSC                  --码值/付款状态代码描述
            ,NULL                      AS CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
            ,NULL                      AS DELVER_ID                        --交割人/交割人ID
            ,NULL                      AS DELVER_NM                        --交割人姓名/交割人姓名
            ,T2.SFKDY                  AS IS_SURE_PRNT                     --是否可以打印/是否可以打印
            ,T2.JYPZDYYS               AS TXN_VCHR_PRNT_PAGNMB             --交易凭证打印页数/交易凭证打印页数
            ,NULL                      AS PRNT_COPS                        --打印份数/打印份数
            ,NULL                      AS PRJ_NM                           --NULL/项目名称
            ,NULL                      AS TRSFEE_NM                        --NULL/受让方名称
            ,T2.HTQDRQ                 AS CONT_SIGN_DT                     --合同签订日期/合同签订日期
            ,T2.HTSXRQ                 AS CONT_EFF_DT                      --合同生效日期/合同生效日期
            ,REGEXP_REPLACE(T2.JGGSKSRQ ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                 
			                           AS RSLT_PUBTY_START_DT              --结果公示起始时间/结果公示起始日期
            ,REGEXP_REPLACE(T2.JGGSJSRQ ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                 
			                           AS RSLT_PUBTY_END_DT                --结果公示结束时间/结果公示结束日期
            ,T2.JGGSZQ                 AS RSLT_PUBTY_PRD_WKDY              --结果公示周期（工作日）/结果公示周期（工作日）
            ,NULL                      AS FNL_TRNSFR_PCT                   --NULL/最终受让比例
            ,NULL                      AS DEAL_UNIT_PRIC                   --NULL/成交单价
            ,T2.SFZJK                  AS IS_MRGN_OF_TXN_PRC               --保证金是否转交易价款/是否保证金转交易价款
            ,T2.ZJKJE*10000            AS MRGN_OF_TXN_PRC_AMT              --NULL/保证金转交易价款金额
            ,T2.BZJZJKJE*10000         AS MRGN_TF_PRC_AMT                  --保证金转价款金额(万元)/保证金转价款金额
            ,NULL                      AS MRGN_TF_PRC_TMPOT                --NULL/保证金转价款时点
            ,NULL                      AS MRGN_TF_PRC_DT                   --NULL/保证金转价款日期
            ,NULL                      AS AVALB_MRGN_AMT                   --NULL/可使用保证金金额
            ,T2.DZJKJE*10000           AS TOACCT_PRC_AMT                   --NULL/到账价款金额
            ,T2.BZJJE*10000            AS MRGN_AMT                         --保证金金额（万元）/保证金金额
            ,NULL                      AS MRGN_DISPL_MTH_CD                --NULL/保证金处置方式代码
            ,NULL                      AS MRGN_DISPL_MTH_CD_DSC            --NULL/保证金处置方式代码描述
            ,T2.BZJZJKDDID             AS MRGN_TF_PRC_ORDER_ID             --保证金转价款订单ID/保证金转价款订单ID
            ,T2.BZJZJKSJ               AS MRGN_TF_PRC_TM                   --保证金转价款时间/保证金转价款时间
            ,NULL                      AS IS_MRGN_ADV_TSFT                 --NULL/是否保证金提前划出
            ,CASE WHEN T50.IS_CNVR='N' THEN T2.ZFFS ELSE CASE WHEN T50.CD_VAL IS NULL THEN 'WZ'||T2.ZFFS ELSE T50.CD_VAL END END AS PY_MTH_CD --支付方式代码
            ,T6.NOTE                   AS PY_MTH_CD_DSC                    --码值/支付方式代码描述
            ,CASE WHEN T51.IS_CNVR='N' THEN T2.SYJKCZ ELSE CASE WHEN T51.CD_VAL IS NULL THEN 'WZ'||T2.SYJKCZ ELSE T51.CD_VAL END END  AS SURPL_PRC_SETL_MTH_CD --剩余价款结算方式代码
            ,CASE WHEN T2.SYJKCZ = '1' THEN '场内结算'
			       WHEN T2.SYJKCZ = '2' THEN '场外结算'
				   WHEN T2.SYJKCZ = '2' THEN '其他'END  AS SURPL_PRC_SETL_MTH_CD_DSC        --NULL/剩余价款结算方式代码描述
            ,T2.SFJE*10000             AS DPAMT_AMT                        --首付金额(万元)/首付金额
            ,T2.SQFKBL*0.01            AS FISSU_PAY_PECT                   --首期付款百分比/首期付款百分比
            ,T2.WKJZRQ                 AS BLPT_EXPI_DT                     --尾款截止日期/尾款截止日期
            ,T2.JKJE*10000             AS PAYB_PRC                         --应支付价款（万元）/应支付价款
            ,NULL                      AS SCROT_PRC_AMT                    --NULL/可划出价款金额
            ,T2.QTYY                   AS OTHR_REASON                      --其他原因/其他原因
            ,CASE WHEN T52.IS_CNVR='N' THEN T2.JSFS ELSE CASE WHEN T52.CD_VAL IS NULL THEN 'WZ'||T2.JSFS ELSE T52.CD_VAL END END AS SETL_MTH_CD --结算方式代码
            ,T8.NOTE                   AS SETL_MTH_CD_DSC                  --码值/结算方式代码描述
            ,T2.SFWBJS                 AS IS_FRN_CUR_SETL                  --是否外币结算/是否外币结算
            ,CASE WHEN T53.IS_CNVR='N' THEN T2.WBBZ ELSE CASE WHEN T53.CD_VAL IS NULL THEN 'WZ'||T2.WBBZ ELSE T53.CD_VAL END END  AS FRN_CUR_CURR_CD --外币币种代码
            ,T9.BZMC                   AS FRN_CUR_CURR_CD_DSC              --码值/外币币种代码描述
            ,T2.YDRHL                  AS AGDT_EXRT                        --约定日汇率/约定日汇率
            ,T2.ZSWBJE*10000           AS CONVT_FRN_CUR_AMT                --折算外币金额(万元)/折算外币金额
            ,CASE WHEN T54.IS_CNVR='N' THEN T2.SJJYFS ELSE CASE WHEN T54.CD_VAL IS NULL THEN 'WZ'||T2.SJJYFS ELSE T54.CD_VAL END END AS ACTL_TXN_MTH_CD --实际交易方式
            ,T10.NOTE                  AS ACTL_TXN_MTH_CD_DSC              --码值/实际交易方式代码描述
            ,T2.GGJYFSYY               AS CHAG_TXN_MTH_REASON              --更改交易方式原因/更改交易方式原因
            ,T2.JYJGSHYJ               AS TXN_ORG_AUDIT_OPIN               --交易机构审核意见/交易机构审核意见
            ,NULL                      AS DEAL_TP_CD                       --NULL/成交类型代码
            ,NULL                      AS DEAL_TP_CD_DSC                   --NULL/成交类型代码描述
            ,CASE WHEN T55.IS_CNVR='N' THEN T2.HCBS ELSE CASE WHEN T55.CD_VAL IS NULL THEN 'WZ'||T2.HCBS ELSE T55.CD_VAL END END AS PRC_TSFT_FLG_CD --价款划出标识代码
            ,T12.NOTE                  AS PRC_TSFT_FLG_CD_DSC              --码值/价款划出标识代码描述
            ,T2.HCJE*10000             AS ATSFT_AMT                        --已划出金额（万元）/已划出金额
            ,NULL                      AS PRC_GOIN_BSN_ORDER_ID            --NULL/价款划入业务订单ID
            ,NULL                      AS ORDER_NO_PLFORM                  --NULL/订单编号（平台）
            ,T2.CJR                    AS CATR_ID                          --创建人/创建人ID
            ,T13.NAME                  AS CATR_NM                          --名称/创建人名称
            ,T2.CJSJ                   AS CRT_TM                           --创建时间/创建时间
            ,T2.GXR                    AS UPD_PSN_ID                       --更新人/更新人ID
            ,T14.NAME                  AS UPD_PSN_NM                       --名称/更新人名称
            ,T2.GXSJ                   AS MOD_TM                           --更新时间/更新时间
            ,NULL                      AS PRJ_PRIN_ID                      --NULL/项目负责人ID
            ,NULL                      AS PRJ_PRIN_NM                      --NULL/项目负责人名称
            ,NULL                      AS PRJ_PRIN_PASS_DT                 --NULL/项目负责人通过日期
            ,NULL                      AS BSN_DEPT_PRIN_ID                 --NULL/业务部门负责人ID
            ,NULL                      AS BSN_DEPT_PRIN_NM                 --NULL/业务部门负责人名称
            ,NULL                      AS BSN_DEPT_PRIN_PASS_DT            --NULL/业务部门负责人通过日期
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_ID          --NULL/交易审核部审核人ID
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_NM          --NULL/交易审核部审核人名称
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_PASS_DT     --NULL/交易审核部审核人通过日期
            ,NULL                      AS TRAS_DEP_PRIN_ID                 --NULL/交易部负责人ID
            ,NULL                      AS TRAS_DEP_PRIN_NM                 --NULL/交易部负责人名称
            ,NULL                      AS TRAS_DEP_PRIN_PASS_DT            --NULL/交易部负责人通过日期
            ,NULL                      AS EXG_AUDITOR_ID                   --NULL/交易所审核人ID
            ,NULL                      AS EXG_AUDITOR_NM                   --NULL/交易所审核人名称
            ,NULL                      AS EXG_AUDITOR_PASS_DT              --NULL/交易所审核人通过日期
            ,NULL                      AS CETR_PRIN_ID                     --NULL/中心负责人ID
            ,NULL                      AS CETR_PRIN_NM                     --NULL/中心负责人名称
            ,NULL                      AS CETR_PRIN_PASS_DT                --NULL/中心负责人通过日期
            ,NULL                      AS CONT_ATCH                        --NULL/合同附件
            ,NULL                      AS IS_FST_TM_PRC_TSFT_AUDIT_PASS    --NULL/是否第一次价款划出审核通过
            ,NULL                      AS DEAL_REC_STAT_CD                 --NULL/成交记录状态代码
            ,NULL                      AS DEAL_REC_SERIAL_NO               --NULL/成交记录流水号
            ,NULL                      AS DEAL_OVRL_RMRK                   --NULL/成交总体备注
            ,NULL                      AS RMRK                             --备注/备注
             ,T1.DT                   AS DT
  FROM ODS.ODS_BJHL_TDZSW_XXPL    T1       --大宗实物信息披露
  LEFT JOIN (SELECT *
               FROM ODS.ODS_BJHL_TDZSW_CJJL
			  WHERE DT = ${dmp_day}  )   T2       --大宗实物成交记录信息
    ON T1.ID=T2.XM
  LEFT JOIN (SELECT *
               FROM ODS.ODS_BJHL_TDZSW_YXSRFXX	--意向受让方信息
			  WHERE DT = ${dmp_day}  )   TT       
    ON T2.SRF=TT.ID
  LEFT JOIN (SELECT IBM  --代码 
                     ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='DZSW_JKZFFS' ) T6
    ON T2.ZFFS=T6.IBM
  LEFT JOIN (SELECT IBM  --代码 
                    ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='DZSW_CJZFFS' ) T8
    ON T2.JSFS=T8.IBM
  LEFT JOIN (SELECT BZ  ,--代码 
                    BZMC  --码值
               FROM ODS.ODS_BJHL_TBZ  --币种定义
  			WHERE DT = ${dmp_day} ) T9 
    ON T2.WBBZ=T9.BZ	
  LEFT JOIN (SELECT IBM  --代码 
                    ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='DZSW_JYFS' ) T10
    ON T2.SJJYFS=T10.IBM
  LEFT JOIN (SELECT IBM  --代码 
                    ,NOTE  --码值
               FROM ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
  			WHERE DT = ${dmp_day}
  			  AND FLDM='DZSW_HCBS' ) T12
    ON T2.HCBS=T12.IBM
  LEFT JOIN (SELECT ID  --代码 
                    ,NAME  --码值
               FROM ODS.ODS_BJHL_TUSER  --用户管理
  			WHERE DT = ${dmp_day} ) T13	   		  		   
    ON T2.CJR=T13.ID
  LEFT JOIN (SELECT ID  --代码 
                    ,NAME  --码值
               FROM ODS.ODS_BJHL_TUSER  --用户管理
  			WHERE DT = ${dmp_day} ) T14	   		  		   
    ON T2.GXR=T14.ID
  --------------------------------------------------------------
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='dzsw.tdzsw_cjjl'   
               AND PUB_CD_NO= 'CD000039'
               AND SRC_COL_ENG_NM = 'ZFFS' ) T50
    ON T2.ZFFS = T50.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='dzsw.tdzsw_cjjl'   
                AND PUB_CD_NO= 'CD000146'
                AND SRC_COL_ENG_NM = 'SYJKCZ' ) T51
    ON T2.SYJKCZ = T51.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='dzsw.tdzsw_cjjl'   
                AND PUB_CD_NO= 'CD000146'
                AND SRC_COL_ENG_NM = 'JSFS' ) T52
    ON T2.JSFS = T52.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='dzsw.tdzsw_cjjl'   
                AND PUB_CD_NO= 'CD000016'
                AND SRC_COL_ENG_NM = 'WBBZ' ) T53
    ON T2.WBBZ = T53.SRC_CD_VAL 
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='dzsw.tdzsw_cjjl'   
                AND PUB_CD_NO= 'CD000035'
                AND SRC_COL_ENG_NM = 'SJJYFS' ) T54
    ON T2.SJJYFS = T54.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
              FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
             WHERE SRC_TAB_ENG_NM='dzsw.tdzsw_cjjl'   
               AND PUB_CD_NO= 'CD000159'
               AND SRC_COL_ENG_NM = 'HCBS' ) T55
    ON T2.HCBS = T55.SRC_CD_VAL
 WHERE T1.DT = ${dmp_day}
  ------------------------------------------------------------------------------- -------------------------------------------------------------------------------
 union ALL
 ----增资挂牌
SELECT      T1.XMID                   AS DEAL_REC_ID                      --成交记录号/成交记录ID
            ,NULL                      AS PRJ_WRD                          --项目ID/项目关键字
            ,'BJHL'||T2.T_XM||'QYZZ'   AS BSN_PRJ_WRD                      --项目/业务项目关键字
            ,T3.KHH                    AS CUST_WRD                         --客户号/客户关键字
			,T2.T_TZF                  AS BSN_BUYER_ID                     --投资方/业务买受方ID
            ,T2.ID                     AS BSN_DEAL_REC_ID                  --ID/业务成交记录ID
            ,NULL                      AS DEAL_NO                          --成交编号/成交编号
            ,NULL                      AS ETRS_CGY_CD                      --委托类别/委托类别代码
            ,NULL                      AS ETRS_CGY_CD_DSC                  --码值/委托类别代码描述
            ,NULL                      AS CURR_CD                          --币种/币种代码
            ,NULL                      AS CURR_CD_DSC                      --码值/币种代码描述
            ,NULL                      AS CPTL_ACC                         --资金账户/资金账户
            ,NULL                      AS TXN_PRC                          --成交价格(元)/成交价格
            ,NULL                      AS DEAL_NUM                         --成交数量(手)/成交数量
            ,NULL                      AS DEAL_AMT                         --成交金额/成交金额
            ,NULL                      AS DEAL_DT                          --成交日期/成交日期
            ,NULL                      AS DEAL_TM                          --成交时间/成交时间
            ,substr(T1.CJRQ,1,10)                   AS BSN_REC_DEAL_DT                  --成交日期/业务记录成交日期
            ,NULL                      AS CFRM_DT                          --确认日期/确认日期 
            ,NULL                      AS CFRM_TM                          --确认时间/确认时间
            ,NULL                      AS DELVRY_DT                        --交割日期/交割日期
            ,NULL                      AS DELVRY_TM                        --交割时间/交割时间
            ,NULL                      AS DEAL_STAT_CD                     --成交状态/成交状态代码
            ,NULL                      AS DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
            ,NULL                      AS IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
            ,NULL                      AS BROGE                            --手续费/手续费
            ,NULL                      AS THIRD_DEPST_SERIAL_NO            --第三方入金流水号/第三方入金流水号
            ,NULL                      AS PAY_STAT_CD                      --付款状态/付款状态代码
            ,NULL                      AS PAY_STAT_CD_DSC                  --码值/付款状态代码描述
            ,NULL                      AS CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
            ,NULL                      AS DELVER_ID                        --交割人/交割人ID
            ,NULL                      AS DELVER_NM                        --交割人姓名/交割人姓名
            ,NULL                      AS IS_SURE_PRNT                     --NULL/是否可以打印
            ,T2.JYPZDYYS               AS TXN_VCHR_PRNT_PAGNMB             --交易凭证打印页数/交易凭证打印页数
            ,NULL                      AS PRNT_COPS                        --打印份数/打印份数
            ,NULL                      AS PRJ_NM                           --NULL/项目名称
            ,T2.TZFMC                  AS TRSFEE_NM                        --投资方名称/受让方名称
            ,T2.QYRQ                   AS CONT_SIGN_DT                     --签约日期/合同签订日期
            ,REGEXP_REPLACE(T2.HTSXRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                 
			                           AS CONT_EFF_DT                      --合同生效日期/合同生效日期
            ,T1.JGGSKSSJ               AS RSLT_PUBTY_START_DT              --结果公示开始时间/结果公示起始日期
            ,T1.JGGSJSSJ               AS RSLT_PUBTY_END_DT                --结果公示结束时间/结果公示结束日期
            ,T1.JGGSZQ                 AS RSLT_PUBTY_PRD_WKDY              --结果公示周期（工作日）/结果公示周期（工作日）
            ,NULL                      AS FNL_TRNSFR_PCT                   --NULL/最终受让比例
            ,NULL                      AS DEAL_UNIT_PRIC                   --NULL/成交单价
            ,T2.BZJSFZZZK              AS IS_MRGN_OF_TXN_PRC               --保证金是否转增资款/是否保证金转交易价款
            ,NULL                      AS MRGN_OF_TXN_PRC_AMT              --NULL/保证金转交易价款金额
            ,T4.YZJKJE                 AS MRGN_TF_PRC_AMT                  --NULL/保证金转价款金额
            ,NULL                      AS MRGN_TF_PRC_TMPOT                --NULL /保证金转价款时点
            ,NULL                      AS MRGN_TF_PRC_DT                   --NULL /保证金转价款日期
            ,T2.KSYBZJJE*10000         AS AVALB_MRGN_AMT                   --可使用保证金金额/可使用保证金金额
            ,NULL                      AS TOACCT_PRC_AMT                   --NULL/到账价款金额
            ,T2.BZJJE*10000            AS MRGN_AMT                         --保证金金额/保证金金额
            ,NULL                      AS MRGN_DISPL_MTH_CD                --NULL/保证金处置方式代码
            ,NULL                      AS MRGN_DISPL_MTH_CD_DSC            --NULL/保证金处置方式代码描述
            ,T4.ZFBH                   AS MRGN_TF_PRC_ORDER_ID             --NULL/保证金转价款订单ID(这里已经是平台ID，可直接关联订单明细中的订单编号)
            ,T4.BZJZJKSJ               AS MRGN_TF_PRC_TM                   --NULL/保证金转价款时间
            ,NULL                      AS IS_MRGN_ADV_TSFT                 --NULL/是否保证金提前划出
            ,CASE WHEN T50.IS_CNVR='N' THEN T2.FKFS ELSE CASE WHEN T50.CD_VAL IS NULL THEN 'WZ'||T2.FKFS ELSE T50.CD_VAL END END  AS PY_MTH_CD --支付方式
            ,CASE WHEN T2.FKFS = '1' THEN '一次性付款'
			      WHEN T2.FKFS = '2' THEN '分期付款'
		    END                        AS PY_MTH_CD_DSC                       --码值/支付方式代码描述
            ,NULL                      AS SURPL_PRC_SETL_MTH_CD            --NULL/剩余价款结算方式代码
            ,NULL                      AS SURPL_PRC_SETL_MTH_CD_DSC        --NULL/剩余价款结算方式代码描述
            ,NULL                      AS DPAMT_AMT                        --NULL/首付金额
            ,NULL                      AS FISSU_PAY_PECT                   --NULL/首期付款百分比
            ,NULL                      AS BLPT_EXPI_DT                     --NULL/尾款截止日期
            ,NULL                      AS PAYB_PRC                         --NULL/应支付价款
            ,NULL                      AS SCROT_PRC_AMT                    --NULL/可划出价款金额
            ,NULL                      AS OTHR_REASON                      --NULL/其他原因
            ,CASE WHEN T51.IS_CNVR='N' THEN T2.JSFS ELSE CASE WHEN T51.CD_VAL IS NULL THEN 'WZ'||T2.JSFS ELSE T51.CD_VAL END END AS SETL_MTH_CD --结算方式
            ,CASE WHEN T2.JSFS = '1' THEN '场内结算'
			      WHEN T2.JSFS = '2' THEN '场外结算'
				  WHEN T2.JSFS = '3' THEN '无结算' END                        AS SETL_MTH_CD_DSC                  --码值/结算方式代码描述
            ,NULL                      AS IS_FRN_CUR_SETL                  --NULL/是否外币结算
            ,NULL                      AS FRN_CUR_CURR_CD                  --NULL/外币币种代码
            ,NULL                      AS FRN_CUR_CURR_CD_DSC              --NULL/外币币种代码描述
            ,NULL                      AS AGDT_EXRT                        --NULL/约定日汇率
            ,NULL                      AS CONVT_FRN_CUR_AMT                --NULL/折算外币金额
            ,NULL                      AS ACTL_TXN_MTH_CD                  --NULL/实际交易方式代码
            ,NULL                      AS ACTL_TXN_MTH_CD_DSC              --NULL/实际交易方式代码描述
            ,NULL                      AS CHAG_TXN_MTH_REASON              --NULL/更改交易方式原因
            ,NULL                      AS TXN_ORG_AUDIT_OPIN               --NULL/交易机构审核意见
            ,NULL                      AS DEAL_TP_CD                       --NULL/成交类型代码
            ,NULL                      AS DEAL_TP_CD_DSC                   --NULL/成交类型代码描述
            ,NULL                      AS PRC_TSFT_FLG_CD                  --NULL/价款划出标识代码
            ,NULL                      AS PRC_TSFT_FLG_CD_DSC              --NULL/价款划出标识代码描述
            ,NULL                      AS ATSFT_AMT                        --NULL/已划出金额
            ,NULL                      AS PRC_GOIN_BSN_ORDER_ID            --NULL/价款划入业务订单ID
            ,T2.JKHCDD                 AS ORDER_NO_PLFORM                  --价款划出订单/订单编号（平台）
            ,T2.CJR                    AS CATR_ID                          --创建人/创建人ID
            ,T13.NAME                  AS CATR_NM                          --名称/创建人名称
            ,T2.CJSJ                   AS CRT_TM                           --创建时间/创建时间
            ,T2.GXR                    AS UPD_PSN_ID                       --更新人/更新人ID
            ,T14.NAME                  AS UPD_PSN_NM                       --名称/更新人名称
            ,T2.GXSJ                   AS MOD_TM                           --更新时间/更新时间
            ,NULL                      AS PRJ_PRIN_ID                      --NULL/项目负责人ID
            ,NULL                      AS PRJ_PRIN_NM                      --NULL/项目负责人名称
            ,NULL                      AS PRJ_PRIN_PASS_DT                 --NULL/项目负责人通过日期
            ,NULL                      AS BSN_DEPT_PRIN_ID                 --NULL/业务部门负责人ID
            ,NULL                      AS BSN_DEPT_PRIN_NM                 --NULL/业务部门负责人名称
            ,NULL                      AS BSN_DEPT_PRIN_PASS_DT            --NULL/业务部门负责人通过日期
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_ID          --NULL/交易审核部审核人ID
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_NM          --NULL/交易审核部审核人名称
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_PASS_DT     --NULL/交易审核部审核人通过日期
            ,NULL                      AS TRAS_DEP_PRIN_ID                 --NULL/交易部负责人ID
            ,NULL                      AS TRAS_DEP_PRIN_NM                 --NULL/交易部负责人名称
            ,NULL                      AS TRAS_DEP_PRIN_PASS_DT            --NULL/交易部负责人通过日期
            ,NULL                      AS EXG_AUDITOR_ID                   --NULL/交易所审核人ID
            ,NULL                      AS EXG_AUDITOR_NM                   --NULL/交易所审核人名称
            ,NULL                      AS EXG_AUDITOR_PASS_DT              --NULL/交易所审核人通过日期
            ,NULL                      AS CETR_PRIN_ID                     --NULL/中心负责人ID
            ,NULL                      AS CETR_PRIN_NM                     --NULL/中心负责人名称
            ,NULL                      AS CETR_PRIN_PASS_DT                --NULL/中心负责人通过日期
            ,NULL                      AS CONT_ATCH                        --NULL/合同附件
            ,NULL                      AS IS_FST_TM_PRC_TSFT_AUDIT_PASS    --NULL/是否第一次价款划出审核通过
            ,NULL                      AS DEAL_REC_STAT_CD                 --NULL/成交记录状态代码
            ,T2.LSH                    AS DEAL_REC_SERIAL_NO               --成交记录的流水号/成交记录流水号
            ,T1.BZ                     AS DEAL_OVRL_RMRK                   --成交总体备注/成交总体备注
            ,NULL                      AS RMRK                             --备注/备注
             ,T1.DT                   AS DT
  FROM ODS.ODS_BJHL_TCGQ_ZZZSGPXM  T1 --增资挂牌项目
  LEFT JOIN (SELECT *
               FROM   ODS.ODS_BJHL_TCGQ_CJJL    T2       --企业增资成交记录
              WHERE DT = ${dmp_day} ) T2
    ON T1.ID=T2.T_XM	
  LEFT JOIN (SELECT ID  --项目ID
                    ,KHH  --关联投资人
               FROM ODS.ODS_BJHL_TCGQ_YXTZFXX           --企业增资投资方意向信息
              WHERE DT = ${dmp_day} ) T3
    ON T2.T_TZF=T3.ID	
  LEFT JOIN (
    SELECT XMID,YZJKJE,BZJZJKSJ,ZFBH,KHID FROM ODS.ODS_BJHL_TCGQ_ZFXW  --  支付支付信息表
    WHERE  DT = '${dmp_day}' AND ZFLX = 0 AND DZQK = 1
  ) T4  		                                          
  ON T2.T_XM = T4.XMID AND T3.KHH = T4.KHID  
  LEFT JOIN (SELECT ID  --代码 
                     ,NAME  --码值
               FROM ODS.ODS_BJHL_TUSER --用户管理
  			  WHERE DT = ${dmp_day} ) T13	   		  		   
    ON T2.CJR=T13.ID
  LEFT JOIN (SELECT ID  --代码 
                    ,NAME  --码值
               FROM ODS.ODS_BJHL_TUSER  --用户管理
  			  WHERE DT = ${dmp_day} ) T14	   		  		   
    ON T2.GXR=T14.ID   		 
----------------------------------------------------------
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='bid.tcgq_cjjl'  
                AND PUB_CD_NO= 'CD000039'
                AND SRC_COL_ENG_NM = 'FKFS' ) T50
    ON T2.FKFS = T50.SRC_CD_VAL
  LEFT JOIN (SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
               FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
              WHERE SRC_TAB_ENG_NM='bid.tcgq_cjjl'  
                AND PUB_CD_NO= 'CD000146'
                AND SRC_COL_ENG_NM = 'JSFS' ) T51
    ON T2.JSFS = T51.SRC_CD_VAL 
 WHERE T1.DT = ${dmp_day}
  ------------------------------------------------------------------------------- -------------------------------------------------------------------------------
 union ALL
 ----资产出租
SELECT      T1.XMID                   AS DEAL_REC_ID                      --成交记录号/成交记录ID
            ,NULL                      AS PRJ_WRD                          --项目ID/项目关键字
            ,'BJHL'||T2.XM||'ZCCZ'     AS BSN_PRJ_WRD                      --项目/业务项目关键字
            ,T22.KHH                      AS CUST_WRD                         --客户号/客户关键字
            ,T2.SRF                    AS BSN_BUYER_ID                     --承租方/业务买受方ID
            ,T2.ID                     AS BSN_DEAL_REC_ID                  --ID/业务成交记录ID
            ,NULL                      AS DEAL_NO                          --成交编号/成交编号
            ,NULL                      AS ETRS_CGY_CD                      --委托类别/委托类别代码
            ,NULL                      AS ETRS_CGY_CD_DSC                  --码值/委托类别代码描述
            ,NULL                      AS CURR_CD                          --币种/币种代码
            ,NULL                      AS CURR_CD_DSC                      --码值/币种代码描述
            ,NULL                      AS CPTL_ACC                         --资金账户/资金账户
            ,NULL                      AS TXN_PRC                          --成交价格(元)/成交价格
            ,NULL                      AS DEAL_NUM                         --成交数量(手)/成交数量
            ,NULL                      AS DEAL_AMT                         --成交金额/成交金额
            ,NULL                      AS DEAL_DT                          --成交日期/成交日期
            ,NULL                      AS DEAL_TM                          --成交时间/成交时间
            ,REGEXP_REPLACE(T2.CJRQ ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')                 
                  			           AS BSN_REC_DEAL_DT                  --成交日期/业务记录成交日期
            ,NULL                      AS CFRM_DT                          --确认日期/确认日期
            ,NULL                      AS CFRM_TM                          --确认时间/确认时间
            ,NULL                      AS DELVRY_DT                        --交割日期/交割日期
            ,NULL                      AS DELVRY_TM                        --交割时间/交割时间
            ,NULL                      AS DEAL_STAT_CD                     --成交状态/成交状态代码
            ,NULL                      AS DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
            ,NULL                      AS IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
            ,NULL                      AS BROGE                            --手续费/手续费
            ,NULL                      AS THIRD_DEPST_SERIAL_NO            --第三方入金流水号/第三方入金流水号
            ,NULL                      AS PAY_STAT_CD                      --付款状态/付款状态代码
            ,NULL                      AS PAY_STAT_CD_DSC                  --码值/付款状态代码描述
            ,NULL                      AS CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
            ,NULL                      AS DELVER_ID                        --交割人/交割人ID
            ,NULL                      AS DELVER_NM                        --交割人姓名/交割人姓名
            ,T2.SFKDY                  AS IS_SURE_PRNT                     --交易凭证是否可以打印/是否可以打印
            ,T2.JYPZDYYS               AS TXN_VCHR_PRNT_PAGNMB             --交易凭证打印页数/交易凭证打印页数
            ,NULL                      AS PRNT_COPS                        --打印份数/打印份数
            ,NULL                      AS PRJ_NM                           --NULL/项目名称
            ,T22.CZFMC                 AS TRSFEE_NM                        --意向承租方名称/受让方名称
            ,T2.HTQDRQ                 AS CONT_SIGN_DT                     --合同签订日期/合同签订日期
            ,T2.HTSXRQ                 AS CONT_EFF_DT                      --合同生效日期/合同生效日期
            ,REGEXP_REPLACE(T2.JGGSKSRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS RSLT_PUBTY_START_DT              --结果公示起始日期/结果公示起始日期
            ,REGEXP_REPLACE(T2.JGGSJSRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS RSLT_PUBTY_END_DT                --结果公示结束时间/结果公示结束日期
            ,T2.JGGSZQ                 AS RSLT_PUBTY_PRD_WKDY              --结果公示周期（工作日）/结果公示周期（工作日）
            ,NULL                      AS FNL_TRNSFR_PCT                   --NULL/最终受让比例
            ,T2.CJZJDJ                 AS DEAL_UNIT_PRIC                   --成交租金单价(元/平方米/月)/成交单价
            ,T2.SFZJK                  AS IS_MRGN_OF_TXN_PRC               --保证金是否转交易价款/是否保证金转交易价款
            ,T2.ZJKJE                  AS MRGN_OF_TXN_PRC_AMT              --保证金转交易价款金额（元）/保证金转交易价款金额
            ,T2.BZJZJKJE               AS MRGN_TF_PRC_AMT                  --保证金转价款金额（元）/保证金转价款金额
            ,NULL                      AS MRGN_TF_PRC_TMPOT                --NULL/保证金转价款时点
            ,NULL                      AS MRGN_TF_PRC_DT                   --NULL/保证金转价款日期
            ,NULL                      AS AVALB_MRGN_AMT                   --NULL/可使用保证金金额
            ,T2.DZJKJE                 AS TOACCT_PRC_AMT                   --到账价款金额（元）/到账价款金额
            ,T2.BZJJE                  AS MRGN_AMT                         --BZJJE	保证金金额（元）/保证金金额
            ,CASE WHEN T50.IS_CNVR='N' THEN T2.BZJCZFS ELSE CASE WHEN T50.CD_VAL IS NULL THEN 'WZ'||T2.BZJCZFS ELSE T50.CD_VAL END END AS MRGN_DISPL_MTH_CD --保证金处置方式
            ,T5_1.NOTE                 AS MRGN_DISPL_MTH_CD_DSC            --码值/保证金处置方式代码描述
            ,T2.BZJZJKDDID             AS MRGN_TF_PRC_ORDER_ID             --保证金转价款订单ID/保证金转价款订单ID
            ,NULL                      AS MRGN_TF_PRC_TM                   --NULL/保证金转价款时间
            ,NULL                      AS IS_MRGN_ADV_TSFT                 --NULL/是否保证金提前划出
            ,CASE WHEN T51.IS_CNVR='N' THEN T2.ZFFS ELSE CASE WHEN T51.CD_VAL IS NULL THEN 'WZ'||T2.ZFFS ELSE T51.CD_VAL END END AS PY_MTH_CD --支付方式
            ,T6.NOTE                   AS PY_MTH_CD_DSC                    --码值/支付方式代码描述
            ,CASE WHEN T52.IS_CNVR='N' THEN T2.JSFS ELSE CASE WHEN T52.CD_VAL IS NULL THEN 'WZ'||T2.JSFS ELSE T52.CD_VAL END END  AS SURPL_PRC_SETL_MTH_CD --剩余价款结算方式
            ,T7.NOTE                   AS SURPL_PRC_SETL_MTH_CD_DSC        --码值/剩余价款结算方式代码描述
            ,T2.SFJE                   AS DPAMT_AMT                        --首付金额（元）/首付金额
            ,T2.SQFKBL*0.01            AS FISSU_PAY_PECT                   --首期付款比例（%）/首期付款百分比
            ,T2.WKJZRQ                 AS BLPT_EXPI_DT                     --尾款截至日期/尾款截止日期
            ,NULL                      AS PAYB_PRC                         --NULL/应支付价款
            ,NULL                      AS SCROT_PRC_AMT                    --NULL/可划出价款金额
            ,NULL                      AS OTHR_REASON                      --NULL/其他原因
            ,CASE WHEN T53.IS_CNVR='N' THEN T2.JSFS_YJ ELSE CASE WHEN T53.CD_VAL IS NULL THEN 'WZ'||T2.JSFS_YJ ELSE T53.CD_VAL END END AS SETL_MTH_CD --/结算方式代码
            ,CASE WHEN T2.JSFS_YJ = '1' THEN '场外结算'
			      WHEN T2.JSFS_YJ = '2' THEN '场内结算' END  AS SETL_MTH_CD_DSC                  --码值/结算方式代码描述
            ,T2.SFWBJS                 AS IS_FRN_CUR_SETL                  --是否外币结算/是否外币结算
            ,CASE WHEN T54.IS_CNVR='N' THEN T2.WBBZ ELSE CASE WHEN T54.CD_VAL IS NULL THEN 'WZ'||T2.WBBZ ELSE T54.CD_VAL END END AS FRN_CUR_CURR_CD  --外币币种
            ,T9.BZMC                   AS FRN_CUR_CURR_CD_DSC              --码值/外币币种代码描述
            ,T2.YDRHL                  AS AGDT_EXRT                        --约定日汇率/约定日汇率
            ,T2.ZSWBJE           AS CONVT_FRN_CUR_AMT                --折算外币金额（元）/折算外币金额
            ,CASE WHEN T55.IS_CNVR='N' THEN T2.SJJYFS ELSE CASE WHEN T55.CD_VAL IS NULL THEN 'WZ'||T2.SJJYFS ELSE T55.CD_VAL END END AS ACTL_TXN_MTH_CD --实际交易方式
            ,T10.NOTE                  AS ACTL_TXN_MTH_CD_DSC              --码值/实际交易方式代码描述
            ,T2.GGJYFSYY               AS CHAG_TXN_MTH_REASON              --更改交易方式原因/更改交易方式原因
            ,T2.JYJGSHYJ               AS TXN_ORG_AUDIT_OPIN               --交易机构审核意见/交易机构审核意见
            ,NULL                      AS DEAL_TP_CD                       --NULL/成交类型代码
            ,NULL                      AS DEAL_TP_CD_DSC                   --NULL/成交类型代码描述
            ,CASE WHEN T56.IS_CNVR='N' THEN T2.HCBS ELSE CASE WHEN T56.CD_VAL IS NULL THEN 'WZ'||T2.HCBS ELSE T56.CD_VAL END END AS PRC_TSFT_FLG_CD --价款划出标识代码
            ,T12.NOTE                  AS PRC_TSFT_FLG_CD_DSC              --码值/价款划出标识代码描述
            ,T2.HCJE                   AS ATSFT_AMT                        --划出金额（元）/已划出金额
            ,NULL                      AS PRC_GOIN_BSN_ORDER_ID            --NULL/价款划入业务订单ID
            ,NULL                      AS ORDER_NO_PLFORM                  --NULL/订单编号（平台）
            ,T2.CJR                    AS CATR_ID                          --创建人/创建人ID
            ,T13.NAME                  AS CATR_NM                          --名称/创建人名称
            ,T2.CJSJ                   AS CRT_TM                           --创建时间/创建时间
            ,T2.GXR                    AS UPD_PSN_ID                       --更新人/更新人ID
            ,T14.NAME                  AS UPD_PSN_NM                       --名称/更新人名称
            ,T2.GXSJ                   AS MOD_TM                           --更新时间/更新时间
            ,T2.XMFZR                  AS PRJ_PRIN_ID                      --项目负责人/项目负责人ID
            ,T15.NAME                  AS PRJ_PRIN_NM                      --名称/项目负责人名称
            ,REGEXP_REPLACE(T2.XMFZRTGSJ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS PRJ_PRIN_PASS_DT --项目负责人通过时间/项目负责人通过日期
            ,T2.YWBMFZR                AS BSN_DEPT_PRIN_ID                 --业务部门负责人/业务部门负责人ID
            ,T16.NAME                  AS BSN_DEPT_PRIN_NM                 --名称/业务部门负责人名称
            ,REGEXP_REPLACE(T2.YWBMFZRTGSJ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS BSN_DEPT_PRIN_PASS_DT  --业务部门负责人通过时间/业务部门负责人通过日期
            ,T2.JYSHBSHR               AS TXN_AUDT_DEP_AUDITOR_ID          --交易审核部审核人/交易审核部审核人ID
            ,T17.NAME                  AS TXN_AUDT_DEP_AUDITOR_NM          --名称/交易审核部审核人名称
            ,REGEXP_REPLACE(T2.JYSHBSHRTGSJ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS TXN_AUDT_DEP_AUDITOR_PASS_DT --交易审核部审核人通过时间
            ,T2.JYBFZR                 AS TRAS_DEP_PRIN_ID                 --交易部负责人/交易部负责人ID
            ,T18.NAME                  AS TRAS_DEP_PRIN_NM                 --名称/交易部负责人名称
            ,REGEXP_REPLACE(T2.JYBFZRTGSJ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3') AS TRAS_DEP_PRIN_PASS_DT --交易部负责人通过时间/交易部负责人通过日期
            ,T2.JYSSHR                 AS EXG_AUDITOR_ID                   --交易所审核人/交易所审核人ID
            ,T19.NAME                  AS EXG_AUDITOR_NM                   --名称/交易所审核人名称
            ,REGEXP_REPLACE(T2.JYSSHRTGSJ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS EXG_AUDITOR_PASS_DT --交易所审核人通过时间/交易所审核人通过日期
            ,T2.ZXFZR                  AS CETR_PRIN_ID                     --中心负责人/中心负责人ID
            ,T20.NAME                  AS CETR_PRIN_NM                     --名称/中心负责人名称
            ,REGEXP_REPLACE(T2.ZXFZRTGSJ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS CETR_PRIN_PASS_DT  --中心负责人通过时间/中心负责人通过日期
            ,NULL                      AS CONT_ATCH                        --NULL/合同附件
            ,NULL                      AS IS_FST_TM_PRC_TSFT_AUDIT_PASS    --NULL/是否第一次价款划出审核通过
            ,CASE WHEN T57.IS_CNVR='N' THEN T2.CJZT ELSE CASE WHEN T57.CD_VAL IS NULL THEN 'WZ'||T2.CJZT ELSE T57.CD_VAL END END  AS DEAL_REC_STAT_CD --成交状态
            ,NULL                      AS DEAL_REC_SERIAL_NO               --NULL/成交记录流水号
            ,NULL                      AS DEAL_OVRL_RMRK                   --NULL/成交总体备注
            ,NULL                   AS RMRK                             --备注/备注
             ,T1.DT                   AS DT
FROM        ODS.ODS_BJHL_TZCCZ_ZCCZXM    T1       --资产出租项目
LEFT JOIN
            (SELECT *
             FROM   ODS.ODS_BJHL_TZCCZ_CJJL 
			 WHERE DT = ${dmp_day}
			 )   T2     --资产出租成交记录
ON          T1.ID=T2.XM			 
LEFT JOIN  (SELECT ID --ID 
                   ,CZFMC  --意向承租方名称
				   ,KHH
             FROM  ODS.ODS_BJHL_TZCCZ_YXCZFXX  --意向承租方信息
			WHERE DT = ${dmp_day}
           ) T22
ON         T2.SRF=T22.ID
LEFT JOIN  (SELECT IBM  --代码 
                   ,NOTE  --码值
             FROM  ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
			WHERE DT = ${dmp_day}
			  AND  FLDM='ZCCZ_BZJCZFS'
           ) T5_1
ON         T2.BZJCZFS=T5_1.IBM
LEFT JOIN  (SELECT IBM  --代码 
                   ,NOTE  --码值
             FROM  ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
			WHERE  DT = ${dmp_day}
			  AND  FLDM='ZCCZ_JKZFFS'
           ) T6
ON         T2.ZFFS=T6.IBM
LEFT JOIN  (SELECT IBM  --代码 
                   ,NOTE  --码值
             FROM  ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
			WHERE DT = ${dmp_day}
			  AND  FLDM='ZCCZ_CJZFFS'
           ) T7
ON         T2.JSFS=T7.IBM
LEFT JOIN (SELECT BZ  ,--代码 
                  BZMC  --码值
             FROM ODS.ODS_BJHL_TBZ  --币种定义
			WHERE DT = ${dmp_day}
           ) T9 
ON          T2.WBBZ=T9.BZ	
LEFT JOIN  (SELECT IBM  --代码 
                   ,NOTE  --码值
             FROM  ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
			WHERE  DT = ${dmp_day}
			  AND   FLDM='ZCCZ_SJJYFS'
           ) T10
ON         T2.SJJYFS=T10.IBM
LEFT JOIN  (SELECT IBM  --代码 
                   ,NOTE  --码值
             FROM  ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
			WHERE  DT = ${dmp_day}
			  AND  FLDM='ZCCZ_HCBS'
           ) T12
ON         T2.HCBS=T12.IBM
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T13	   		  		   
ON         T2.CJR=T13.ID
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T14	   		  		   
ON         T2.GXR=T14.ID
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T15	   		  		   
ON         T2.XMFZR=T15.ID
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T16	   		  		   
ON         T2.YWBMFZR=T16.ID
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T17	   		  		   
ON         T2.JYSHBSHR=T17.ID
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T18	   		  		   
ON         T2.JYBFZR=T18.ID
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T19	   		  		   
ON         T2.JYSSHR=T19.ID
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T20	   		  		   
ON         T2.ZXFZR=T20.ID

LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='product.tzccz_cjjl' 
AND PUB_CD_NO= 'CD000053'
AND SRC_COL_ENG_NM = 'BZJCZFS'
) T50
ON T2.BZJCZFS = T50.SRC_CD_VAL 

LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='product.tzccz_cjjl'  
AND PUB_CD_NO= 'CD000039'
AND SRC_COL_ENG_NM = 'ZFFS'
) T51
ON T2.ZFFS = T51.SRC_CD_VAL 

LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='product.tzccz_cjjl'  
AND PUB_CD_NO= 'CD000146'
AND SRC_COL_ENG_NM = 'JSFS'
) T52
ON T2.JSFS = T52.SRC_CD_VAL

LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='product.tzccz_cjjl'  
AND PUB_CD_NO= 'CD000146'
AND SRC_COL_ENG_NM = 'JSFS_YJ'
) T53
ON T2.JSFS_YJ = T53.SRC_CD_VAL

LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='product.tzccz_cjjl'  
AND PUB_CD_NO= 'CD000016'
AND SRC_COL_ENG_NM = 'WBBZ'
) T54
ON T2.WBBZ = T54.SRC_CD_VAL 

LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='product.tzccz_cjjl'  
AND PUB_CD_NO= 'CD000035'
AND SRC_COL_ENG_NM = 'SJJYFS'
) T55
ON T2.SJJYFS = T55.SRC_CD_VAL

LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='product.tzccz_cjjl'  
AND PUB_CD_NO= 'CD000159'
AND SRC_COL_ENG_NM = 'HCBS'
) T56
ON T2.HCBS = T56.SRC_CD_VAL

LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='product.tzccz_cjjl'  
AND PUB_CD_NO= 'CD000160'
AND SRC_COL_ENG_NM = 'CJZT'
) T57
ON T2.CJZT = T57.SRC_CD_VAL 
WHERE T1.DT = ${dmp_day}
AND T2.CJRQ IS NOT NULL
 )

--成交记录
SELECT      CAST(T1.CJJLH AS DECIMAL(16,0))   AS DEAL_REC_ID               --成交记录号/成交记录ID
            ,'BJHL'||T1.XMID           AS PRJ_WRD                          --项目ID/项目关键字
            ,T2.BSN_PRJ_WRD                                                --项目/业务项目关键字
            ,'BJHL'||T1.KHH            AS CUST_WRD                         --客户号/客户关键字
            ,T2.BSN_BUYER_ID                                               --受让方/业务买受方ID
            ,T2.BSN_DEAL_REC_ID                                            --ID/业务成交记录ID
            ,CAST(T1.CJBH AS STRING)                  AS DEAL_NO                          --成交编号/成交编号
            ,CASE WHEN T50.IS_CNVR='N' THEN T1.WTLB ELSE CASE WHEN T50.CD_VAL IS NULL THEN 'WZ'||T1.WTLB ELSE T50.CD_VAL END END  AS ETRS_CGY_CD --委托类别
            ,CASE WHEN T1.WTLB = '1' THEN '买入' WHEN T1.WTLB = '2' THEN '卖出' END                        AS ETRS_CGY_CD_DSC                  --码值/委托类别代码描述
            ,CASE WHEN T51.IS_CNVR='N' THEN T1.BZ ELSE CASE WHEN T51.CD_VAL IS NULL THEN 'WZ'||T1.BZ ELSE T51.CD_VAL END END AS CURR_CD --币种/币种代码
            ,T3.BZMC                   AS CURR_CD_DSC                      --码值/币种代码描述
            ,T1.ZJZH                   AS CPTL_ACC                         --资金账户/资金账户
            ,T1.CJJG                   AS TXN_PRC                          --成交价格(元)/成交价格
            ,T1.CJSL                   AS DEAL_NUM                         --成交数量(手)/成交数量
            ,T1.CJJE                   AS DEAL_AMT                         --成交金额/成交金额
            ,REGEXP_REPLACE(T1.CJRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS DEAL_DT                          --成交日期/成交日期
            ,T1.CJSJ                   AS DEAL_TM                          --成交时间/成交时间
            ,T2.BSN_REC_DEAL_DT                  --成交日期/业务记录成交日期
            ,REGEXP_REPLACE(T1.QRRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS CFRM_DT                          --确认日期/确认日期
            ,T1.QRSJ                   AS CFRM_TM                          --确认时间/确认时间
            ,REGEXP_REPLACE(T1.JGRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS DELVRY_DT                        --交割日期/交割日期
            ,T1.JGSJ                   AS DELVRY_TM                        --交割时间/交割时间
            ,CASE WHEN T52.IS_CNVR='N' THEN T1.CJZT ELSE CASE WHEN T52.CD_VAL IS NULL THEN 'WZ'||T1.CJZT ELSE T52.CD_VAL END END  AS DEAL_STAT_CD --成交状态
            ,T4.NOTE                   AS DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
            ,CAST(T1.SFXTSQ AS STRING)                AS IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
            ,CAST(T1.SXF AS STRING)                   AS BROGE                            --手续费/手续费
            ,CAST(T1.DSF_RJ_LSH AS STRING)             AS THIRD_DEPST_SERIAL_NO            --第三方入金流水号/第三方入金流水号
            ,CASE WHEN T53.IS_CNVR='N' THEN T1.FKZT ELSE CASE WHEN T53.CD_VAL IS NULL THEN 'WZ'||T1.FKZT ELSE T53.CD_VAL END END AS PAY_STAT_CD --付款状态/付款状态代码
            ,T5.NOTE                   AS PAY_STAT_CD_DSC                  --码值/付款状态代码描述
            ,CAST(T1.DRZJMX_LSH AS STRING)              AS CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
            ,CAST(T1.JGR AS STRING)                    AS DELVER_ID                        --交割人/交割人ID
            ,T1.JGRXM                  AS DELVER_NM                        --交割人姓名/交割人姓名
            ,T2.IS_SURE_PRNT                                               --是否可以打印/是否可以打印
            ,T2.TXN_VCHR_PRNT_PAGNMB                                       --交易凭证打印页数/交易凭证打印页数
            ,T1.DYFS                   AS PRNT_COPS                        --打印份数/打印份数
            ,T2.PRJ_NM                                                     --项目名称/项目名称
            ,T2.TRSFEE_NM                                                  --受让方名称/受让方名称
            ,T2.CONT_SIGN_DT                                               --合同签订日期/合同签订日期
            ,T2.CONT_EFF_DT                                                --合同生效日期/合同生效日期
            ,T2.RSLT_PUBTY_START_DT                                        --结果公示起始时间/结果公示起始日期
            ,T2.RSLT_PUBTY_END_DT                                          --结果公示结束时间/结果公示结束日期
            ,T2.RSLT_PUBTY_PRD_WKDY                                        --结果公示周期（工作日）/结果公示周期（工作日）
            ,T2.FNL_TRNSFR_PCT                                             --最终受让比例/最终受让比例
            ,T2.DEAL_UNIT_PRIC                                             --NULL/成交单价
            ,T2.IS_MRGN_OF_TXN_PRC                                         --保证金是否转交易价款/是否保证金转交易价款
            ,T2.MRGN_OF_TXN_PRC_AMT                                        --NULL/保证金转交易价款金额
            ,T2.MRGN_TF_PRC_AMT                                            --保证金转价款金额(万元)/保证金转价款金额
            ,T2.MRGN_TF_PRC_TMPOT                                          --保证金转价款时点/保证金转价款时点
            ,T2.MRGN_TF_PRC_DT                                             --保证金转价款日期/保证金转价款日期
            ,T2.AVALB_MRGN_AMT                                             --NULL/可使用保证金金额
            ,T2.TOACCT_PRC_AMT                                             --NULL/到账价款金额
            ,T2.MRGN_AMT                                                   --保证金金额（万元）/保证金金额
            ,T2.MRGN_DISPL_MTH_CD                                          --NULL/保证金处置方式代码
            ,T2.MRGN_DISPL_MTH_CD_DSC                                      --NULL/保证金处置方式代码描述
            ,T2.MRGN_TF_PRC_ORDER_ID                                       --保证金转价款订单ID/保证金转价款订单ID
            ,CAST(T2.MRGN_TF_PRC_TM AS STRING) AS MRGN_TF_PRC_TM                                            --保证金转价款时间/保证金转价款时间
            ,T2.IS_MRGN_ADV_TSFT                                           --保证金提前划出/是否保证金提前划出
            ,T2.PY_MTH_CD                                                  --支付方式/支付方式代码
            ,T2.PY_MTH_CD_DSC                                              --码值/支付方式代码描述
            ,T2.SURPL_PRC_SETL_MTH_CD                                      --剩余价款结算方式/剩余价款结算方式代码
            ,T2.SURPL_PRC_SETL_MTH_CD_DSC                                  --码值/剩余价款结算方式代码描述
            ,T2.DPAMT_AMT                                                  --首付金额(万元)/首付金额
            ,T2.FISSU_PAY_PECT                                             --首期付款百分比/首期付款百分比
            ,T2.BLPT_EXPI_DT                                               --尾款截止日期/尾款截止日期
            ,T2.PAYB_PRC                                                   --应支付价款（万元）/应支付价款
            ,T2.SCROT_PRC_AMT                                              --可划出价款金额（万元）/可划出价款金额
            ,T2.OTHR_REASON                                                --其他原因/其他原因
            ,T2.SETL_MTH_CD                                                --结算方式/结算方式代码
            ,T2.SETL_MTH_CD_DSC                                            --码值/结算方式代码描述
            ,T2.IS_FRN_CUR_SETL                                            --是否外币结算/是否外币结算
            ,T2.FRN_CUR_CURR_CD                                            --外币币种/外币币种代码
            ,T2.FRN_CUR_CURR_CD_DSC                                        --码值/外币币种代码描述
            ,T2.AGDT_EXRT                                                  --约定日汇率/约定日汇率
            ,T2.CONVT_FRN_CUR_AMT                                          --折算外币金额(万元)/折算外币金额
            ,T2.ACTL_TXN_MTH_CD                                            --实际交易方式/实际交易方式代码
            ,T2.ACTL_TXN_MTH_CD_DSC                                        --码值/实际交易方式代码描述
            ,T2.CHAG_TXN_MTH_REASON                                        --更改交易方式原因/更改交易方式原因
            ,T2.TXN_ORG_AUDIT_OPIN                                         --交易机构审核意见/交易机构审核意见
            ,T2.DEAL_TP_CD                                                 --成交类型/成交类型代码
            ,T2.DEAL_TP_CD_DSC                                             --码值/成交类型代码描述
            ,T2.PRC_TSFT_FLG_CD                                            --价款划出标识/价款划出标识代码
            ,T2.PRC_TSFT_FLG_CD_DSC                                        --码值/价款划出标识代码描述
            ,T2.ATSFT_AMT                                                  --已划出金额（万元）/已划出金额
            ,T2.PRC_GOIN_BSN_ORDER_ID                                      --价款划入业务订单ID/价款划入业务订单ID
            ,T2.ORDER_NO_PLFORM                                            --订单编号（平台）/订单编号（平台）
            ,T2.CATR_ID                                                    --创建人/创建人ID
            ,T2.CATR_NM                                                    --名称/创建人名称
            ,CAST(T2.CRT_TM AS STRING) AS crt_tm                                                     --创建时间/创建时间
            ,T2.UPD_PSN_ID                                                 --更新人/更新人ID
            ,T2.UPD_PSN_NM                                                 --名称/更新人名称
            ,T2.MOD_TM                                                     --更新时间/更新时间
            ,T2.PRJ_PRIN_ID                                                --NULL/项目负责人ID
            ,T2.PRJ_PRIN_NM                                                --NULL/项目负责人名称
            ,T2.PRJ_PRIN_PASS_DT                                           --NULL/项目负责人通过日期
            ,T2.BSN_DEPT_PRIN_ID                                           --NULL/业务部门负责人ID
            ,T2.BSN_DEPT_PRIN_NM                                           --NULL/业务部门负责人名称
            ,T2.BSN_DEPT_PRIN_PASS_DT                                      --NULL/业务部门负责人通过日期
            ,T2.TXN_AUDT_DEP_AUDITOR_ID                                    --NULL/交易审核部审核人ID
            ,T2.TXN_AUDT_DEP_AUDITOR_NM                                    --NULL/交易审核部审核人名称
            ,T2.TXN_AUDT_DEP_AUDITOR_PASS_DT                               --NULL/交易审核部审核人通过日期
            ,T2.TRAS_DEP_PRIN_ID                                           --NULL/交易部负责人ID
            ,T2.TRAS_DEP_PRIN_NM                                           --NULL/交易部负责人名称
            ,T2.TRAS_DEP_PRIN_PASS_DT                                      --NULL/交易部负责人通过日期
            ,T2.EXG_AUDITOR_ID                                             --NULL/交易所审核人ID
            ,T2.EXG_AUDITOR_NM                                             --NULL/交易所审核人名称
            ,T2.EXG_AUDITOR_PASS_DT                                        --NULL/交易所审核人通过日期
            ,T2.CETR_PRIN_ID                                               --NULL/中心负责人ID
            ,T2.CETR_PRIN_NM                                               --NULL/中心负责人名称
            ,T2.CETR_PRIN_PASS_DT                                          --NULL/中心负责人通过日期
            ,T2.CONT_ATCH                                                  --合同附件/合同附件
            ,T2.IS_FST_TM_PRC_TSFT_AUDIT_PASS                              --第一次价款划出是否审核通过/是否第一次价款划出审核通过
            ,T2.DEAL_REC_STAT_CD                                           --NULL/成交记录状态代码
            ,T2.DEAL_REC_SERIAL_NO                                         --NULL/成交记录流水号
            ,T2.DEAL_OVRL_RMRK                                             --NULL/成交总体备注
            ,T1.NOTE  AS RMRK                                              --备注/备注
FROM      ODS.ODS_BJHL_TBID_CJJL         T1               --成交记录表
LEFT JOIN (SELECT *
FROM TMP_A	--各组业务数据
WHERE DT = ${dmp_day}
AND BSN_REC_DEAL_DT IS NOT NULL) T2
--ON  REGEXP_REPLACE(T1.CJRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3') = T2.BSN_REC_DEAL_DT
ON T1.XMID = T2.DEAL_REC_ID
AND T1.KHH=T2.Cust_Wrd
LEFT JOIN (SELECT BZ  ,--代码 
                  BZMC  --码值
             FROM ODS.ODS_BJHL_TBZ  --币种定义
			WHERE DT = ${dmp_day}
           ) T3 
ON T1.BZ=T3.BZ	
LEFT JOIN  (SELECT IBM  --代码 
                   ,NOTE  --码值
             FROM  ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
			WHERE  DT = ${dmp_day}
			  AND  FLMC = 'BID_成交状态'
           ) T4
ON         T1.CJZT=T4.IBM
LEFT JOIN  (SELECT IBM  --代码 
                   ,NOTE  --码值
             FROM  ODS.ODS_BJHL_TXTDM  --LIVEBOS数据字典
			WHERE  DT = ${dmp_day}
			  AND  FLMC = 'BID_付款状态'
           ) T5
ON         T1.FKZT=T5.IBM
LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='bid.tbid_cjjl'
AND PUB_CD_NO= 'CD000155'
AND SRC_COL_ENG_NM = 'WTLB'
) T50
ON T1.WTLB = T50.SRC_CD_VAL
LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='bid.tbid_cjjl'
AND PUB_CD_NO= 'CD000016'
AND SRC_COL_ENG_NM = 'BZ'
) T51
ON T1.BZ = T51.SRC_CD_VAL 
LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='bid.tbid_cjjl'
AND PUB_CD_NO= 'CD000156'
AND SRC_COL_ENG_NM = 'CJZT'
) T52
ON T1.CJZT = T52.SRC_CD_VAL 
LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='bid.tbid_cjjl'
AND PUB_CD_NO= 'CD000157'
AND SRC_COL_ENG_NM = 'FKZT'
) T53
ON T1.FKZT = T53.SRC_CD_VAL
WHERE       T1.DT = ${dmp_day}
AND         T1.WTLB='1'
 ------------------------------------------------------------------------------- -------------------------------------------------------------------------------
--成交记录事实
 union ALL
 SELECT       T1.XMID                   AS DEAL_REC_ID                      --成交记录号/成交记录ID
            ,NULL                      AS PRJ_WRD                          --项目ID/项目关键字
            ,'BJHL'||T2.T_XM||'QYZZ'   AS BSN_PRJ_WRD                      --项目/业务项目关键字
            ,NULL                   AS CUST_WRD                         --客户号/客户关键字
			,T2.T_TZF                  AS BSN_BUYER_ID                     --投资方/业务买受方ID
            ,T2.ID                     AS BSN_DEAL_REC_ID                  --ID/业务成交记录ID
            ,NULL                      AS DEAL_NO                          --成交编号/成交编号
            ,NULL                      AS ETRS_CGY_CD                      --委托类别/委托类别代码
            ,NULL                      AS ETRS_CGY_CD_DSC                  --码值/委托类别代码描述
            ,NULL                      AS CURR_CD                          --币种/币种代码
            ,NULL                      AS CURR_CD_DSC                      --码值/币种代码描述
            ,NULL                      AS CPTL_ACC                         --资金账户/资金账户
            ,NULL                      AS TXN_PRC                          --成交价格(元)/成交价格
            ,NULL                      AS DEAL_NUM                         --成交数量(手)/成交数量
            ,NULL                      AS DEAL_AMT                         --成交金额/成交金额
            ,NULL                      AS DEAL_DT                          --成交日期/成交日期
            ,NULL                      AS DEAL_TM                          --成交时间/成交时间
            ,substr(T1.CJRQ,1,10)                   AS BSN_REC_DEAL_DT                  --成交日期/业务记录成交日期
            ,NULL                      AS CFRM_DT                          --确认日期/确认日期 
            ,NULL                      AS CFRM_TM                          --确认时间/确认时间
            ,NULL                      AS DELVRY_DT                        --交割日期/交割日期
            ,NULL                      AS DELVRY_TM                        --交割时间/交割时间
            ,NULL                      AS DEAL_STAT_CD                     --成交状态/成交状态代码
            ,NULL                      AS DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
            ,NULL                      AS IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
            ,NULL                      AS BROGE                            --手续费/手续费
            ,NULL                      AS THIRD_DEPST_SERIAL_NO            --第三方入金流水号/第三方入金流水号
            ,NULL                      AS PAY_STAT_CD                      --付款状态/付款状态代码
            ,NULL                      AS PAY_STAT_CD_DSC                  --码值/付款状态代码描述
            ,NULL                      AS CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
            ,NULL                      AS DELVER_ID                        --交割人/交割人ID
            ,NULL                      AS DELVER_NM                        --交割人姓名/交割人姓名
            ,NULL                      AS IS_SURE_PRNT                     --NULL/是否可以打印
            ,T2.JYPZDYYS               AS TXN_VCHR_PRNT_PAGNMB             --交易凭证打印页数/交易凭证打印页数
            ,NULL                      AS PRNT_COPS                        --打印份数/打印份数
            ,NULL                      AS PRJ_NM                           --NULL/项目名称
            ,T2.TZFMC                  AS TRSFEE_NM                        --投资方名称/受让方名称
            ,CAST(T2.QYRQ AS STRING)   AS CONT_SIGN_DT                     --签约日期/合同签订日期
            ,REGEXP_REPLACE(T2.HTSXRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')   AS CONT_EFF_DT --合同生效日期/合同生效日期
            ,CAST(T1.JGGSKSSJ AS STRING) AS RSLT_PUBTY_START_DT              --结果公示开始时间/结果公示起始日期
            ,CAST(T1.JGGSJSSJ  AS STRING) AS RSLT_PUBTY_END_DT                --结果公示结束时间/结果公示结束日期
            ,T1.JGGSZQ                 AS RSLT_PUBTY_PRD_WKDY              --结果公示周期（工作日）/结果公示周期（工作日）
            ,NULL                      AS FNL_TRNSFR_PCT                   --NULL/最终受让比例
            ,NULL                      AS DEAL_UNIT_PRIC                   --NULL/成交单价
            ,T2.BZJSFZZZK              AS IS_MRGN_OF_TXN_PRC               --保证金是否转增资款/是否保证金转交易价款
            ,NULL                      AS MRGN_OF_TXN_PRC_AMT              --NULL/保证金转交易价款金额
            ,NULL                      AS MRGN_TF_PRC_AMT                  --NULL/保证金转价款金额
            ,NULL                      AS MRGN_TF_PRC_TMPOT                --NULL /保证金转价款时点
            ,NULL                      AS MRGN_TF_PRC_DT                   --NULL /保证金转价款日期
            ,T2.KSYBZJJE*10000         AS AVALB_MRGN_AMT                   --可使用保证金金额/可使用保证金金额
            ,NULL                      AS TOACCT_PRC_AMT                   --NULL/到账价款金额
            ,T2.BZJJE*10000            AS MRGN_AMT                         --保证金金额/保证金金额
            ,NULL                      AS MRGN_DISPL_MTH_CD                --NULL/保证金处置方式代码
            ,NULL                      AS MRGN_DISPL_MTH_CD_DSC            --NULL/保证金处置方式代码描述
            ,NULL                      AS MRGN_TF_PRC_ORDER_ID             --NULL/保证金转价款订单ID
            ,NULL                      AS MRGN_TF_PRC_TM                   --NULL/保证金转价款时间
            ,NULL                      AS IS_MRGN_ADV_TSFT                 --NULL/是否保证金提前划出
            ,CASE WHEN T50.IS_CNVR='N' THEN T2.FKFS ELSE CASE WHEN T50.CD_VAL IS NULL THEN 'WZ'||T2.FKFS ELSE T50.CD_VAL END END AS PY_MTH_CD  --支付方式代码
            ,CASE WHEN T2.FKFS = '1' THEN '一次性付款'
			      WHEN T2.FKFS = '2' THEN '分期付款' END                        AS PY_MTH_CD_DSC                    --码值/支付方式代码描述
            ,NULL                      AS SURPL_PRC_SETL_MTH_CD            --NULL/剩余价款结算方式代码
            ,NULL                      AS SURPL_PRC_SETL_MTH_CD_DSC        --NULL/剩余价款结算方式代码描述
            ,NULL                      AS DPAMT_AMT                        --NULL/首付金额
            ,NULL                      AS FISSU_PAY_PECT                   --NULL/首期付款百分比
            ,NULL                      AS BLPT_EXPI_DT                     --NULL/尾款截止日期
            ,NULL                      AS PAYB_PRC                         --NULL/应支付价款
            ,NULL                      AS SCROT_PRC_AMT                    --NULL/可划出价款金额
            ,NULL                      AS OTHR_REASON                      --NULL/其他原因
            ,CASE WHEN T51.IS_CNVR='N' THEN T2.JSFS ELSE CASE WHEN T51.CD_VAL IS NULL THEN 'WZ'||T2.JSFS ELSE T51.CD_VAL END END AS SETL_MTH_CD                      --结算方式
            ,CASE WHEN T2.JSFS = '1' THEN '场内结算'
			      WHEN T2.JSFS = '2' THEN '场外结算'
				  WHEN T2.JSFS = '3' THEN '无结算' END                        AS SETL_MTH_CD_DSC                  --码值/结算方式代码描述
            ,NULL                      AS IS_FRN_CUR_SETL                  --NULL/是否外币结算
            ,NULL                      AS FRN_CUR_CURR_CD                  --NULL/外币币种代码
            ,NULL                      AS FRN_CUR_CURR_CD_DSC              --NULL/外币币种代码描述
            ,NULL                      AS AGDT_EXRT                        --NULL/约定日汇率
            ,NULL                      AS CONVT_FRN_CUR_AMT                --NULL/折算外币金额
            ,NULL                      AS ACTL_TXN_MTH_CD                  --NULL/实际交易方式代码
            ,NULL                      AS ACTL_TXN_MTH_CD_DSC              --NULL/实际交易方式代码描述
            ,NULL                      AS CHAG_TXN_MTH_REASON              --NULL/更改交易方式原因
            ,NULL                      AS TXN_ORG_AUDIT_OPIN               --NULL/交易机构审核意见
            ,NULL                      AS DEAL_TP_CD                       --NULL/成交类型代码
            ,NULL                      AS DEAL_TP_CD_DSC                   --NULL/成交类型代码描述
            ,NULL                      AS PRC_TSFT_FLG_CD                  --NULL/价款划出标识代码
            ,NULL                      AS PRC_TSFT_FLG_CD_DSC              --NULL/价款划出标识代码描述
            ,NULL                      AS ATSFT_AMT                        --NULL/已划出金额
            ,NULL                      AS PRC_GOIN_BSN_ORDER_ID            --NULL/价款划入业务订单ID
            ,T2.JKHCDD                 AS ORDER_NO_PLFORM                  --价款划出订单/订单编号（平台）
            ,T2.CJR                    AS CATR_ID                          --创建人/创建人ID
            ,T13.NAME                  AS CATR_NM                          --名称/创建人名称
            ,CAST(T2.CJSJ AS STRING)                   AS CRT_TM                           --创建时间/创建时间
            ,T2.GXR                    AS UPD_PSN_ID                       --更新人/更新人ID
            ,T14.NAME                  AS UPD_PSN_NM                       --名称/更新人名称
            ,CAST(T2.GXSJ AS STRING)   AS MOD_TM                           --更新时间/更新时间
            ,NULL                      AS PRJ_PRIN_ID                      --NULL/项目负责人ID
            ,NULL                      AS PRJ_PRIN_NM                      --NULL/项目负责人名称
            ,NULL                      AS PRJ_PRIN_PASS_DT                 --NULL/项目负责人通过日期
            ,NULL                      AS BSN_DEPT_PRIN_ID                 --NULL/业务部门负责人ID
            ,NULL                      AS BSN_DEPT_PRIN_NM                 --NULL/业务部门负责人名称
            ,NULL                      AS BSN_DEPT_PRIN_PASS_DT            --NULL/业务部门负责人通过日期
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_ID          --NULL/交易审核部审核人ID
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_NM          --NULL/交易审核部审核人名称
            ,NULL                      AS TXN_AUDT_DEP_AUDITOR_PASS_DT     --NULL/交易审核部审核人通过日期
            ,NULL                      AS TRAS_DEP_PRIN_ID                 --NULL/交易部负责人ID
            ,NULL                      AS TRAS_DEP_PRIN_NM                 --NULL/交易部负责人名称
            ,NULL                      AS TRAS_DEP_PRIN_PASS_DT            --NULL/交易部负责人通过日期
            ,NULL                      AS EXG_AUDITOR_ID                   --NULL/交易所审核人ID
            ,NULL                      AS EXG_AUDITOR_NM                   --NULL/交易所审核人名称
            ,NULL                      AS EXG_AUDITOR_PASS_DT              --NULL/交易所审核人通过日期
            ,NULL                      AS CETR_PRIN_ID                     --NULL/中心负责人ID
            ,NULL                      AS CETR_PRIN_NM                     --NULL/中心负责人名称
            ,NULL                      AS CETR_PRIN_PASS_DT                --NULL/中心负责人通过日期
            ,NULL                      AS CONT_ATCH                        --NULL/合同附件
            ,NULL                      AS IS_FST_TM_PRC_TSFT_AUDIT_PASS    --NULL/是否第一次价款划出审核通过
            ,NULL                      AS DEAL_REC_STAT_CD                 --NULL/成交记录状态代码
            ,T2.LSH                    AS DEAL_REC_SERIAL_NO               --成交记录的流水号/成交记录流水号
            ,T1.BZ                     AS DEAL_OVRL_RMRK                   --成交总体备注/成交总体备注
            ,NULL                      AS RMRK                             --备注/备注
FROM        ODS.ODS_BJHL_TCGQ_ZZZSGPXM  T1 --增资挂牌项目
INNER JOIN   (SELECT *
             FROM    ODS.ODS_BJHL_TCGQ_CJJL    T2       --企业增资成交记录
             WHERE  DT = ${dmp_day}
             AND    TZFS='2'	 
             ) T2
ON          T1.ID=T2.T_XM
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T13	   		  		   
ON         T2.CJR=T13.ID
LEFT JOIN  (SELECT ID  --代码 
                   ,NAME  --码值
             FROM  ODS.ODS_BJHL_TUSER  --用户管理
			WHERE  DT = ${dmp_day}
           ) T14	   		  		   
ON         T2.GXR=T14.ID   		 


LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='bid.tcgq_cjjl'  
AND PUB_CD_NO= 'CD000039'
AND SRC_COL_ENG_NM = 'FKFS'
) T50
ON T2.FKFS = T50.SRC_CD_VAL

 LEFT JOIN (
SELECT CD_VAL,SRC_CD_VAL,IS_CNVR
FROM DIM.DIM_PUB_CD_VAL_SRC_TRGT_MPNG
WHERE SRC_TAB_ENG_NM='bid.tcgq_cjjl'  
AND PUB_CD_NO= 'CD000146'
AND SRC_COL_ENG_NM = 'JSFS'
) T51
ON T2.JSFS = T51.SRC_CD_VAL 

WHERE T1.DT = ${dmp_day}
 ------------------------------------------------------------------------------- -------------------------------------------------------------------------------
--临时表
 union ALL
 SELECT  DEAL_REC_ID               --成交记录号/成交记录ID
        ,PRJ_WRD                          --项目ID/项目关键字
        ,BSN_PRJ_WRD                                                --项目/业务项目关键字
        ,CUST_WRD                         --客户号/客户关键字
        ,BSN_BUYER_ID                                               --受让方/业务买受方ID
        ,BSN_DEAL_REC_ID                                            --ID/业务成交记录ID
        ,CAST(DEAL_NO AS STRING) AS DEAL_NO                         --成交编号/成交编号
        ,ETRS_CGY_CD --委托类别
        ,ETRS_CGY_CD_DSC                  --码值/委托类别代码描述
        ,CURR_CD --币种/币种代码
        ,CURR_CD_DSC                      --码值/币种代码描述
        ,CPTL_ACC                         --资金账户/资金账户
        ,CAST(TXN_PRC AS DECIMAL(24,6))  TXN_PRC                        --成交价格(元)/成交价格
        ,CAST(DEAL_NUM AS DECIMAL(10,0)) AS DEAL_NUM                       --成交数量(手)/成交数量
        ,CAST(DEAL_AMT AS DECIMAL(24,6)) AS DEAL_AMT                         --成交金额/成交金额
        ,DEAL_DT                          --成交日期/成交日期
        ,DEAL_TM                          --成交时间/成交时间
        ,BSN_REC_DEAL_DT                  --成交日期/业务记录成交日期
        ,CFRM_DT                          --确认日期/确认日期
        ,CFRM_TM                          --确认时间/确认时间
        ,DELVRY_DT                        --交割日期/交割日期
        ,DELVRY_TM                        --交割时间/交割时间
        ,DEAL_STAT_CD --成交状态
        ,DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
        ,IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
        ,BROGE                            --手续费/手续费
        ,THIRD_DEPST_SERIAL_NO            --第三方入金流水号/第三方入金流水号
        ,PAY_STAT_CD --付款状态/付款状态代码
        ,PAY_STAT_CD_DSC                  --码值/付款状态代码描述
        ,CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
        ,DELVER_ID                        --交割人/交割人ID
        ,DELVER_NM                        --交割人姓名/交割人姓名
        ,IS_SURE_PRNT                                               --是否可以打印/是否可以打印
        ,TXN_VCHR_PRNT_PAGNMB                                       --交易凭证打印页数/交易凭证打印页数
        ,CAST(PRNT_COPS AS DECIMAL(24,6)) AS PRNT_COPS                       --打印份数/打印份数
        ,PRJ_NM                                                     --项目名称/项目名称
        ,TRSFEE_NM                                                  --受让方名称/受让方名称
        ,CONT_SIGN_DT                                               --合同签订日期/合同签订日期
        ,CONT_EFF_DT                                                --合同生效日期/合同生效日期
        ,RSLT_PUBTY_START_DT                                        --结果公示起始时间/结果公示起始日期
        ,RSLT_PUBTY_END_DT                                          --结果公示结束时间/结果公示结束日期
        ,RSLT_PUBTY_PRD_WKDY                                        --结果公示周期（工作日）/结果公示周期（工作日）
        ,FNL_TRNSFR_PCT                                             --最终受让比例/最终受让比例
        ,DEAL_UNIT_PRIC                                             --NULL/成交单价
        ,IS_MRGN_OF_TXN_PRC                                         --保证金是否转交易价款/是否保证金转交易价款
        ,MRGN_OF_TXN_PRC_AMT                                        --NULL/保证金转交易价款金额
        ,MRGN_TF_PRC_AMT                                            --保证金转价款金额(万元)/保证金转价款金额
        ,MRGN_TF_PRC_TMPOT                                          --保证金转价款时点/保证金转价款时点
        ,MRGN_TF_PRC_DT                                             --保证金转价款日期/保证金转价款日期
        ,AVALB_MRGN_AMT                                             --NULL/可使用保证金金额
        ,TOACCT_PRC_AMT                                             --NULL/到账价款金额
        ,MRGN_AMT                                                   --保证金金额（万元）/保证金金额
        ,MRGN_DISPL_MTH_CD                                          --NULL/保证金处置方式代码
        ,MRGN_DISPL_MTH_CD_DSC                                      --NULL/保证金处置方式代码描述
        ,MRGN_TF_PRC_ORDER_ID                                       --保证金转价款订单ID/保证金转价款订单ID
        ,CAST(MRGN_TF_PRC_TM AS STRING) AS  MRGN_TF_PRC_TM                                             --保证金转价款时间/保证金转价款时间
        ,IS_MRGN_ADV_TSFT                                           --保证金提前划出/是否保证金提前划出
        ,PY_MTH_CD                                                  --支付方式/支付方式代码
        ,PY_MTH_CD_DSC                                              --码值/支付方式代码描述
        ,SURPL_PRC_SETL_MTH_CD                                      --剩余价款结算方式/剩余价款结算方式代码
        ,SURPL_PRC_SETL_MTH_CD_DSC                                  --码值/剩余价款结算方式代码描述
        ,DPAMT_AMT                                                  --首付金额(万元)/首付金额
        ,FISSU_PAY_PECT                                             --首期付款百分比/首期付款百分比
        ,BLPT_EXPI_DT                                               --尾款截止日期/尾款截止日期
        ,PAYB_PRC                                                   --应支付价款（万元）/应支付价款
        ,SCROT_PRC_AMT                                              --可划出价款金额（万元）/可划出价款金额
        ,OTHR_REASON                                                --其他原因/其他原因
        ,SETL_MTH_CD                                                --结算方式/结算方式代码
        ,SETL_MTH_CD_DSC                                            --码值/结算方式代码描述
        ,IS_FRN_CUR_SETL                                            --是否外币结算/是否外币结算
        ,FRN_CUR_CURR_CD                                            --外币币种/外币币种代码
        ,FRN_CUR_CURR_CD_DSC                                        --码值/外币币种代码描述
        ,AGDT_EXRT                                                  --约定日汇率/约定日汇率
        ,CONVT_FRN_CUR_AMT                                          --折算外币金额(万元)/折算外币金额
        ,ACTL_TXN_MTH_CD                                            --实际交易方式/实际交易方式代码
        ,ACTL_TXN_MTH_CD_DSC                                        --码值/实际交易方式代码描述
        ,CHAG_TXN_MTH_REASON                                        --更改交易方式原因/更改交易方式原因
        ,TXN_ORG_AUDIT_OPIN                                         --交易机构审核意见/交易机构审核意见
        ,DEAL_TP_CD                                                 --成交类型/成交类型代码
        ,DEAL_TP_CD_DSC                                             --码值/成交类型代码描述
        ,PRC_TSFT_FLG_CD                                            --价款划出标识/价款划出标识代码
        ,PRC_TSFT_FLG_CD_DSC                                        --码值/价款划出标识代码描述
        ,ATSFT_AMT                                                  --已划出金额（万元）/已划出金额
        ,PRC_GOIN_BSN_ORDER_ID                                      --价款划入业务订单ID/价款划入业务订单ID
        ,ORDER_NO_PLFORM                                            --订单编号（平台）/订单编号（平台）
        ,CATR_ID                                                    --创建人/创建人ID
        ,CATR_NM                                                    --名称/创建人名称
        ,CAST(CRT_TM  AS STRING) AS CRT_TM                                                     --创建时间/创建时间
        ,UPD_PSN_ID                                                 --更新人/更新人ID
        ,UPD_PSN_NM                                                 --名称/更新人名称
        ,MOD_TM                                                     --更新时间/更新时间
        ,PRJ_PRIN_ID                                                --NULL/项目负责人ID
        ,PRJ_PRIN_NM                                                --NULL/项目负责人名称
        ,PRJ_PRIN_PASS_DT                                           --NULL/项目负责人通过日期
        ,BSN_DEPT_PRIN_ID                                           --NULL/业务部门负责人ID
        ,BSN_DEPT_PRIN_NM                                           --NULL/业务部门负责人名称
        ,BSN_DEPT_PRIN_PASS_DT                                      --NULL/业务部门负责人通过日期
        ,TXN_AUDT_DEP_AUDITOR_ID                                    --NULL/交易审核部审核人ID
        ,TXN_AUDT_DEP_AUDITOR_NM                                    --NULL/交易审核部审核人名称
        ,TXN_AUDT_DEP_AUDITOR_PASS_DT                               --NULL/交易审核部审核人通过日期
        ,TRAS_DEP_PRIN_ID                                           --NULL/交易部负责人ID
        ,TRAS_DEP_PRIN_NM                                           --NULL/交易部负责人名称
        ,TRAS_DEP_PRIN_PASS_DT                                      --NULL/交易部负责人通过日期
        ,EXG_AUDITOR_ID                                             --NULL/交易所审核人ID
        ,EXG_AUDITOR_NM                                             --NULL/交易所审核人名称
        ,EXG_AUDITOR_PASS_DT                                        --NULL/交易所审核人通过日期
        ,CETR_PRIN_ID                                               --NULL/中心负责人ID
        ,CETR_PRIN_NM                                               --NULL/中心负责人名称
        ,CETR_PRIN_PASS_DT                                          --NULL/中心负责人通过日期
        ,CONT_ATCH                                                  --合同附件/合同附件
        ,IS_FST_TM_PRC_TSFT_AUDIT_PASS                              --第一次价款划出是否审核通过/是否第一次价款划出审核通过
        ,DEAL_REC_STAT_CD                                           --NULL/成交记录状态代码
        ,DEAL_REC_SERIAL_NO                                         --NULL/成交记录流水号
        ,DEAL_OVRL_RMRK                                             --NULL/成交总体备注
        ,RMRK                                              --备注/备注
FROM  TMP_A
where dt = ${dmp_day}