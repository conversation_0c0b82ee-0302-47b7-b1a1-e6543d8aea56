WITH all_data AS
(
	SELECT  DISTINCT 
            CASE WHEN a.lit_exg_cd_dsc = '上海联合产权交易所' THEN '上海' 
                 WHEN a.lit_exg_cd_dsc = '广东联合产权交易中心' THEN '广东' 
                 WHEN a.lit_exg_cd_dsc = '重庆联合产权交易所' THEN '重庆' 
                 WHEN a.lit_exg_cd_dsc = '深圳联合产权交易所' THEN '深圳' 
                 WHEN a.lit_exg_cd_dsc = '山东产权交易中心' THEN '山东' 
            END AS exch
	       ,a.prj_id                                                   AS ht_proj_no
	       ,a.prj_nm                                                   AS SUBJ_MATTER_NAME
	       ,CASE WHEN c.src_cd_dsc = '国务院国资委监管' THEN '央企'
	             WHEN c.src_cd_dsc = '中央其他部委监管' THEN '部委'  ELSE '' END AS cust_class
	       ,hqname                                                     AS BELONG_GROUP
	       ,sellername                                                 AS SELLER_FINCER_NAME
	       ,tfr_prc/10000                                              AS HT_AMT_W_YUAN
	       ,''                                                         AS AGENT_MEM
	       ,lit_start_dt                                               AS INFO_DCLO_BEGIN_DT
	       ,CASE WHEN a.txn_cgy_cd_dsc in('资产转让','小宗实物') THEN '实物'
	             WHEN a.txn_cgy_cd_dsc in('企业产权转让') THEN '产权转让'
	             WHEN a.txn_cgy_cd_dsc in('企业增资') THEN '企业增资' END      AS PROJ_TYPE
	       ,c.src_cd_dsc                                               AS CUSTD_TYPE
	       ,'' PROJ_BELONG_DEPT_NAME
	       ,'' PROJ_PRINC_NAME
	       ,'' SRC_FLAG
	       ,current_date() UDT_TM
	       ,'爬虫系统'                                                     AS DATA_SOURCE
	FROM dwd.dwd_stb_prj_fct a
	LEFT JOIN ods.ods_pcxt_sellerinfo b
	ON a.prj_id = b.projectcode AND b.dt = '${dmp_day}'
	LEFT JOIN
	(
		SELECT  src_cd_val
		       ,src_cd_dsc
		FROM dim.dim_pub_cd_val_src_trgt_mpng
		WHERE pub_cd_no = 'CD000042'
		AND src_tab_eng_nm = 'sdm.s0_bot_sellerinfo'
	) c
	ON b.monitorName = c.src_cd_val left anti
	JOIN
	(
		SELECT  ht_proj_no
		FROM ods.ods_bl_center_formal_dcl
		WHERE dt = '${dmp_day}' 
	) d
	ON a.prj_id = d.ht_proj_no
	WHERE a.dt = '${dmp_day}'
	AND a.lit_exg_cd_dsc IN ('上海联合产权交易所', '广东联合产权交易中心', '重庆联合产权交易所', '深圳联合产权交易所', '山东产权交易中心')
	AND TO_DATE(SUBSTR(a.lit_start_dt, 1, 10)) >= DATE_SUB(TO_DATE(FROM_UNIXTIME(UNIX_TIMESTAMP('${dmp_day}', 'yyyyMMdd'), 'yyyy-MM-dd')), 35) 
)
SELECT  EXCH
       ,HT_PROJ_NO
       ,SUBJ_MATTER_NAME
       ,CUST_CLASS
       ,BELONG_GROUP
       ,SELLER_FINCER_NAME
       ,HT_AMT_W_YUAN
       ,AGENT_MEM
       ,INFO_DCLO_BEGIN_DT
       ,PROJ_TYPE
       ,CUSTD_TYPE
       ,PROJ_BELONG_DEPT_NAME
       ,PROJ_PRINC_NAME
       ,SRC_FLAG
       ,UDT_TM
       ,DATA_SOURCE
-- 挂牌项目编号、标的名称、转让方、挂牌金额、信息披露起始日期 都一致时，进行去重只保留一条 20250703 
FROM
(
	SELECT  *
	       ,ROW_NUMBER() OVER ( PARTITION BY ht_proj_no,SUBJ_MATTER_NAME,SELLER_FINCER_NAME,HT_AMT_W_YUAN,INFO_DCLO_BEGIN_DT ORDER BY  INFO_DCLO_BEGIN_DT DESC ) AS rn
	FROM all_data
) t
WHERE rn = 1