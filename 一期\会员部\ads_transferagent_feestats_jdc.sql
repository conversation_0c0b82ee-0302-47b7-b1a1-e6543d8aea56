with temp_cust as (
    select 
       DISTINCT
       a.rltv_prj as prj_wrd,
       a.seller_fincer_attr_inves_sbj,
       b.cust_full_nm as seller_fincer_name,
       a.tsk_rcv_side_unit,
       d.compy_name as agent_mem,
       d.agent_no
from dwd.dwd_bid_task_list_info a
         left join dim.dim_pty_cust b on 'BJHL'||a.seller_fincer_attr_inves_sbj = b.cust_wrd and b.dt = '${dmp_day}'
         left join dim.dim_pty_cust c on 'BJHL'||a.tsk_rcv_side_unit = c.cust_wrd and c.dt = '${dmp_day}'
         left join ods.ods_bl_agent_info d on a.tsk_rcv_side_unit=d.cust_no and d.dt='${dmp_day}'
where a.dt = '${dmp_day}'
)

select
    DISTINCT
    a.prj_id             as proj_no,
       a.prj_nm             as proj_name,
       d.agent_mem  as agent_mem,
       a.lit_amt/10000           as sell_price,
       e.sprc               as starting_bid,
       b.txn_prc/10000 as deal_price,
       b.deal_amt/10000           as deal_amt,
       t3.fwfze as fee_amt,
       t3.bjssr as cbex_fee_amt,
       ''                   as disposal_method,
       a.txn_mth_cd_dsc as trans_method,
       a.txn_stat_cd_dsc   as trans_status,
       b.delvry_dt    as deal_date,
       substr(lit_star_dt,1,4)||'-'||substr(lit_star_dt,5,2)||'-'||substr(lit_star_dt,7,2) as free_bid_start_time,
       substr(lit_end_dt,1,4)||'-'||substr(lit_end_dt,5,2)||'-'||substr(lit_end_dt,7,2) as free_bid_end_time,
       t2.org_nm  as proj_belong_dept_name,
       t1.usr_nm        as proj_princ_name


from dwd.dwd_prj_fct a
         left join dwd.dwd_evt_deal_rec_fct b on a.prj_wrd = b.prj_wrd and b.dt = '${dmp_day}'
         left join dwd.dwd_ittn_buyer_fct c
                   on b.bsn_buyer_id = c.bsn_buyer_id and a.prj_wrd = c.prj_wrd and c.dt = '${dmp_day}'
         left join temp_cust d on a.prj_wrd=d.prj_wrd
         left join 
         (
          select 
          DISTINCT
          prj_wrd,sprc,actl_star_dt,actl_end_dt 
          from dim.dim_bid_sesi_info 
            where dt='${dmp_day}' 
            and edw_end_dt = '20991231'
         ) e 
         on a.prj_wrd=e.prj_wrd
         left join dwd.dwd_bid_task_list_info t on a.prj_wrd=t.rltv_prj and t.dt='${dmp_day}'
         left join dim.dim_user_info t1 on cast(t.src_bsn_hdl as String) = t1.usr_id and t1.dt = '${dmp_day}' and
                                          t1.edw_dt_src_tbl = 'livebos.tuser'
         left join dim.dim_org_info t2 on cast(t.prj_blng_dept as String)=t2.org_id and t2.dt='${dmp_day}'
         left join dwd.dwd_prj_fee_fct t3 on a.prj_id=t3.prj_id and t3.dt='${dmp_day}'
where a.dt = '${dmp_day}'
  and a.prj_bsn_tp_cd = '1B'
  and b.delvry_dt is not null
  and d.agent_no is not null