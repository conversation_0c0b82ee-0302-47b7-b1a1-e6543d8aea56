SELECT
roles AS sub_rl,
zzsl AS addval_tax_rate,
case when t2.is_cnvr = 'N' then t1.rhqd else case when t2.cd_val is null then 'W-'||t1.rhqd else t2.cd_val end end AS enroll_list, 
t2.cd_val_nm as enroll_list_dsc,--入会清单描述
case when t3.is_cnvr = 'N' then t1.hyzt else case when t3.cd_val is null then 'W-'||t1.hyzt else t3.cd_val end end AS mbsh_sts,
t3.cd_val_nm as mbsh_sts_dsc,--会员状态描述
yxq AS avl_dt,
case when t4.is_cnvr = 'N' then t1.nsrlx else case when t4.cd_val is null then 'W-'||t1.nsrlx else t4.cd_val end end AS tax_pymt_psn_tp,
t4.cd_val_nm as tax_pymt_psn_tp_dsc,--纳税人类型描述
rhsj AS enroll_dt,
glinstid AS rltv_instid,
roleid AS orig_sub_rl,
orgid AS blng_org,
hymc AS mbsh_nm,
case when t5.is_cnvr = 'N' then t1.hylx else case when t5.cd_val is null then 'W-'||t1.hylx else t5.cd_val end end AS mbsh_tp,
t5.cd_val_nm as mbsh_tp_dsc,--会员类型描述
case when t6.is_cnvr = 'N' then t1.hylb else case when t6.cd_val is null then 'W-'||t1.hylb else t6.cd_val end end AS mbsh_cgy,
t6.cd_val_nm as mbsh_cgy_dsc,--会员类别描述
case when t7.is_cnvr = 'N' then t1.dy else case when t7.cd_val is null then 'W-'||t1.dy else t7.cd_val end end AS loc,
t7.cd_val_nm as loc_dsc,--地域描述
yhkh AS bnk_card_no,
yhlhh AS dep_bnk_bnk_cd,
zhmc AS subbr_nm,
case when t8.is_cnvr = 'N' then t1.khyh else case when t8.cd_val is null then 'W-'||t1.khyh else t8.cd_val end end AS opn_acc_bnk,
t8.cd_val_nm as opn_acc_bnk_dsc,--开户银行描述
khh AS cust_no,
hybh AS id,
zdhy AS key_mbsh, 
zyfwhylb AS prof_srv_mem_type,
CASE
    WHEN zyfwhylb = '1' THEN '专项服务机构'
    WHEN zyfwhylb = '2' THEN '招投标机构'
    WHEN zyfwhylb = '3' THEN '法律服务机构'
    WHEN zyfwhylb = '4' THEN '审计服务机构'
    WHEN zyfwhylb = '5' THEN '评估服务机构'
    WHEN zyfwhylb = '6' THEN '拍卖机构'
    ELSE NULL
END AS prof_srv_mem_type_dsc
FROM ods.ods_bjhl_tbid_fwhyfsxx t1
left join dim.dim_pub_cd_val_src_trgt_mpng  t2                                                                                                       
  on t1.rhqd = t2.src_cd_val and t2.src_tab_eng_nm = 'bid.tbid_fwhyfsxx' and t2.pub_cd_no= 'CD000025' and t2.src_col_eng_nm = 'RHQD'           
left join dim.dim_pub_cd_val_src_trgt_mpng  t3                                                                                                       
  on t1.hyzt = t3.src_cd_val and t3.src_tab_eng_nm = 'bid.tbid_fwhyfsxx' and t3.pub_cd_no= 'CD000024' and t3.src_col_eng_nm = 'HYZT'             
left join dim.dim_pub_cd_val_src_trgt_mpng  t4                                                                                                       
  on t1.nsrlx = t4.src_cd_val and t4.src_tab_eng_nm = 'bid.tbid_fwhyfsxx' and t4.pub_cd_no= 'CD000023' and t4.src_col_eng_nm = 'NSRLX'                 
left join dim.dim_pub_cd_val_src_trgt_mpng  t5                                                                                                       
  on t1.hylx = t5.src_cd_val and t5.src_tab_eng_nm = 'bid.tbid_fwhyfsxx' and t5.pub_cd_no= 'CD000022' and t5.src_col_eng_nm = 'HYLX'           
left join dim.dim_pub_cd_val_src_trgt_mpng  t6                                                                                                       
  on t1.hylb = t6.src_cd_val and t6.src_tab_eng_nm = 'bid.tbid_fwhyfsxx' and t6.pub_cd_no= 'CD000021' and t6.src_col_eng_nm = 'HYLB'
left join dim.dim_pub_cd_val_src_trgt_mpng  t7                                                                                                       
  on t1.dy = t7.src_cd_val and t7.src_tab_eng_nm = 'bid.tbid_fwhyfsxx' and t7.pub_cd_no= 'CD000020' and t7.src_col_eng_nm = 'DY'           
left join dim.dim_pub_cd_val_src_trgt_mpng  t8                                                                                                       
  on t1.khyh = t8.src_cd_val and t8.src_tab_eng_nm = 'bid.tbid_fwhyfsxx' and t8.pub_cd_no= 'CD000019' and t8.src_col_eng_nm = 'KHYH' 
WHERE dt=${dmp_day}