with temp as(
SELECT
    belong_group,
        belong_group1,
        PROJ_BELONG_DEPT_NAME,
        PROJ_PRINC_NAME, 
        udt_tm
FROM (
    SELECT
        belong_group,
        belong_group1,
        PROJ_BELONG_DEPT_NAME,
        PROJ_PRINC_NAME,  
        udt_tm,
        ROW_NUMBER() OVER (PARTITION BY belong_group ORDER BY udt_tm DESC) AS rn
    FROM
        (select * from ods.ods_bl_center_cust_bel_dept where dt='${dmp_day}')
) AS subquery
WHERE
    subquery.rn = 1
),
temp1 as(
SELECT 
substr(x.info_publ_start_dt, 1, 7) as data_dt,
COALESCE(x.Lit_Amt,0)/10000 as Lit_Amt, --挂牌金额（万元）
x.is_repeat as is_repeat_ht, --是否重复挂牌
x.prj_no as proj_no, -- 项目编号
x.prj_nm as proj_name, --项目名称
x.info_publ_start_dt as info_dclo_begin_dt, --信息披露起始日期
x.Info_Publ_Exprt_Dt as info_dclo_expire_dt, --信息披露期满日期
x1.belong_group1 as belong_group, -- 所属集团
x.prj_prt_nm  as seller_fincer_name, --转让方名称/融资方名称
x.prj_prt_sown_spvs_org_cd_dsc as oasset_custd_org, --国资监管机构
 --央企三部三部的数据需要映射码表取具体的所属部门
case when x.prj_blng_dept_nm ='央企三部' then x1.PROJ_BELONG_DEPT_NAME else x.prj_blng_dept_nm end  as proj_belong_dept_name, --所属部门
case when x.prj_blng_dept_nm ='央企三部' then x1.PROJ_PRINC_NAME else x.prj_prin_nm end  as proj_princ_name, --项目负责人
x.txn_mth_cd_dsc as deal_way_name,--成交方式
case when x.deal_amt-x.tfr_prc>0 then '是'else '否' end  as is_incre, --是否增值
x.exch -- 挂牌交易所
FROM dws.dws_all_trsfer_info x
 left join temp x1 on x.Blng_Org=x1.belong_group
where  
prj_blng_dept_nm in('央企一部','央企二部','央企三部','央企四部','央企五部','央企六部') and x.dt=${dmp_day}
and x.Lit_Amt>=100000000
and (
  x.prj_bsn_tp_cd_dsc not in ('产权转让', '企业增资')
  OR (
    x.prj_bsn_tp_cd_dsc in ('产权转让', '企业增资')
    AND (x.prj_prt_sown_spvs_org_cd_dsc not IN(
      '省级财政部门监管',
      '市级财政部门或金融办监管',
      '省级国资委监管',
      '省级其他部门监管',
      '市级国资委监管',
      '市级其他部门监管'
    )
    OR (x.exch = '北交所' AND x.prj_prt_sown_spvs_org_cd_dsc IS NULL))
  )
)),
temp2 as(
SELECT 
        a.*,
         ROW_NUMBER() OVER (
            PARTITION BY data_dt, SPLIT(proj_no, '-')[0]
            ORDER BY Lit_Amt DESC, 
                     CAST(SPLIT(proj_no, '-')[1] AS INT) ASC
        ) AS rn
    FROM 
        temp1 a
    WHERE 
        is_repeat_ht = '是'

)
select 
Lit_Amt,is_repeat_ht,proj_no,proj_name,info_dclo_begin_dt,info_dclo_expire_dt,belong_group,seller_fincer_name,oasset_custd_org,proj_belong_dept_name,
proj_princ_name,deal_way_name,is_incre,exch 
from temp1 where is_repeat_ht = '否'
union all
SELECT
Lit_Amt,is_repeat_ht,proj_no,proj_name,info_dclo_begin_dt,info_dclo_expire_dt,belong_group,seller_fincer_name,oasset_custd_org,proj_belong_dept_name,
proj_princ_name,deal_way_name,is_incre,exch 
from temp2 where rn=1
