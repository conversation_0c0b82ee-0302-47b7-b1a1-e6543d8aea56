with temp_cust as (
    select 
       DISTINCT
       a.prj_wrd,
       a.trsfer_cust_wrd,
       b.cust_full_nm as seller_fincer_name,
       a.task_recver_cust_wrd,
       D.compy_name as agent_mem,
       D.agent_no
from dwd.dwd_evt_task_list_fct a
         left join dim.dim_pty_cust b on a.trsfer_cust_wrd = b.cust_wrd and b.dt = '${dmp_day}'
         left join dim.dim_pty_cust c on a.task_recver_cust_wrd = c.cust_wrd and c.dt = '${dmp_day}'
         left join ods.ods_bl_agent_info d on a.task_recver_cust_wrd='BJHL'||d.cust_no and d.dt='${dmp_day}'
where a.dt = '${dmp_day}'
)

select
    a.prj_id             as proj_no,
       a.prj_nm             as proj_name,
       d.seller_fincer_name         as seller_fincer_name,
       d.agent_mem  as agent_mem,
       a.lit_amt/10000            as sell_price,
       e.sprc/10000               as starting_bid,
       b.deal_amt/10000           as deal_amt,
       g.fwfze as fee_amt,
       g.bjssr as cbex_fee_amt,


       ''                   as disposal_method,
       b.deal_stat_cd_dsc   as deal_status,
       b.delvry_dt    as deal_date,
       e.actl_star_dt as free_bid_start_time,
       e.actl_end_dt as free_bid_end_time,
       a.prj_blng_dept_nm  as proj_belong_dept_name,
       a.prj_prin_nm_ipubl        as proj_princ_name


from dwd.dwd_prj_fct a
         left join dwd.dwd_evt_deal_rec_fct b on a.prj_wrd = b.prj_wrd and b.dt = '${dmp_day}'
         left join dwd.dwd_ittn_buyer_fct c
                   on b.bsn_buyer_id = c.bsn_buyer_id and a.prj_wrd = c.prj_wrd and c.dt = '${dmp_day}'
         left join temp_cust d on a.prj_wrd=d.prj_wrd
         left join 
         (
          select 
          DISTINCT
          prj_wrd,sprc,actl_star_dt,actl_end_dt 
          from dim.dim_bid_sesi_info 
            where dt='${dmp_day}' 
            and edw_end_dt = '20991231'
         ) e 
         on a.prj_wrd=e.prj_wrd 
         left join dwd.dwd_evt_task_list_fct f on a.prj_wrd=f.prj_wrd and f.dt='${dmp_day}'
         left join dwd.dwd_prj_fee_fct g on a.prj_id=g.prj_id and g.dt='${dmp_day}'
where a.dt = '${dmp_day}'
  and a.prj_bsn_tp_cd = '1F'
  and b.deal_dt is not null
  and d.agent_no is not null


