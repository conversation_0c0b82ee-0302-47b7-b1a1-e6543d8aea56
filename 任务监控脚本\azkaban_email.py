# -*- coding: utf-8 -*-

from datetime import datetime
import requests
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

# Azkaban URL 和凭据


# 生产地址
azkaban_url = 'http://*************:18081'
# 准生产地址
# azkaban_url = 'http://*************:18081'
username = 'azkaban'
password = 'azkaban'
project_id = 'dataflow_11ef65fb284d3643904dab7855587e1f'
#准生产
#project_id='sparkJob_11ef224f03112266b2a2d991dfd8431e'
flow = 'base'

# 邮件配置
smtp_server = 'smtp.qq.com'  # 邮件服务器地址
smtp_port = 587  # 使用465端口如果是SSL
email_user = 'XXXX'  # 你的邮箱
email_password = 'XXXX'  # 邮箱密码
recipient_emails = ['<EMAIL>', 'chenhong<PERSON><PERSON>@yuanian.com', '<EMAIL>', '<EMAIL>']  # 收件人列表


# 登录到 Azkaban 并获取 session ID
def azkaban_login(azkaban_url, username, password):
    login_url = f'{azkaban_url}/index'
    data = {
        'action': 'login',
        'username': username,
        'password': password
    }
    response = requests.post(login_url, data=data)
    response.raise_for_status()
    response_json = response.json()
    print("Login Response:", response_json)  # 调试信息
    return response_json['session.id']


# 获取最新的执行 ID
def get_latest_execution_id(azkaban_url, session_id, project_id):
    executions_url = f'{azkaban_url}/manager'
    params = {
        'ajax': 'fetchFlowExecutions',
        'project': project_id,
        'flow': flow,
        'start': 0,
        'length': 16,
        'session.id': session_id
    }
    response = requests.get(executions_url, params=params)
    response.raise_for_status()
    response_json = response.json()
    print("Executions Response:", response_json)  # 调试信息
    executions = response_json.get('executions', [])
    if executions:
        return executions[0]['execId']
    else:
        raise Exception('找不到指定项目的执行记录')


# 获取最新执行的结果
def get_execution_result(azkaban_url, session_id, exec_id):
    execution_url = f'{azkaban_url}/executor'
    params = {
        'ajax': 'fetchexecflow',
        'execid': exec_id,
        'session.id': session_id
    }
    response = requests.get(execution_url, params=params)
    response.raise_for_status()
    response_json = response.json()
    print("Execution Result Response:", response_json)  # 调试信息
    return response_json


# 发送邮件
def send_email(subject, body):
    msg = MIMEMultipart()
    msg['From'] = email_user
    msg['To'] = ", ".join(recipient_emails)  # 将多个收件人合并为一个字符串
    msg['Subject'] = subject

    # 邮件正文
    msg.attach(MIMEText(body, 'html'))

    # 发送邮件
    try:
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()  # 如果使用TLS
        server.login(email_user, email_password)
        server.sendmail(email_user, recipient_emails, msg.as_string())
        server.quit()
        print("邮件已发送")
    except Exception as e:
        print(f"发送邮件失败: {e}")


# 转换时间戳
def convert_timestamp(timestamp):
    if timestamp:
        return datetime.fromtimestamp(timestamp / 1000)
    return None


# 主程序
try:
    session_id = azkaban_login(azkaban_url, username, password)
    print(session_id)
    latest_exec_id = get_latest_execution_id(azkaban_url, session_id, project_id)
    execution_result = get_execution_result(azkaban_url, session_id, latest_exec_id)
    print("Latest Execution Result:")
    print(execution_result)

    start_time = execution_result.get('startTime')
    end_time = execution_result.get('endTime')
    status = execution_result.get('status')
    start_time_formatted = convert_timestamp(start_time)
    end_time_formatted = convert_timestamp(end_time)

    if start_time_formatted and end_time_formatted:
        time_diff = end_time_formatted - start_time_formatted
        duration_minutes = time_diff.total_seconds() / 60
    else:
        duration_minutes = None

    print("Latest Execution Details:")
    print(f"Start Time: {start_time_formatted}")
    print(f"End Time: {end_time_formatted}")
    print(f"Status: {status}")
    print(f"运行时长：{duration_minutes}")
    # 仅在状态为失败时发送邮件
    if status != 'SUCCEEDED':
        start_time_str = start_time_formatted.strftime('%Y-%m-%d %H:%M:%S') if start_time_formatted else 'N/A'
        end_time_str = end_time_formatted.strftime('%Y-%m-%d %H:%M:%S') if end_time_formatted else 'N/A'
        duration_str = f"{duration_minutes:.2f} 分" if duration_minutes is not None else 'N/A'

        # 格式化状态
        if status == 'FAILED':
            status_str = "<b style='color:red'>失败</b>"
        else:
            status_str = f"<b style='color:orange'>{status}</b>"

        email_body = (
            f"<h3>数据流运行监控</h3>"
            f"<p><b>开始时间</b>: {start_time_str}</p>"
            f"<p><b>结束时间</b>: {end_time_str}</p>"
            f"<p><b>运行时长</b>: {duration_str}</p>"
            f"<p><b>状态</b>: {status_str}</p>"
        )

        # 发送邮件
        send_email("Azkaban 任务执行失败报告", email_body)

except Exception as e:
    print(f"错误: {e}")