select
    t.id,
    t.project_code,
    t.project_name,
    t.trade_category,
    t.trade_category_dsc,
    t.reference_price_tp,
    t.reference_price_w_yuan,
    t.reference_price_min_w_yuan,
    t.reference_price_max_w_yuan,
    t.prc_cmnt,
    t.industry_tp,
    t.industry_tp_dsc,
    t.industry,
    t.wbt_prov,
    a.xzqymc as wbt_prov_nm,
    t.wbt_city,
    b.xzqymc as wbt_city_nm,
    t.wbt_cnty_and_dstc,
    c.xzqymc as wbt_cnty_and_dstc_nm,
    t.esr_tp,
    'BJHL'||cast(t.pltfrm_prj_id as String) as prj_wrd,
    t.pro_pps_pnp,
    t.dept_pnp,
    t.esr_beg_dt,
    t.esr_ancm_prd,
    t.esr_exp_dt,
    t.exchange_type,
    t.prj_pnp,
    t.prj_blng_dept,
    t.prj_sts,
    t.prj_sts_dsc,
    t.seller_fincer_attr_inves_sbj,
    t.sshymc
    from std.std_bjhl_txxpl_xxpl_d t
    left join ods.ods_bjhl_txzqydm a on t.wbt_prov=a.xzqydm and a.dt='${dmp_day}'
    left join ods.ods_bjhl_txzqydm b on t.wbt_city=b.xzqydm and b.dt='${dmp_day}'
    left join ods.ods_bjhl_txzqydm c on t.wbt_cnty_and_dstc=c.xzqydm and c.dt='${dmp_day}'
where t.dt='${dmp_day}' and t.prj_src!=2