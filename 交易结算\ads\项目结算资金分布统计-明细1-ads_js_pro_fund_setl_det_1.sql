SELECT  t.cptl_lo --资金位置	
		,t.proj_no --项目编号	
		,proj_name --项目名称	
		,t.proj_type -- 业务类型	
		,t.fund_type AS fund_type -- 资金类型	
		,t.trns_date AS trans_date -- 交易日期
		,t.prj_amt -- 项目余额
		,t.deposit_amt --入金金额（万元）	
		,t.withdrawal_amount AS withdrawal_amt -- 出金金额	
    	,regexp_replace(replace(c.deposit_date,'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS deposit_date -- 入金日期
		,regexp_replace(replace(c.withdrawal_date,'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS withdrawal_date -- 出金日期	
		,CASE WHEN t.proj_type = '企业增资' THEN t.lit_amt ELSE t.lit_amt/10000 END AS lit_amt-- 挂牌金额（万元）
		,COALESCE(CASE WHEN t.proj_type = '企业增资' THEN t.lit_amt_mun ELSE t.lit_amt_mun/10000 END,0) AS lit_amt_mun-- 挂牌金额（万元）	
		,t.interest_rate AS intrest_rate--利率				
		,b.concatenated_bz AS note -- 备注		
from dws.dws_crj_rcd_summary t
INNER JOIN (
	-- 备注    
	SELECT 
		proj_no,
		CONCAT_WS('\n', COLLECT_LIST(NAMED_STRUCT('bz', bz, 'date', trans_date).bz)) AS concatenated_bz
	FROM 
		(SELECT proj_no,trans_date,cptl_type_lrgcls,crj_flag,bz
		FROM (
				select 
				ordr_prj_no as proj_no,cptl_type_lrgcls,crj_flag,
				bank_to_acc_date as trans_date,
				regexp_replace(replace(bank_to_acc_date,'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3')  || cptl_type_lrgcls || case when ast_type = '保证金转价款' then '(保证金转价款)' ELSE '' END || ':' || crj_flag || '金额 ' || round(amt,2) || ';' AS bz
				from dws.dws_trans_setl_info
				where cptl_type_lrgcls = '价款' 
				AND dt = '${dmp_day}'
				union ALL
				select 
				ordr_prj_no as proj_no,cptl_type_lrgcls,crj_flag,
				bank_to_acc_date as trans_date,
				regexp_replace(replace(bank_to_acc_date,'-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') || cptl_type_lrgcls || case when ast_type = '保证金转价款' then '(保证金转价款)' ELSE '' END || ':' || crj_flag || '金额 ' || round(amt,2) || ';' AS bz
				from dws.dws_trans_setl_info
				where cptl_type_lrgcls = '保证金' 
						AND (
						CONCAT(ordr_prj_no,cust_name) IN 
						(
						SELECT CONCAT(project_code,buyer_anm) isbuyer FROM dwd.dwd_evt_itrsfee_fct 
						WHERE is_buyer = '是' AND dt = '${dmp_day}'
						UNION ALL
						-- 联合受让方
						SELECT  
							CONCAT(a.project_code,b.mbr_nm) as isbuyer
						FROM dwd.dwd_evt_itrsfee_fct a
						LEFT JOIN (select buyer_id,mbr_nm from std.std_bjhl_tcqzr_yxsrf_cy_d  where dt = '${dmp_day}') b
						ON a.buyer_id = b.buyer_id
						AND a.is_joint_transferee = 1
						WHERE is_buyer = '是'
						AND b.mbr_nm IS NOT NULL
						AND dt = '${dmp_day}'
						UNION ALL
						-- 房屋出租和资产的需要单取
						SELECT  concat(b.prj_id,c.cust_full_nm) isbuyer
						FROM std.std_bjhl_tbid_cjjl_d a
						LEFT JOIN dwd.dwd_prj_fct b
						ON a.project_id = b.plform_prj_id AND a.dt = b.dt
						LEFT JOIN
						(
								SELECT  DISTINCT bsn_stm_cust_no
									,cust_full_nm
								FROM dim.dim_pty_cust
								WHERE dt = '${dmp_day}' 
						) c
						ON a.cust_no = c.bsn_stm_cust_no
						WHERE a.dt = '${dmp_day}'
						AND a.entrst_cgy = '1'
						AND substr(b.prj_bsn_tp_cd_dsc, 0, 4) IN ('诉讼资产', '房屋出租')
						)
						OR ast_type = '保证金转价款'
						) 
				AND dt = '${dmp_day}'
		) k
		DISTRIBUTE BY proj_no
		SORT BY proj_no, 
		CASE WHEN cptl_type_lrgcls = '保证金' THEN 1 ELSE 2 END,  -- 第一级排序
				CASE WHEN crj_flag = '入金' THEN 1 ELSE 2 END,            -- 第二级排序
				trans_date ASC) t
	GROUP BY 
		proj_no
) b ON t.proj_no = b.proj_no
LEFT JOIN (
		SELECT ordr_prj_no,
         cptl_type_lrgcls,
	       min(CASE WHEN crj_flag = '入金' THEN bank_to_acc_date ELSE NULL END) AS deposit_date,
	       max(CASE WHEN crj_flag = '出金' THEN bank_to_acc_date ELSE NULL END) AS withdrawal_date
	FROM dws.dws_trans_setl_info
	WHERE DT = ${dmp_day}
	  --AND bsn_type IN ('珍品', '小宗实物', '诉讼资产', '房屋出租', '产权转让') 
	  AND cptl_type_lrgcls = '价款'
	 GROUP BY ordr_prj_no,cptl_type_lrgcls
	  UNION ALL 
	 SELECT ordr_prj_no,
        cptl_type_lrgcls,
	       min(CASE WHEN crj_flag = '入金' THEN bank_to_acc_date ELSE NULL END) AS deposit_date,
	       max(CASE WHEN crj_flag = '出金' THEN bank_to_acc_date ELSE NULL END) AS withdrawal_date
	FROM dws.dws_trans_setl_info
	WHERE DT = ${dmp_day}
	  --AND bsn_type IN ('珍品', '小宗实物', '诉讼资产', '房屋出租', '产权转让') 
	  AND cptl_type_lrgcls = '保证金'
	  AND (CONCAT(ordr_prj_no,cust_name) IN 
	  (SELECT CONCAT(project_code,buyer_anm) isbuyer FROM dwd.dwd_evt_itrsfee_fct WHERE is_buyer = '是' AND dt = '${dmp_day}'
	  UNION ALL
	    -- 联合受让方
		SELECT  
			CONCAT(a.project_code,b.mbr_nm) as isbuyer
		FROM dwd.dwd_evt_itrsfee_fct a
		LEFT JOIN (select buyer_id,mbr_nm from std.std_bjhl_tcqzr_yxsrf_cy_d  where dt = '${dmp_day}') b
		ON a.buyer_id = b.buyer_id
		AND a.is_joint_transferee = 1
		WHERE is_buyer = '是'
		AND b.mbr_nm IS NOT NULL
		AND dt = '${dmp_day}'
	  UNION ALL
		-- 房屋出租和资产的需要单去
		SELECT  concat(b.prj_id,c.cust_full_nm) isbuyer
		FROM std.std_bjhl_tbid_cjjl_d a
		LEFT JOIN dwd.dwd_prj_fct b
		ON a.project_id = b.plform_prj_id AND a.dt = b.dt
		LEFT JOIN
		(
			SELECT  DISTINCT bsn_stm_cust_no
				,cust_full_nm
			FROM dim.dim_pty_cust
			WHERE dt = '${dmp_day}' 
		) c
		ON a.cust_no = c.bsn_stm_cust_no
		WHERE a.dt = '${dmp_day}'
		AND a.entrst_cgy = '1'
		AND substr(b.prj_bsn_tp_cd_dsc, 0, 4) IN ('诉讼资产', '房屋出租')
	  ) 
	  OR ast_type = '保证金转价款')
	GROUP BY ordr_prj_no,cptl_type_lrgcls
) c
ON t.proj_no = c.ordr_prj_no and c.cptl_type_lrgcls = t.fund_type
WHERE DT=${dmp_day}
AND (t.prj_amt != 0 OR t.deposit_amt != 0 OR t.withdrawal_amount != 0)