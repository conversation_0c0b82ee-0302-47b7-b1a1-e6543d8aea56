-- Active: 1725412082987@@10.254.99.145@10000@dws
SELECT  category --类别 
       ,SUM(CASE WHEN Trans_type = '拍卖' THEN 1 ELSE 0 END)                     AS pm_trans_num --拍卖交易数 
       ,SUM(CASE WHEN Trans_type = '拍卖' THEN deal_amt ELSE 0 END)/10000        AS pm_trans_amt --拍卖交易额 
       ,SUM(CASE WHEN Trans_type = '招投标' THEN 1 ELSE 0 END)                    AS ztb_trans_num --招投标交易数 
       ,SUM(CASE WHEN Trans_type = '招投标' THEN deal_amt ELSE 0 END)/10000       AS ztb_trans_amt --招投标交易额 
       ,SUM(CASE WHEN Trans_type = '网络竞价' THEN 1 ELSE 0 END)                   AS wljj_trans_num --网络竞价交易数 
       ,SUM(CASE WHEN Trans_type = '网络竞价' THEN deal_amt ELSE 0 END)/10000      AS wljj_trans_amt --网络竞价交易额 
       ,SUM(CASE WHEN Trans_type = '动态报价' THEN 1 ELSE 0 END)                   AS dtbj_trans_num --动态报价交易数 
       ,SUM(CASE WHEN Trans_type = '动态报价' THEN deal_amt ELSE 0 END)/10000      AS dtbj_trans_amt --动态报价交易额 
       ,SUM(CASE WHEN Trans_type = '协议转让' THEN 1 ELSE 0 END)                   AS xyzr_trans_num --协议转让交易数 
       ,SUM(CASE WHEN Trans_type = '协议转让' THEN deal_amt ELSE 0 END)/10000      AS xyzr_trans_amt --协议转让交易额 
       ,SUM(CASE WHEN Trans_type LIKE '%其他竞价%' THEN 1 ELSE 0 END)              AS qt_trans_num --其他交易数 
       ,SUM(CASE WHEN Trans_type LIKE '%其他竞价%' THEN deal_amt ELSE 0 END)/10000 AS qt_trans_amt --其他交易额 
       ,SUM(added_amt)                                                         AS added_amt --增值金额 
       ,deal_date --成交日期 
FROM
(
	SELECT  CASE WHEN prj_bsn_tp_cd = 'GQ' THEN '企业产权'
	             WHEN CONCAT(prj_bsn_tp_cd,zcly) LIKE '1B行政事业%' OR CONCAT(prj_bsn_tp_cd,zcly) LIKE '1B企业资产%' THEN '企业实物资产'
	             WHEN CONCAT(prj_bsn_tp_cd,zcly) = '1D企业实物资产' THEN '企业实物资产'
	             WHEN CONCAT(prj_bsn_tp_cd,zcly) = '1D行政事业单位实物资产' THEN '行政事业资产'  ELSE NULL END       AS category -- 类别 
	       ,deal_amt                                                                                 AS Deal_amt -- 成交金额 
	       ,CASE WHEN eval_prc = '' OR eval_prc IS NULL THEN 0  ELSE (deal_amt - eval_prc)/10000 END AS Added_amt --增值金额 
	       ,prj_no                                                                                   AS Prj_no -- 项目编码 
	       ,txn_mth_cd_dsc                                                                           AS Trans_type --交易方式 
	       ,BSN_REC_DEAL_DT                                                                          AS Deal_date --成交日期 
	FROM dws.dws_all_trsfer_info
	WHERE ( CONCAT(prj_bsn_tp_cd, prj_tp_cd) = 'GQ1' --企业产权只取正式披露的 
 OR (CONCAT(prj_bsn_tp_cd, is_owned_state) = '1D1' AND zcly IN ('企业实物资产', '行政事业单位实物资产')) OR (CONCAT(prj_bsn_tp_cd, txn_mth_cd_dsc) = '1B动态报价' AND (zcly LIKE '行政事业%' OR zcly LIKE '企业资产%')) )
	AND bsn_rec_deal_dt IS NOT NULL -- 成交日期不为空 
	AND exch = '北交所'
	AND dt = '${dmp_day}' 
) T
WHERE category IS NOT NULL
GROUP BY  category
         ,added_amt
         ,deal_date