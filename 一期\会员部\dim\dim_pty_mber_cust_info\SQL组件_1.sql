--新增
select
T1.bsn_src               ,--业务来源,来源系统编码    
T1.edw_dt_src_tbl        ,--EDW数据来源主表名     
T1.MBER_WRD  AS  edw_pk_unq_flg        ,--EDW主键唯一标识      
T1.edw_non_pk_md5        ,--EDW非主键MD5      
${dmp_day} AS edw_star_dt           ,--EDW开始日期        
'********' AS edw_end_dt            ,--EDW结束日期        
'增加'  AS  edw_dlt_flg           ,--IUD标志(增加、修改、删除)
T1.edw_data_dt           ,--EDW数据日期        
T1.edw_stm_dt            ,--EDW系统日期 
T1.MBER_WRD				--1.会员关键字
,T1.BLNG_ORG_WRD         --2.所属机构关键字
,T1.MBER_NO              --3.会员编号
,T1.CUST_NO              --4.客户编号
,T1.ACC_BANK_CD          --5.开户银行代码
,T1.ACC_BANK_CD_DSC      --6.开户银行代码描述
,T1.SUBBR_NM             --7.支行名称
,T1.ACC_BANK_INT_NO      --8.开户行联行号
,T1.BANK_CARD_NO         --9.银行卡号
,T1.LOC_CD               --10.地域代码
,T1.LOC_CD_DSC           --11.地域代码描述
,T1.MBER_CGY_CD          --12.会员类别代码
,T1.MBER_CGY_CD_DSC      --13.会员类别代码描述
,T1.MBER_TP_CD             --14.会员类型代码
,T1.MBER_TP_CD_DSC       --15.会员类型代码描述
,T1.MBER_NM              --16.会员名称
,T1.ORIG_SUB_RL_ID       --17.原隶属角色ID
,T1.SUB_RL_ID            --18.隶属角色ID
,T1.REL_INSTID           --19.关联INSTID
,T1.MEBS_DT              --20.入会日期
,T1.TXPR_TP_CD           --21.纳税人类型代码
,T1.TXPR_TP_CD_DSC       --22.纳税人类型代码描述
,T1.VAT_RATE             --23.增值税率
,T1.TMVD                 --24.有效期
,T1.MBER_STAT_CD         --25.会员状态代码
,T1.MBER_STAT_CD_DSC     --26.会员状态代码描述
,T1.MEBS_LIST_CD         --27.入会清单代码
,T1.MEBS_LIST_CD_DSC     --28.入会清单代码描述
,T1.IS_KEY_MBER          --29.是否重点会员
,T1.CTACTS_NM            --30.联系人名称
,T1.CTC_TEL              --31.联系电话
,T1.CTC_PHONE_NO         --32.联系手机号
,T1.prof_srv_mem_type_dsc  -- 专业服务会员类别
from table1 T1
left join table3 T2
ON T1.edw_non_pk_md5 = T2.edw_non_pk_md5
WHERE T2.edw_non_pk_md5 IS NULL     

UNION ALL 
--修改

select
T1.bsn_src               ,--业务来源,来源系统编码    
T1.edw_dt_src_tbl        ,--EDW数据来源主表名     
T1.MBER_WRD  AS  edw_pk_unq_flg        ,--EDW主键唯一标识      
T1.edw_non_pk_md5        ,--EDW非主键MD5      
T1.edw_star_dt AS edw_star_dt           ,--EDW开始日期        
CASE WHEN T1.EDW_END_DT='********' AND T2.EDW_NON_PK_MD5 IS NOT NULL AND T1.EDW_NON_PK_MD5!=T2.EDW_NON_PK_MD5 THEN ${dmp_2_day} ELSE T1.EDW_END_DT END AS EDW_END_DT,--EDW结束日期
CASE WHEN T1.EDW_END_DT='********' AND T2.EDW_NON_PK_MD5 IS NOT NULL AND T1.EDW_NON_PK_MD5!=T2.EDW_NON_PK_MD5 THEN  '修改' ELSE T1.EDW_DLT_FLG END AS EDW_DLT_FLG,--IUD标志(增加、修改、删除)   
T1.edw_data_dt           ,--EDW数据日期        
T1.edw_stm_dt            ,--EDW系统日期           
T1.MBER_WRD				--1.会员关键字
,T1.BLNG_ORG_WRD         --2.所属机构关键字
,T1.MBER_NO              --3.会员编号
,T1.CUST_NO              --4.客户编号
,T1.ACC_BANK_CD          --5.开户银行代码
,T1.ACC_BANK_CD_DSC      --6.开户银行代码描述
,T1.SUBBR_NM             --7.支行名称
,T1.ACC_BANK_INT_NO      --8.开户行联行号
,T1.BANK_CARD_NO         --9.银行卡号
,T1.LOC_CD               --10.地域代码
,T1.LOC_CD_DSC           --11.地域代码描述
,T1.MBER_CGY_CD          --12.会员类别代码
,T1.MBER_CGY_CD_DSC      --13.会员类别代码描述
,T1.MBER_TP_CD             --14.会员类型代码
,T1.MBER_TP_CD_DSC       --15.会员类型代码描述
,T1.MBER_NM              --16.会员名称
,T1.ORIG_SUB_RL_ID       --17.原隶属角色ID
,T1.SUB_RL_ID            --18.隶属角色ID
,T1.REL_INSTID           --19.关联INSTID
,T1.MEBS_DT              --20.入会日期
,T1.TXPR_TP_CD           --21.纳税人类型代码
,T1.TXPR_TP_CD_DSC       --22.纳税人类型代码描述
,T1.VAT_RATE             --23.增值税率
,T1.TMVD                 --24.有效期
,T1.MBER_STAT_CD         --25.会员状态代码
,T1.MBER_STAT_CD_DSC     --26.会员状态代码描述
,T1.MEBS_LIST_CD         --27.入会清单代码
,T1.MEBS_LIST_CD_DSC     --28.入会清单代码描述
,T1.IS_KEY_MBER          --29.是否重点会员
,T1.CTACTS_NM            --30.联系人名称
,T1.CTC_TEL              --31.联系电话
,T1.CTC_PHONE_NO     --32.联系手机号
,T1.prof_srv_mem_type_dsc  -- 专业服务会员类别
from table2 T1
left join table1 T2
ON T1.MBER_WRD = T2.MBER_WRD 