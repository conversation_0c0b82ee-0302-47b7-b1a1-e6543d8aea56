SELECT  DISTINCT t1.bsn_prj_wrd
       ,t1.prj_id                                      AS project_no
       ,t1.prj_nm                                      AS project_name
       ,t3.prj_prt_nm                                  AS transferor_name
       ,T2.APRV_RCRD_ORG                               AS approval_institution
       ,T3.APRV_UNIT_NM                                AS approval_unit
       ,T3.BLNG_IDY_CD_DSC                             AS industry  -- 转让方所属行业
       ,T4.anul                                        AS audit_year
       ,T4.NET_PFT / 10000                             AS audit_net_profit
       ,T7.RPT_TP_CD_DSC                               AS report_type
       ,T7.RPT_DT                                      AS report_date
       ,T7.NET_PFT / 10000                             AS report_net_profit
       ,t5.hold_pty_eqty_pct                           AS held_equity
       ,T3.plan_tfr_pct                                AS proposed_equity
       ,T6.plan_tfr_pct                                AS total_equity
       ,t8.equity_book_val                             AS book_value
       ,t8.equity_ases_val                             AS assessed_value
       ,t8.sell_price                                  AS target_valuation
       ,CASE 
              WHEN COALESCE(t8.sell_object_evalu, 0) <= 0 
              THEN 0.0001  
              ELSE t8.sell_object_evalu
       END                                             AS ut_target_valuation
       ,t8.sell_price                                  AS transfer_price
       ,t8.claim_amt                                   AS debt_amount
       ,t9.deal_amt/10000                              AS transaction_amount
       ,t9.actl_txn_mth_cd_dsc                         AS transaction_method
       ,t9.bsn_rec_deal_dt                             AS transaction_date
       ,t9.trsfee_nm                                   AS assignee_name -- 受让方名称
       ,t1.prj_blng_dept_nm                            AS department
       ,t1.prj_prin_nm                                 AS project_manager
       ,t1.dept_prin_nm                                AS department_head
       ,t1.prj_src_cd_dsc                              AS project_source
       ,t1.prj_stat_cd_dsc                             AS project_status
       ,t8.obj_entp_nm                                 AS target_name
       ,t8.lead_object_corp_actl_ctrl_tfr              AS control_transfer
       ,t8.asset_sum_book_val                          AS assets_book_value
       ,t8.asset_sum_evalu_val                         AS assets_assessed_value
       ,t3.ECN_TP_CD_DSC                               AS transferor_economic_type
       ,t3.prov_cd_dsc                                 AS transferor_province
       ,t3.city_cd_dsc                                 AS transferor_city
       ,t3.regn_cnty_cd_dsc                            AS transferor_district
       ,coalesce(BL.state_owned_asset_regulator,t3.sown_spvs_org_cd_dsc)     AS transferor_regulatory -- 国资监管机构
       ,t3.cntry_sfep_or_mgr_dept_nm                   AS transferor_department -- 转让方国家出资企业
       ,coalesce(BL.regulator_location,t3.spvs_org_prov_cd_dsc)              AS transferor_area --监管机构属地(省)代码描述
       ,t1.info_publ_start_dt                          AS disclosure_start
       ,t1.info_publ_exprt_dt                          AS disclosure_end
       ,t1.txn_mth_cd_dsc                              AS transaction_mode
       ,t1.choc_txn_mth_cd_dsc                         AS preselect_mode
       ,''                                             AS pre_disclosure_start
       ,1                                              AS intended_assignees
       ,t9.cont_sign_dt                                AS contract_signing
       ,t9.cont_eff_dt                                 AS contract_effective
       ,t9.py_mth_cd_dsc                               AS payment_method
       ,t10.prov_cd_dsc                                AS asassignee_province
       ,t10.ecn_tp_cd_dsc                              AS assignee_economic_type
       ,t10.itrsfee_ctacts                             AS assignee_contact
       ,t10.itrsfee_phon                               AS assignee_phone
       ,t10.txn_svc_mber_nm                            AS assignee_broker
       ,t10.itrt_prt_tp_cd_dsc                         AS assignee_type
       ,'否'                                            AS is_original_shareholder
       ,round((coalesce(t9.deal_amt,0)/10000 - coalesce(t8.sell_object_evalu,0)- coalesce(t8.claim_amt,0)) / coalesce(t8.sell_object_evalu,0),4)*100 AS transaction_increase
       ,round( coalesce(t9.deal_amt,0)/10000 - coalesce(t8.sell_object_evalu,0)- coalesce(t8.claim_amt,0) ,4)                                        AS transaction_increase_amount
       ,t1.publ_anct_prod_wkdys                        AS disclosure_period
       ,round( (coalesce(t9.deal_amt,0)/10000 - coalesce(t8.sell_object_evalu,0)- coalesce(t8.claim_amt,0)) / coalesce(t8.sell_object_evalu,0),4) *100 AS premium_rate
       ,t11. toacct_dt                                 AS price_transfer_date
       ,t10.cert_no                                    AS assignee_id
       ,'' AS project_tags 
       ,t8.project_type_dsc                                  AS proj_type -- 项目类别 
       ,CASE WHEN t8.project_type_dsc = '非公开' 
                 THEN t9.deal_tp_cd_dsc                       
             ELSE t9.actl_txn_mth_cd_dsc 
        END AS deal_type -- 成交类型 
       ,CASE WHEN t8.project_type_dsc = '非公开' THEN 
            CASE WHEN TRIM(t3.cntry_sfep_or_mgr_dept_nm) = TRIM(t14.cty_contri_corp_lead_dept_nm) THEN '集团内调整' ELSE '跨集团调整' END
        ELSE '' END AS is_internal_adjust -- 集团内调整/跨集团调整 (转让方和受让方的隶属集团是否相同，相同的为集团内调整) 
       ,t12.income                                     AS income -- 北交所收(元) 
       ,coalesce(t9.deal_amt,0)/10000 - (coalesce(t8.sell_object_evalu,0) + coalesce(t8.claim_amt,0))       AS appreciation_amt -- 增值额（万元）成交金额 -（标的评估值+债权金额）
       ,t9.fnl_trnsfr_pct                               AS fnl_trnsfr_pct --最终受让比例
       ,t14.is_unite_buy                                AS is_unite_buy -- 是否联合受让方
       ,t8.industry_tp_dsc                              AS industry_tp_dsc --所属行业类型
       ,coalesce(BLDQ.xzqydm,T3.spvs_org_prov_cd)       AS transferor_area_code --监管机构属地(省)代码
       ,t8.industry_dsc                                 AS target_industry --标的企业所属行业
       ,coalesce(t8.sell_object_evalu,0)                AS ut_target_valuation_2 --转让标的对应评估值(万元)_2
       ,TRIM(t14.cty_contri_corp_lead_dept_nm)          AS trnsfr_department  -- 受让方国家出资企业
FROM dwd.dwd_prj_fct t1
LEFT JOIN
(
	SELECT  distinct BSN_PRJ_WRD
	       ,APRV_RCRD_ORG
	       ,PRJ_PRT_AST_WRD
	FROM dim.dim_prj_prt_ast_info --项目方资产维 
	WHERE DT = '${dmp_day}'
) T2
ON T1.BSN_PRJ_WRD = T2.BSN_PRJ_WRD
LEFT JOIN
(
	SELECT  BSN_PRJ_WRD
	       ,PRJ_PRT_NM
	       ,SOWN_SPVS_ORG_CD_DSC
	       ,PRJ_PRT_WRD
	       ,APRV_UNIT_NM
	       ,BLNG_IDY_TP_CD_DSC
	       ,BLNG_IDY_CD_DSC
	       ,PROV_CD_DSC
	       ,CITY_CD_DSC
	       ,REGN_CNTY_CD_DSC
	       ,RGST_ADDR
	       ,ECN_TP_CD_DSC
	       ,CNTRY_SFEP_OR_MGR_DEPT_NM
	       ,RGST_CPTL
	       ,REAL_INCM_CPTL
	       ,APRV_UNIT_FILE_TP_CD_DSC
	       ,plan_tfr_pct
              ,sfep_uscc_org_code -- 出资企业统一社会信用码/组织机构码
	       ,spvs_org_prov_cd_dsc --监管机构属地(省)代码描述
              ,spvs_org_prov_cd -- 监管机构属地(省)代码
	FROM dim.dim_trsfer_info --项目方信息维 
	WHERE DT = '${dmp_day}'
) T3 -- 转让方
ON T1.BSN_PRJ_WRD = T3.BSN_PRJ_WRD
LEFT JOIN
(
	SELECT  ROW_NUMBER() OVER(PARTITION BY BSN_PRJ_WRD ORDER BY  ANUL DESC) RN
	       ,BSN_PRJ_WRD
	       ,ANUL
	       ,NET_PFT
	       ,OPRT_REVN
	       ,OPRT_PFT
	       ,TOT_AST
	       ,TOT_LBY
	       ,OWN_EQTY
	FROM dim.dim_trgt_entp_audit_rpt_info --标的企业审计报告维 
	WHERE DT = '${dmp_day}'
	AND EDW_STAR_DT <= '${dmp_day}'
	AND '${dmp_day}' < EDW_END_DT 
) T4
ON T1.BSN_PRJ_WRD = T4.BSN_PRJ_WRD AND T4.RN = 1 --取年度最大的数据 
LEFT JOIN
(
	SELECT  *
	FROM dim.dim_prtrigt_trsfer_info
	WHERE dt = '${dmp_day}'
) t5
ON t3.prj_prt_wrd = t5.prj_prt_wrd
LEFT JOIN
(
	SELECT  BSN_PRJ_WRD
	       ,SUM(plan_tfr_pct) AS plan_tfr_pct
	FROM dim.dim_trsfer_info
	WHERE dt = '${dmp_day}'
	GROUP BY  BSN_PRJ_WRD
) t6
ON T1.BSN_PRJ_WRD = T6.BSN_PRJ_WRD
LEFT JOIN
(
	SELECT  ROW_NUMBER() OVER(PARTITION BY BSN_PRJ_WRD ORDER BY  RPT_DT DESC) RN
	       ,BSN_PRJ_WRD
	       ,SUBSTR(RPT_DT,1,10) AS RPT_DT
	       ,RPT_TP_CD_DSC
	       ,NET_PFT
	       ,OPRT_REVN
	       ,OPRT_PFT
	       ,TOT_AST
	       ,TOT_LBY
	       ,OWN_EQTY
	FROM dim.dim_fin_rpt_info --标的企业财务报表维 
	WHERE DT = '${dmp_day}'
	AND EDW_STAR_DT <= '${dmp_day}'
	AND '${dmp_day}' < EDW_END_DT 
) T7
ON T1.BSN_PRJ_WRD = T7.BSN_PRJ_WRD AND T7.RN = 1 --取报表日期最大的数据 
LEFT JOIN dwd.dwd_cqzr_prj_fct t8
ON t1.prj_id = t8.project_code AND t8.dt = '${dmp_day}'
LEFT JOIN dwd.dwd_evt_deal_rec_fct t9
ON t1.bsn_prj_wrd = t9.bsn_prj_wrd AND t9.dt = '${dmp_day}'
LEFT JOIN dwd.dwd_ittn_buyer_fct t10
ON t1.bsn_prj_wrd = t10.bsn_prj_wrd AND t9.bsn_buyer_id = t10.bsn_buyer_id AND t10.dt = '${dmp_day}'
LEFT JOIN dwd.dwd_bsn_order_fct t11
ON t1.bsn_prj_wrd = t11.bsn_prj_wrd AND t11.dt = '${dmp_day}' AND t11.order_tp_cd = '2' AND t11.order_stat_cd = '2'
LEFT JOIN
(
	SELECT  prj_id
	       ,bjssr AS income
	FROM dwd.dwd_prj_fee_fct
	WHERE dt = '${dmp_day}'
) t12
ON t1.prj_id = t12.prj_id -- 北交所收入(元) 
LEFT JOIN (
        select a1.project_id,a1.buyer_repre_nm,
       CASE WHEN a1.oasset_reg_org_dsc = '省级国资委监管' AND a1.custd_org_depdc_prov LIKE '110%' THEN b1.nm 
              ELSE a1.cty_contri_corp_lead_dept_nm 
       END  AS cty_contri_corp_lead_dept_nm,
       a1.is_unite_buy 
       from std.std_bjhl_tcqzr_yxsrfxx_d a1 
       left join std.std_bjhl_tbzzd_d b1
       on a1.fndd_entp = b1.cd 
       and a1.dt = b1.dt
       where a1.dt = '${dmp_day}'
       ) t14 -- 统一社会信用码
ON regexp_replace(TRIM(t9.trsfee_nm), '[\\(\\)（）]', '') = regexp_replace(TRIM(t14.buyer_repre_nm), '[\\(\\)（）]', '') AND 'BJHL'||t14.project_id||'CQZR' = t1.bsn_prj_wrd
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_eqty_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) BL -- 产权转让项目数据纠正表-补录 
ON t1.prj_id = BL.proj_no AND t9.trsfee_nm = BL.investor_name
LEFT JOIN (SELECT xzqydm  ,-- 代码 
	                  xzqymc  -- 码值
	             FROM ods.ods_bjhl_txzqydm  -- 行政区域代码表
	   WHERE dt='${dmp_day}' 
	         ) BLDQ
ON BL.regulator_location = BLDQ.xzqymc
WHERE t1.dt = '${dmp_day}'
AND t1.prj_bsn_tp_cd = 'GQ'
AND t1.bsn_prj_wrd is not null
