SELECT
  `proj_type` AS proj_type, -- 业务类型 
  `deal_num` AS deal_num, -- 成交数量 
  `deal_value` AS deal_value, -- 成交金额 
  `deal_date` AS deal_date, -- 成交日期 
  `proj_category` AS proj_category, -- 项目类别 
  `proj_status` AS proj_status, -- 项目状态 
  `cagetory` AS cagetory -- 类别 
FROM table1
UNION ALL
SELECT
  `proj_type` AS proj_type, -- 业务类型 
  `deal_num` AS deal_num, -- 成交数量 
  `deal_value` AS deal_value, -- 成交金额 
  `deal_date` AS deal_date, -- 成交日期 
  `proj_category` AS proj_category, -- 项目类别 
  `proj_status` AS proj_status, -- 项目状态 
  `cagetory` AS cagetory -- 类别 
FROM table2