SELECT 
    DISTINCT 
    A.project_no                            AS proj_no -- 项目编号 
   ,A.project_name                          AS proj_name -- 项目名称 
   ,A.transaction_date                      AS deal_date -- 成交日期 
   ,B.proj_category                         AS proj_category -- 项目类别（混改/压减/其他） 
   ,B.mix_own_type                          AS mix_own_type -- 混改类型 
   ,B.red_type                              AS red_type -- 压减类型 
   ,B.is_related                            AS is_related -- 是否涉及债券转让/土地使用权/技术资产 
   ,B.bond_amt                              AS bond_amt -- 涉及债券转让金额 
   ,B.land_use_amt                          AS land_use_amt -- 涉及土地使用权金额 
   ,B.tech_asset_amt                        AS tech_asset_amt -- 涉及技术资产金额
   ,A.project_manager                       AS project_manager -- 项目负责人
   ,A.assignee_name                         AS investor_name -- 投资人名称 
   ,B.investor_type                         AS investor_type -- 投资人类型 
   ,A.transferor_regulatory                 AS state_owned_asset_regulator -- 国资监管机构
   ,A.transferor_area                       AS regulator_location -- 监管机构属地
   ,''                                      AS udt_user -- 更新人
   ,current_timestamp()                     AS udt_tm -- 更新时间
FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_eqty_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) B -- 产权转让项目数据纠正表-补录 
ON A.project_no = B.proj_no AND A.assignee_name = B.investor_name AND A.dt = B.dt
LEFT ANTI JOIN (
    SELECT DISTINCT proj_no, investor_name FROM std.std_bl_eqty_prj_data_d WHERE dt = '${dmp_day}'
) C -- 产权转让项目数据纠正表-补录 
ON A.project_no = C.proj_no AND A.assignee_name = C.investor_name
WHERE A.dt = '${dmp_day}'
-- 数据范围：
-- 国资监管机构为“省级”或“市级”国资监管机构
-- 监管机构属地(省)为“北京市”的项目；项目所属部门包含“市属中心”、“行司中心”两个部门
-- AND A.transferor_regulatory in ('省级国资委监管','省级其他部门监管','省级财政部门监管','市级国资委监管','市级其他部门监管','市级财政部门或金融办监管')
-- AND A.transferor_area_code LIKE '110%'
AND A.department REGEXP '市属|行政司法'
AND A.transaction_date IS NOT NULL
UNION ALL
--联合体成员
SELECT 
    DISTINCT 
    A.project_no                            AS proj_no -- 项目编号 
   ,A.project_name                          AS proj_name -- 项目名称 
   ,A.transaction_date                      AS deal_date -- 成交日期 
   ,B.proj_category                         AS proj_category -- 项目类别（混改/压减/其他） 
   ,B.mix_own_type                          AS mix_own_type -- 混改类型 
   ,B.red_type                              AS red_type -- 压减类型 
   ,B.is_related                            AS is_related -- 是否涉及债券转让/土地使用权/技术资产 
   ,B.bond_amt                              AS bond_amt -- 涉及债券转让金额 
   ,B.land_use_amt                          AS land_use_amt -- 涉及土地使用权金额 
   ,B.tech_asset_amt                        AS tech_asset_amt -- 涉及技术资产金额
   ,A.project_manager                       AS project_manager -- 项目负责人
   ,C.mbr_nm                                AS investor_name -- 投资人名称 
   ,B.investor_type                         AS investor_type -- 投资人类型 
   ,A.transferor_regulatory                 AS state_owned_asset_regulator -- 国资监管机构
   ,A.transferor_area                       AS regulator_location -- 监管机构属地
   ,''                                      AS udt_user -- 更新人
   ,current_timestamp()                     AS udt_tm -- 更新时间
FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
LEFT JOIN
(
SELECT  'BJHL'||cj.prj||'CQZR' AS bsn_prj_wrd -- 业务项目关键字 （关联1）
       ,cj.buyer AS  buyer -- 受买方ID
       ,cj.buyer_nm AS buyer_nm -- 受让方名称(关联2)/联合体代表名称
       ,tyc.mbr_nm AS mbr_nm --联合体成员体名称
       ,ty.is_unite_buy AS is_unite_buy -- 是否联合受让方
       ,tyc.fnl_buy_pct AS fnl_buy_pct -- 最终受让比例
FROM std.std_bjhl_tcqzr_yxsrf_cy_d tyc
LEFT JOIN std.std_bjhl_tcqzr_yxsrfxx_d ty
ON tyc.buyer_id = ty.id AND tyc.dt = ty.dt
LEFT JOIN std.std_bjhl_tcqzr_cjjlxx_d cj
ON cj.buyer = ty.id AND cj.dt = ty.dt
WHERE cj.prj is not null
AND ty.is_unite_buy = 1
AND tyc.fnl_buy_pct IS NOT NULL
AND tyc.dt = '${dmp_day}' 
) C
ON A.bsn_prj_wrd = C.bsn_prj_wrd AND A.assignee_name = C.buyer_nm
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_eqty_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) B -- 产权转让项目数据纠正表-补录 
ON A.project_no = B.proj_no AND A.assignee_name = B.investor_name AND A.dt = B.dt
LEFT ANTI JOIN (
    SELECT DISTINCT proj_no, investor_name FROM std.std_bl_eqty_prj_data_d WHERE dt = '${dmp_day}'
) C -- 产权转让项目数据纠正表-补录 
ON A.project_no = C.proj_no AND A.assignee_name = C.investor_name
WHERE A.dt = '${dmp_day}'
-- AND A.transferor_regulatory in ('省级国资委监管','省级其他部门监管','省级财政部门监管','市级国资委监管','市级其他部门监管','市级财政部门或金融办监管')
-- AND A.transferor_area_code LIKE '110%'
AND A.department REGEXP '市属|行政司法'
AND A.transaction_date IS NOT NULL
AND A.is_unite_buy = 1
