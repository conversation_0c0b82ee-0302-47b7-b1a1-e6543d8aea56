SELECT 
c.cptl_lo AS cptl_lo, -- 资金位置
c.ordr_prj_no AS proj_no, --项目编码
c.ordr_prj_name AS proj_name, --项目名称
c.bsn_type AS proj_type, --业务类型
c.cptl_type_lrgcls AS fund_type, --资金类型
c.col_pay_agent_name, --代付方
regexp_replace(c.date_id, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS trns_date, --交易日期
c.sum_rj_arm - c.sum_cj_arm - c.rj_arm + c.cj_arm AS prj_amt, --项目余额
c.rj_arm AS deposit_amt, --入金金额
c.cj_arm AS withdrawal_amount, --出金金额
'' AS interest_rate, --利率
d.lit_amt AS lit_amt, --挂牌金
'' AS note, --备注 
d.lit_amt_mun AS lit_amt_mun --挂牌金额_数值
FROM (
SELECT 
a.date_id, --交易日期
a.ordr_prj_no, --项目编码
a.ordr_prj_name, --项目名称
a.cptl_lo, -- 资金位置
a.bsn_type, --业务类型
a.cptl_type_lrgcls, --资金类型
'' AS col_pay_agent_name, --代付方
CASE WHEN b.rj_arm IS NOT NULL THEN b.rj_arm ELSE 0 END rj_arm, --入金金额
CASE WHEN b.cj_arm IS NOT NULL THEN b.cj_arm ELSE 0 END cj_arm, --出金金额
SUM(CASE WHEN b.rj_arm IS NOT NULL THEN b.rj_arm ELSE 0 END) OVER (
    PARTITION BY a.ordr_prj_no, --项目编码
				 a.ordr_prj_name, --项目名称
				 a.bsn_type, --业务类型
				 a.cptl_type_lrgcls
    ORDER BY a.date_id) sum_rj_arm, --累计入金金额
SUM(CASE WHEN b.cj_arm IS NOT NULL THEN b.cj_arm ELSE 0 END) OVER (
    PARTITION BY a.ordr_prj_no, --项目编码
				 a.ordr_prj_name, --项目名称
				 a.bsn_type, --业务类型
				 a.cptl_type_lrgcls
    ORDER BY a.date_id)  sum_cj_arm --累计出金金额
FROM 
(
	-- 1. 形成维度表
  SELECT 
    DISTINCT 
    a1.date_id,
    b1.ordr_prj_no,
    b1.cptl_lo,  -- 资金位置
    b1.ordr_prj_name, -- 项目名称
    b1.bsn_type, -- 业务类型
    b1.cptl_type_lrgcls -- 资金类型
    --b1.min_pay_success_date, -- 最小支付成功日期
    --b1.max_pay_success_date -- 最大支付成功日期
	FROM 
	(SELECT * FROM dim.dim_date WHERE date_id > '20100101' AND date_id <= '${dmp_day}')
        a1 JOIN (
            SELECT  A.ordr_no
                    ,A.ordr_prj_no
					,A.cptl_lo
					,A.ordr_prj_name
					,A.bsn_type
					,A.cptl_type_lrgcls
                    ,C.min_pay_success_date
                    ,C.max_pay_success_date
				FROM (
				SELECT A.ordr_no
                    ,A.ordr_prj_no
					,A.cptl_lo
					,A.ordr_prj_name
					,A.bsn_type
					,A.cptl_type_lrgcls
                FROM dws.dws_trans_setl_info A
                WHERE A.dt = '${dmp_day}'
                AND A.cptl_type_lrgcls = '价款'
                AND A.ordr_prj_no IS NOT NULL
                AND A.ordr_no IS NOT NULL
                UNION ALL 
                SELECT  A.ordr_no
                    ,A.ordr_prj_no
                    ,A.cptl_lo
                    ,A.ordr_prj_name
                    ,A.bsn_type
                    ,A.cptl_type_lrgcls
                          FROM dws.dws_trans_setl_info A
						  WHERE A.dt = '${dmp_day}'
                          AND A.cptl_type_lrgcls = '保证金'
                          AND (CONCAT(ordr_prj_no,cust_name) IN 
                          (SELECT CONCAT(project_code,buyer_anm) isbuyer 
                           FROM dwd.dwd_evt_itrsfee_fct WHERE is_buyer = '是' AND dt = '${dmp_day}'
						   UNION ALL
							-- 联合受让方
							SELECT  
								CONCAT(a.project_code,b.mbr_nm) as isbuyer
							FROM dwd.dwd_evt_itrsfee_fct a
							LEFT JOIN (select buyer_id,mbr_nm from std.std_bjhl_tcqzr_yxsrf_cy_d  where dt = '${dmp_day}') b
							ON a.buyer_id = b.buyer_id
							AND a.is_joint_transferee = 1
							WHERE is_buyer = '是'
							AND b.mbr_nm IS NOT NULL
							AND dt = '${dmp_day}'
						   UNION ALL
						   -- 房屋出租和资产的需要单去
						    SELECT  concat(b.prj_id,c.cust_full_nm) isbuyer
							FROM std.std_bjhl_tbid_cjjl_d a
							LEFT JOIN dwd.dwd_prj_fct b
							ON a.project_id = b.plform_prj_id AND a.dt = b.dt
							LEFT JOIN
							(
								SELECT  DISTINCT bsn_stm_cust_no
									,cust_full_nm
								FROM dim.dim_pty_cust
								WHERE dt = '${dmp_day}' 
							) c
							ON a.cust_no = c.bsn_stm_cust_no
							WHERE a.dt = '${dmp_day}'
							AND a.entrst_cgy = '1'
							AND substr(b.prj_bsn_tp_cd_dsc, 0, 4) IN ('诉讼资产', '房屋出租')
						   
						   ) 
                          OR ast_type = '保证金转价款')
                          AND A.ordr_prj_no IS NOT NULL
                          AND A.ordr_no IS NOT NULL
				) A
					INNER JOIN 
					-- 限制只取支付成功日期大于2023年之后的相关项目
					(
						SELECT  ordr_prj_no
							FROM dws.dws_trans_setl_info
							WHERE  pay_success_date >= '********'
							AND dt = '${dmp_day}'
							-- AND ordr_prj_no = 'GR2022BJ1005978' --测试
							GROUP BY  ordr_prj_no
					) B
					ON A.ordr_prj_no = B.ordr_prj_no
					INNER JOIN 
					-- 相关项目最大和最小成交日期
					(
						SELECT  ordr_prj_no
								,MIN(bank_to_acc_date) AS min_pay_success_date
								,MAX(bank_to_acc_date) AS max_pay_success_date
							FROM dws.dws_trans_setl_info
							WHERE  dt = '${dmp_day}'
							AND cptl_type_lrgcls in ('保证金','价款')
							-- AND ordr_prj_no = 'GR2022BJ1005978' --测试
							GROUP BY  ordr_prj_no
					) C  
					ON A.ordr_prj_no = C.ordr_prj_no 				                  
	) b1
	ON 1 = 1
    -- a1.date_id >= b1.min_pay_success_date
    -- AND a1.date_id <= b1.max_pay_success_date
    WHERE a1.date_id >= b1.min_pay_success_date 
    AND a1.date_id <= b1.max_pay_success_date
) a 
LEFT JOIN 
(
	-- 2.关联业务数据
	SELECT 
  ordr_no, --订单编号
	ordr_prj_no, --项目编码
	ordr_prj_name, --项目名称
	cptl_lo,-- 资金位置
	bsn_type, --业务类型
	cptl_type_lrgcls, --资金类型
	bank_to_acc_date, --成交日期(银行到账日期********)
	-- col_pay_agent_name, --代付方
	sum(CASE WHEN crj_flag = '入金' THEN amt ELSE 0 END) rj_arm, --入金金额
	sum(CASE WHEN crj_flag = '出金' THEN amt ELSE 0 END) cj_arm --出金金额
	FROM dws.dws_trans_setl_info 
	WHERE dt = '${dmp_day}' AND cptl_type_lrgcls ='价款' 
	GROUP BY ordr_no,ordr_prj_no,cptl_lo,ordr_prj_name,bsn_type,cptl_type_lrgcls,bank_to_acc_date,ast_type
	UNION ALL 
	SELECT 
    ordr_no, --订单编号
	ordr_prj_no, --项目编码
	ordr_prj_name, --项目名称
	cptl_lo,-- 资金位置
	bsn_type, --业务类型
	cptl_type_lrgcls, --资金类型
	bank_to_acc_date, --成交日期(银行到账日期********)
	-- col_pay_agent_name, --代付方
	sum(CASE WHEN crj_flag = '入金' THEN amt ELSE 0 END) rj_arm, --入金金额
	sum(CASE WHEN crj_flag = '出金' THEN amt ELSE 0 END) cj_arm --出金金额
	FROM dws.dws_trans_setl_info 
	WHERE dt = '${dmp_day}' AND cptl_type_lrgcls ='保证金' 
	AND (CONCAT(ordr_prj_no,cust_name) IN 
	(SELECT CONCAT(project_code,buyer_anm) isbuyer FROM dwd.dwd_evt_itrsfee_fct WHERE is_buyer = '是' AND dt = '${dmp_day}'
	UNION ALL
	-- 联合受让方
	SELECT  
		CONCAT(a.project_code,b.mbr_nm) as isbuyer
	FROM dwd.dwd_evt_itrsfee_fct a
	LEFT JOIN (select buyer_id,mbr_nm from std.std_bjhl_tcqzr_yxsrf_cy_d  where dt = '${dmp_day}') b
	ON a.buyer_id = b.buyer_id
	AND a.is_joint_transferee = 1
	WHERE is_buyer = '是'
	AND b.mbr_nm IS NOT NULL
	AND dt = '${dmp_day}'
	UNION ALL
	-- 房屋出租和资产的需要单取
	SELECT  concat(b.prj_id,c.cust_full_nm) isbuyer
	FROM std.std_bjhl_tbid_cjjl_d a
	LEFT JOIN dwd.dwd_prj_fct b
	ON a.project_id = b.plform_prj_id AND a.dt = b.dt
	LEFT JOIN
	(
		SELECT  DISTINCT bsn_stm_cust_no
			,cust_full_nm
		FROM dim.dim_pty_cust
		WHERE dt = '${dmp_day}' 
	) c
	ON a.cust_no = c.bsn_stm_cust_no
	WHERE a.dt = '${dmp_day}'
	AND a.entrst_cgy = '1'
	AND substr(b.prj_bsn_tp_cd_dsc, 0, 4) IN ('诉讼资产', '房屋出租')
	) 
	OR ast_type = '保证金转价款')
	GROUP BY ordr_no,ordr_prj_no,cptl_lo,ordr_prj_name,bsn_type,cptl_type_lrgcls,bank_to_acc_date,ast_type
) b 
ON a.ordr_prj_no = b.ordr_prj_no
AND a.cptl_lo = b.cptl_lo 
AND a.bsn_type = b.bsn_type
AND a.cptl_type_lrgcls = b.cptl_type_lrgcls
AND a.date_id = b.bank_to_acc_date 
) c
LEFT JOIN (
	SELECT  DISTINCT A.prj_id AS prj_id
       ,CASE WHEN a.prj_bsn_tp_cd = '1C' THEN 
	   		CASE
             WHEN d.intnd_new_cptl_cptl_mod = 2 THEN intnd_rs_cptl_tot_amt_w_yuan || '万元'
             WHEN d.intnd_new_cptl_cptl_mod = 1 AND intnd_rs_cptl_tot_amt_min_w_yuan = 0 AND intnd_rs_cptl_tot_amt_max_w_yuan < ********* THEN '不高于' || intnd_rs_cptl_tot_amt_max_w_yuan || '万元'
             WHEN d.intnd_new_cptl_cptl_mod = 1 AND intnd_rs_cptl_tot_amt_min_w_yuan > 0 AND intnd_rs_cptl_tot_amt_max_w_yuan = ********* THEN '不低于' || intnd_rs_cptl_tot_amt_min_w_yuan || '万元'
             WHEN d.intnd_new_cptl_cptl_mod = 1 AND intnd_rs_cptl_tot_amt_min_w_yuan > 0 AND intnd_rs_cptl_tot_amt_max_w_yuan < ********* THEN intnd_rs_cptl_tot_amt_min_w_yuan || '万元-' || intnd_rs_cptl_tot_amt_max_w_yuan || '万元'
             WHEN d.intnd_new_cptl_cptl_mod = 1 AND intnd_rs_cptl_tot_amt_min_w_yuan = 0 AND intnd_rs_cptl_tot_amt_max_w_yuan = ********* THEN '择优而定' 
			END 
			ELSE lit_amt END AS lit_amt
       ,prj_bsn_tp_cd
	   ,CASE WHEN a.prj_bsn_tp_cd = '1C' THEN intnd_rs_cptl_tot_amt_w_yuan ELSE lit_amt END AS lit_amt_mun
FROM dwd.dwd_prj_fct a
LEFT JOIN dwd.dwd_entp_incptl_prj_fct d
ON a.bsn_prj_wrd = d.bsn_prj_wrd AND d.dt = a.dt
WHERE a.dt = '${dmp_day}'
AND a.prj_id IS NOT NULL
) d
ON c.ordr_prj_no = d.prj_id
WHERE prj_bsn_tp_cd != 'MN'