select * from (
select date_desc as data_dt from dim.dim_date where year_desc>='2017' and date_id<='${dmp_day}')
    cross join
(select compy_name as agent_name,
agent_no as agent_no
from (
SELECT compy_name,agent_no,
ROW_NUMBER() OVER (PARTITION BY cust_no ORDER BY update_time DESC) AS row_num
FROM ods.ods_bl_agent_info
where dt='${dmp_day}'
) ranked_data
where row_num=1)
   cross join 
   (select '资产转让' as proj_type 
    union all
    select '产权转让' as proj_type
    union all
    select '企业增资' as proj_type
   )