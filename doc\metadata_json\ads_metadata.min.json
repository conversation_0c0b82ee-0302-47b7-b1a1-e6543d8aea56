{"metadata_version": "1.0", "last_updated": "2025-02-24", "database": "ads", "tables": {"ads_agent_stateasset_prj_count": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"bj_exchange_income": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入"}, "business_type": {"type": "String", "comment": "业务类型（产权转让、大宗实物、企业增资）"}, "data_dt": {"type": "String", "comment": "统计日期"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_prj_num": {"type": "Nullable(Decimal(10, 0))", "comment": "成交数量"}, "ht_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额"}, "lit_prj_num": {"type": "Nullable(Decimal(10, 0))", "comment": "挂牌数量"}, "proj_type_agent": {"type": "String", "comment": "项目类型（代理转让方、代理意向方、无会员代理）"}}}, "ads_agent_stateasset_prj_cqzr": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "buyer_agent": {"type": "String", "comment": "意向受让方会员名称"}, "buyer_agent_name": {"type": "String", "comment": "（意向）受让方经纪会员名称"}, "buyer_name": {"type": "String", "comment": "（意向）受让方名称"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额(万元)"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "is_acceptance": {"type": "String", "comment": "是否受让"}, "j_exchange_income": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "proj_type_agent": {"type": "String", "comment": "项目类型（代理转让方、代理意向方、无会员代理）"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "tfr_prc": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价(万元)"}, "trans_type": {"type": "String", "comment": "交易方式"}}}, "ads_agent_stateasset_prj_dzsw": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "asset_type": {"type": "String", "comment": "资产类别"}, "buyer_agent": {"type": "String", "comment": "意向受让方会员名称"}, "buyer_agent_name": {"type": "String", "comment": "（意向）受让方经纪会员名称"}, "buyer_name": {"type": "String", "comment": "（意向）受让方名称"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额(万元)"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "is_acceptance": {"type": "String", "comment": "是否受让"}, "j_exchange_income": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "proj_type_agent": {"type": "String", "comment": "项目类型（代理转让方、代理意向方、无会员代理）"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价(万元)"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_type": {"type": "String", "comment": "交易方式"}}}, "ads_agent_stateasset_prj_qyzz": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"buyer_agent": {"type": "String", "comment": "意向受让方会员名称"}, "buyer_agent_name": {"type": "String", "comment": "（意向）受让方经纪会员名称"}, "buyer_name": {"type": "String", "comment": "（意向）受让方名称"}, "deal_date": {"type": "String", "comment": "成交日期"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "投资总额（万元）"}, "is_acceptance": {"type": "String", "comment": "是否受让"}, "j_exchange_income": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "proj_type_agent": {"type": "String", "comment": "项目类型（代理转让方、代理意向方、无会员代理）"}, "selection_method": {"type": "String", "comment": "择优方式"}, "seller_agent_name": {"type": "String", "comment": "融资方经纪会员名称"}, "seller_amt": {"type": "Nullable(String)", "comment": "融资金额"}, "seller_fincer_name": {"type": "String", "comment": "融资方名称"}, "tfr_prc": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价(万元)"}}}, "ads_bidder_intention_info": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"contact_person_name": {"type": "String", "comment": "联系人"}, "cust_name": {"type": "String", "comment": "客户名称"}, "disclosure_end_time": {"type": "String", "comment": "披露结束日期"}, "disclosure_start_time": {"type": "String", "comment": "披露起始日期"}, "disclosure_type": {"type": "String", "comment": "披露类型"}, "email": {"type": "String", "comment": "电子邮件"}, "increase_in_registered_capital": {"type": "Nullable(<PERSON><PERSON><PERSON>(26, 6))", "comment": "拟新增注册资本（万元）"}, "intention_desc": {"type": "String", "comment": "意向描述"}, "investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(26, 6))", "comment": "拟投资金额（万元）"}, "investment_pct": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "拟投资比例（%）"}, "phone": {"type": "String", "comment": "联系电话"}, "prj_dep": {"type": "String", "comment": "项目所属部门"}, "prj_name": {"type": "String", "comment": "项目名称"}, "prj_no": {"type": "String", "comment": "项目编号"}, "prj_responsible_person": {"type": "String", "comment": "项目负责人"}, "prj_type": {"type": "String", "comment": "项目类型"}}}, "ads_bjstateownership_prj_info": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"avg_daily_login_count": {"type": "Nullable(Decimal(10, 0))", "comment": "交易主体日均登录平台量"}, "avg_daily_tenders": {"type": "Nullable(Decimal(10, 0))", "comment": "平台日均开评标数量"}, "avg_experts": {"type": "Nullable(Decimal(10, 0))", "comment": "平台日均抽取专家"}, "data_dt": {"type": "String", "comment": "统计日期"}, "largest_awardee": {"type": "Nullable(String)", "comment": "中标金额最大主体"}, "largest_transaction_project": {"type": "Nullable(String)", "comment": "交易金额最大项目"}, "market_concentration": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "市场集中度"}, "market_openness": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "市场开放度"}, "market_type": {"type": "String", "comment": "市场类别"}, "max_bidders_per_project": {"type": "Nullable(String)", "comment": "单个项目最大投标数量（家）"}}}, "ads_bsn_pcs_aprv_info": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"aprv_pcs_id": {"type": "String", "comment": "审批流程ID"}, "audit_pass_tm": {"type": "String", "comment": "审核通过时间"}, "audit_rmrk": {"type": "String", "comment": "审核备注"}, "audit_tm": {"type": "String", "comment": "审核时间"}, "auditor_blng_cetr_nm": {"type": "String", "comment": "审核人所属中心名称"}, "auditor_blng_dept_nm": {"type": "String", "comment": "审核人所属部门名称"}, "auditor_nm": {"type": "String", "comment": "审核人名称"}, "etl_date": {"type": "String", "comment": "ETL 载入日期"}, "exec_act": {"type": "String", "comment": "执行动作"}, "itt_tm": {"type": "String", "comment": "发起时间"}, "node_audit_durt": {"type": "String", "comment": "节点审核时长"}, "node_ster": {"type": "String", "comment": "节点提交人名称"}, "pcs_aprv_tot_durt": {"type": "String", "comment": "流程审批总时长"}, "pcs_ariv_tm": {"type": "String", "comment": "流程到达时间"}, "pcs_nm": {"type": "String", "comment": "流程名称"}, "pcs_node_nm": {"type": "String", "comment": "流程节点名称"}, "pcs_stat_cd": {"type": "String", "comment": "流程状态"}, "pmer_blng_cetr_nm": {"type": "String", "comment": "发起人所属中心名称"}, "pmer_blng_dept_nm": {"type": "String", "comment": "发起人所属部门名称"}, "pmer_nm": {"type": "String", "comment": "发起人名称"}, "prj_blng_cetr": {"type": "String", "comment": "项目所属中心"}, "prj_blng_dept": {"type": "String", "comment": "项目所属部门"}, "prj_bsn_tp": {"type": "String", "comment": "项目业务类型"}, "prj_id": {"type": "String", "comment": "项目编号"}, "prj_nm": {"type": "String", "comment": "项目名称"}, "prj_prin": {"type": "String", "comment": "项目负责人"}, "prj_prin_dept": {"type": "String", "comment": "项目负责人部门"}, "prj_stg": {"type": "String", "comment": "项目阶段"}, "prj_tp": {"type": "String", "comment": "项目类型"}, "publ_tp": {"type": "String", "comment": "披露类型"}, "sort_no": {"type": "String", "comment": "排序号"}, "stm_tp": {"type": "String", "comment": "系统类型"}}}, "ads_culturetourism_deal": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"buyer_name": {"type": "String", "comment": "受让方/投资方名称"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期"}, "disclosure_type": {"type": "String", "comment": "披露类型"}, "industry_type": {"type": "String", "comment": "所属行业类型"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露截止日期"}, "proj_location": {"type": "String", "comment": "项目所属地"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_status": {"type": "String", "comment": "项目状态"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(18, 4))", "comment": "转让底价"}, "sell_price_compute_mth": {"type": "String", "comment": "转让底价计算方式"}, "sell_price_max": {"type": "Nullable(<PERSON><PERSON><PERSON>(18, 4))", "comment": "转让底价最大值"}, "sell_price_min": {"type": "Nullable(<PERSON><PERSON><PERSON>(18, 4))", "comment": "转让底价最小值"}, "source": {"type": "String", "comment": "来源系统"}}}, "ads_culturetourism_listing": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"disclosure_type": {"type": "String", "comment": "披露类型"}, "industry_type": {"type": "String", "comment": "所属行业类型"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露截止日期"}, "proj_location": {"type": "String", "comment": "项目所属地"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_status": {"type": "String", "comment": "项目状态"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(18, 4))", "comment": "转让底价"}, "sell_price_compute_mth": {"type": "String", "comment": "转让底价计算方式"}, "sell_price_max": {"type": "Nullable(<PERSON><PERSON><PERSON>(18, 4))", "comment": "转让底价最大值"}, "sell_price_min": {"type": "Nullable(<PERSON><PERSON><PERSON>(18, 4))", "comment": "转让底价最小值"}, "source": {"type": "String", "comment": "来源系统"}}}, "ads_invest_bhvr_smy": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"acm_brw_num": {"type": "Nullable(Decimal(16, 0))", "comment": "浏览项目数量"}, "fav_num": {"type": "Nullable(Decimal(16, 0))", "comment": "收藏项目数量"}, "invest_nm": {"type": "String", "comment": "投资人名称"}, "invst_src_stm": {"type": "String", "comment": "投资人来源系统"}, "udt_tm": {"type": "String", "comment": "更新时间"}, "usr_id": {"type": "String", "comment": "用户id"}}}, "ads_invest_brw_bhvr_smy": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"anc_tm": {"type": "Nullable(String)", "comment": "发布时间"}, "brw_num": {"type": "Nullable(Decimal(16, 0))", "comment": "浏览次数"}, "esr_tp": {"type": "Nullable(String)", "comment": "披露类型"}, "invest_position": {"type": "Nullable(String)", "comment": "投资人职位"}, "invst_src_stm": {"type": "Nullable(String)", "comment": "投资人来源系统"}, "is_cle_out": {"type": "String", "comment": "是否注销"}, "prj_cgy": {"type": "Nullable(String)", "comment": "项目类别"}, "prj_code": {"type": "Nullable(String)", "comment": "项目编号"}, "prj_nm": {"type": "Nullable(String)", "comment": "项目名称"}, "prj_prin_nm": {"type": "Nullable(String)", "comment": "项目负责人"}, "prj_smlcls": {"type": "Nullable(String)", "comment": "项目小类"}, "prj_sts": {"type": "Nullable(String)", "comment": "项目状态"}, "usr_id": {"type": "String", "comment": "用户id"}}}, "ads_invest_fav_bhvr_dtl": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"anc_tm": {"type": "Nullable(String)", "comment": "发布时间"}, "esr_tp": {"type": "Nullable(String)", "comment": "披露方式"}, "fav_tm": {"type": "Nullable(String)", "comment": "收藏时间"}, "invest_position": {"type": "Nullable(String)", "comment": "投资人职位"}, "invst_src_stm": {"type": "Nullable(String)", "comment": "投资人来源系统"}, "prj_cgy": {"type": "Nullable(String)", "comment": "项目类别"}, "prj_code": {"type": "Nullable(String)", "comment": "项目编号"}, "prj_nm": {"type": "Nullable(String)", "comment": "项目名称"}, "prj_prin_nm": {"type": "Nullable(String)", "comment": "项目负责人"}, "prj_smlcls": {"type": "Nullable(String)", "comment": "项目小类"}, "prj_sts": {"type": "Nullable(String)", "comment": "项目状态"}, "usr_id": {"type": "String", "comment": "用户id"}}}, "ads_invest_lbl_wgt": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"create_time": {"type": "Nullable(String)", "comment": "创建时间"}, "invest_id": {"type": "String", "comment": "投资人id"}, "invest_lbl_id": {"type": "Nullable(String)", "comment": "投资人标签id"}, "invst_src_stm": {"type": "Nullable(String)", "comment": "投资人来源系统"}, "lbl_ecd": {"type": "Nullable(String)", "comment": "标签code"}, "lbl_nm": {"type": "Nullable(String)", "comment": "标签名称"}, "lbl_wgt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "标签权重"}, "pref_wgt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "偏好标签权重"}, "smy_wgt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "汇总权重"}, "udt_tm": {"type": "Nullable(String)", "comment": "更新时间"}}}, "ads_investor_info": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"cle_out_tm": {"type": "String", "comment": "注销时间"}, "create_time": {"type": "String", "comment": "创建时间"}, "cust_nm": {"type": "String", "comment": "客户姓名"}, "email": {"type": "String", "comment": "邮箱"}, "is_acpt_prj_rcmm": {"type": "String", "comment": "是否愿意接受每周两次最新项目推荐（周一和周四)"}, "is_cle_out": {"type": "String", "comment": "是否注销"}, "is_fcs_oa": {"type": "String", "comment": "是否关注公众号"}, "is_frz": {"type": "String", "comment": "是否冻结"}, "modify_time": {"type": "String", "comment": "修改时间"}, "org_nm": {"type": "String", "comment": "机构名称"}, "phone": {"type": "String", "comment": "手机号"}, "position": {"type": "String", "comment": "职位"}, "pref_lbl": {"type": "String", "comment": "偏好标签"}, "rmrk": {"type": "String", "comment": "备注"}, "src_stm": {"type": "String", "comment": "来源系统"}, "usr_cgy_cd_dsc": {"type": "String", "comment": "用户类别"}, "usr_id": {"type": "String", "comment": "用户id"}}}, "ads_js_cs": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"PROJ_NO": {"type": "String", "comment": "项目编号"}, "a1": {"type": "Decimal(24, 2)", "comment": "入金发生金额（元）"}, "b1": {"type": "Decimal(24, 2)", "comment": "出金发生金额（元）"}, "c1": {"type": "Decimal(24, 2)", "comment": "成交金额"}}}, "ads_js_fund_onsite_setl_rate_det": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期"}, "entry_price_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "进场价款金额"}, "onsite_setl_rate": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "场内结算率"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_type": {"type": "String", "comment": "业务类型"}}}, "ads_js_fund_onsite_setl_rate_summary": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_prj_num": {"type": "Nullable(Decimal(10, 0))", "comment": "成交项目数量"}, "entry_price_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "进场价款金额"}, "onsite_setl_rate": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "场内结算率"}, "proj_type": {"type": "String", "comment": "业务类型"}}}, "ads_js_pro_foreign_det": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"currency": {"type": "String", "comment": "币种"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期"}, "is_transferee": {"type": "String", "comment": "是否受让方"}, "lit_end_dt": {"type": "String", "comment": "挂牌截止日期"}, "lit_star_dt": {"type": "String", "comment": "挂牌开始日期"}, "margin_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "保证金金额（万元）"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "项目所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "proj_type": {"type": "String", "comment": "业务类型"}, "pymt_mode_cd_dsc": {"type": "String", "comment": "结算方式"}, "seller_fincer_name": {"type": "String", "comment": "转让方/融资方名称"}, "seller_fincer_region": {"type": "String", "comment": "转让方/融资方所在地区（省）"}, "tfr_prc": {"type": "String", "comment": "转让底价（万元）"}, "transferee_name": {"type": "String", "comment": "受让方或意向受让方名称"}, "transferee_region": {"type": "String", "comment": "受让方或意向受让方所在地区（省）"}, "trgt_nm": {"type": "String", "comment": "标的名称"}, "udyast_lo_prov_cd_dsc": {"type": "String", "comment": "标的名称所在地区"}}}, "ads_js_pro_fund_setl_det": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"cptl_lo": {"type": "String", "comment": "资金位置"}, "deposit_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "入金金额"}, "deposit_date": {"type": "String", "comment": "入金日期"}, "fund_type": {"type": "String", "comment": "资金类型"}, "intrest_rate": {"type": "Nullable(String)", "comment": "利率"}, "lit_amt": {"type": "Nullable(String)", "comment": "挂牌金额"}, "lit_amt_mun": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额_数值"}, "note": {"type": "String", "comment": "备注"}, "prj_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "项目余额"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_type": {"type": "String", "comment": "业务类型"}, "trans_date": {"type": "String", "comment": "交易日期"}, "withdrawal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "出金金额"}, "withdrawal_date": {"type": "String", "comment": "出金日期"}}}, "ads_js_proj_belong_name_dim": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"c_code": {"type": "String", "comment": "编码"}, "c_name": {"type": "String", "comment": "名称"}, "type_name": {"type": "String", "comment": "类型"}}}, "ads_js_trans_fund_setl_det": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"bank_toacct_dt": {"type": "String", "comment": "银行到账日期"}, "business_center": {"type": "String", "comment": "业务中心"}, "col_pay_agent_name": {"type": "String", "comment": "代收方/代付方名称"}, "cptl_lo": {"type": "String", "comment": "资金位置"}, "deposit_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "入金发生金额（元）"}, "deposit_date": {"type": "String", "comment": "入金日期"}, "ext_ordr_no": {"type": "String", "comment": "机构订单号"}, "fund_type": {"type": "String", "comment": "资金类型"}, "is_bank": {"type": "String", "comment": "出入金银行不一致"}, "order_creat_dt": {"type": "String", "comment": "订单创建日期"}, "order_no": {"type": "String", "comment": "订单编号"}, "payee_payer_name": {"type": "String", "comment": "收款方/付款方名称"}, "proj_belong_dept_name": {"type": "String", "comment": "业务部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "业务负责人"}, "proj_type": {"type": "String", "comment": "业务类型"}, "py_aplc_no": {"type": "String", "comment": "支付申请号"}, "trans_date": {"type": "String", "comment": "交易日期"}, "withdrawal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "出金发生金额（元）"}, "withdrawal_date": {"type": "String", "comment": "出金日期"}}}, "ads_js_trans_fund_setl_summary": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"closing_balance": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "期末余额（万元）"}, "cptl_lo": {"type": "String", "comment": "资金位置"}, "deposit_amount": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "入金金额（万元）"}, "opening_balance": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "期初余额（万元）"}, "proj_type": {"type": "String", "comment": "业务类型"}, "trans_date": {"type": "String", "comment": "交易日期"}, "withdrawal_amount": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "出金金额（万元）"}}}, "ads_memberagent_cqzr": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "buyer_name": {"type": "String", "comment": "受让方名称"}, "buyer_name_y": {"type": "String", "comment": "（意向）受让方名称"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额(万元)"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价(万元)"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_type": {"type": "String", "comment": "交易方式"}, "transferee_name": {"type": "String", "comment": "受让方经纪会员名称"}}}, "ads_memberagent_dzsw": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "asset_type": {"type": "String", "comment": "资产类别"}, "buyer_name": {"type": "String", "comment": "受让方名称"}, "buyer_name_y": {"type": "String", "comment": "（意向）受让方名称"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额(万元)"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价(万元)"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_type": {"type": "String", "comment": "交易方式"}, "transferee_name": {"type": "String", "comment": "受让方经纪会员名称"}}}, "ads_memberagent_fwcz": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"asset_type": {"type": "String", "comment": "资产类别"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交总价（元）"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "info_dclo_begin_dt": {"type": "String", "comment": "披露开始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "披露结束日期"}, "lessee_agent": {"type": "String", "comment": "承租方受托交易服务会员"}, "lessee_agent_final": {"type": "String", "comment": "最终承租方受托交易服务会员"}, "lessee_name": {"type": "String", "comment": "承租方名称"}, "lessor_agent": {"type": "String", "comment": "出租方受托交易服务会员"}, "lessor_name": {"type": "String", "comment": "出租方名称"}, "listing_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌总价（元）"}, "listing_price": {"type": "Nullable(String)", "comment": "挂牌价格（元）"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "prj_type": {"type": "String", "comment": "项目类型"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "selection_method": {"type": "String", "comment": "遴选方式"}}}, "ads_memberagent_jdc": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "任务接收方单位"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交价格（元）"}, "deal_date": {"type": "String", "comment": "成交日期（解体过户确认日期）"}, "deal_je": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额(元)"}, "deal_type": {"type": "String", "comment": "成交状态"}, "disposal_method": {"type": "String", "comment": "处置方式"}, "free_bid_end_time": {"type": "String", "comment": "自由报价结束时间"}, "free_bid_start_time": {"type": "String", "comment": "自由报价开始时间"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门（任务来源）"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人（业务来源经办）"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（元）"}, "starting_bid": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "起拍价（元）"}, "trans_method": {"type": "String", "comment": "交易方式名称"}, "trans_type": {"type": "String", "comment": "交易状态名称"}}}, "ads_memberagent_listing_count": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_name": {"type": "String", "comment": "公司名称"}, "agent_no": {"type": "String", "comment": "会员编号"}, "bulk_investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "大宗实物投融资"}, "bulk_lit_prj_num": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "大宗实物挂牌数量"}, "data_dt": {"type": "String", "comment": "统计日期"}, "equity_raise_investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "企业增资投融资"}, "equity_raise_lit_prj_num": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "企业增资挂牌数量"}, "property_investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "产权转让投融资"}, "property_lit_prj_num": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "产权转让挂牌数量"}}}, "ads_memberagent_listing_cqzr": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态描述"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_type": {"type": "String", "comment": "交易方式"}}}, "ads_memberagent_listing_dzsw": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "asset_type": {"type": "String", "comment": "资产类别"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态描述"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_type": {"type": "String", "comment": "交易方式"}}}, "ads_memberagent_listing_pct": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "公司名称"}, "agnet_no": {"type": "String", "comment": "会员编号"}, "business_type": {"type": "String", "comment": "业务类型"}, "exch": {"type": "String", "comment": "交易所"}, "info_dclo_begin_dt": {"type": "String", "comment": "披露开始日期"}, "lim_prj_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌项目金额"}, "lim_prj_num": {"type": "Nullable(Decimal(10, 0))", "comment": "挂牌项目数量"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}}}, "ads_memberagent_listing_pct_details": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "代理会员名称"}, "blng_grp": {"type": "String", "comment": "所属集团"}, "exch": {"type": "String", "comment": "挂牌交易所"}, "ht_amt": {"type": "Nullable(String)", "comment": "挂牌金额"}, "info_dclo_begin_dt": {"type": "String", "comment": "披露开始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "披露结束日期"}, "is_repeat": {"type": "String", "comment": "是否重复挂牌"}, "oasset_custd_org": {"type": "String", "comment": "监管类型"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "客户所属项目经理"}, "proj_type": {"type": "String", "comment": "项目类型（产权转让、实物、企业增资）"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}}}, "ads_memberagent_listing_qyzz": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态描述"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "selection_method": {"type": "String", "comment": "择优方式"}, "seller_agent": {"type": "String", "comment": "融资方经纪会员名称"}, "seller_fincer_name": {"type": "String", "comment": "融资方名称"}}}, "ads_memberagent_qyzz": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "融资方经纪会员名称"}, "deal_date": {"type": "String", "comment": "成交日期"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "投资总额（万元）"}, "preferential_selection_method": {"type": "String", "comment": "择优方式"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "seller_fincer_name": {"type": "String", "comment": "融资方名称"}}}, "ads_memberagent_stateasset_bidintent_count": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "公司名称"}, "agent_no": {"type": "String", "comment": "会员编号"}, "bid_participants_count": {"type": "Nullable(Decimal(10, 0))", "comment": "参与竞价意向方数量"}, "data_dt": {"type": "String", "comment": "统计日期"}, "investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "投融资"}, "proj_type": {"type": "String", "comment": "项目类型（产权转让、实物、企业增资）"}}}, "ads_memberagent_stateasset_bidintent_cqzr": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "buyer_name": {"type": "String", "comment": "（意向）受让方名称"}, "cbex_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额（万元）"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收费总额（元）"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "is_transferee": {"type": "String", "comment": "是否受让方经纪会员"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态描述"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让方服务费总金额（元）"}, "trans_type": {"type": "String", "comment": "交易方式"}, "transferee_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "受让方服务费总金额（元）"}, "transferee_name": {"type": "String", "comment": "（意向）受让方经纪会员名称"}}}, "ads_memberagent_stateasset_bidintent_dzsw": {"comment": "", "update_time": "2025-02-24 09:39:08", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "buyer_agent_name": {"type": "String", "comment": "（意向）受让方经纪会员名称"}, "buyer_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "受让方服务费总金额（元）"}, "buyer_name": {"type": "String", "comment": "（意向）受让方名称"}, "cbex_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额（万元）"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收费总额（元）"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "is_buyer": {"type": "String", "comment": "是否受让方经纪会员"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态描述"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}, "seller_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让方服务费总金额（元）"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_type": {"type": "String", "comment": "交易方式"}}}, "ads_memberagent_transferstats": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "公司名称"}, "agent_no": {"type": "String", "comment": "会员编号"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value_bulk": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "大宗实物成交金额"}, "deal_value_equity_raise": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "企业增资成交金额"}, "deal_value_property": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "产权转让成交金额"}, "deal_value_rental": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "房屋出租成交金额"}, "deal_value_small_bulk": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "小宗实物成交金额"}, "deal_value_vehicle": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "机动车成交金额"}, "financing": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "投融资"}}}, "ads_memberagent_xzsw": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "任务接收方单位"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交价格（元）"}, "deal_date": {"type": "String", "comment": "交割日期"}, "deal_type": {"type": "String", "comment": "成交状态"}, "disposal_method": {"type": "String", "comment": "处置方式"}, "free_bid_end_time": {"type": "String", "comment": "自由报价结束时间"}, "free_bid_start_time": {"type": "String", "comment": "自由报价开始时间"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（元）"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "starting_bid": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "起拍价（元）"}}}, "ads_municipal_transactiondata": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"added_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增值金额"}, "category": {"type": "String", "comment": "类别"}, "deal_date": {"type": "String", "comment": "成交日期"}, "dtbj_trans_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "动态报价交易额"}, "dtbj_trans_num": {"type": "Nullable(Decimal(24, 0))", "comment": "动态报价交易数"}, "pm_trans_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "拍卖交易额"}, "pm_trans_num": {"type": "Nullable(Decimal(24, 0))", "comment": "拍卖交易数"}, "qt_trans_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "其他交易额"}, "qt_trans_num": {"type": "Nullable(Decimal(24, 0))", "comment": "其他交易数"}, "wljj_trans_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "网络竞价交易额"}, "wljj_trans_num": {"type": "Nullable(Decimal(24, 0))", "comment": "网络竞价交易数"}, "xyzr_trans_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "协议转让交易额"}, "xyzr_trans_num": {"type": "Nullable(Decimal(24, 0))", "comment": "协议转让交易数"}, "ztb_trans_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "招投标交易额"}, "ztb_trans_num": {"type": "Nullable(Decimal(24, 0))", "comment": "招投标交易数"}}}, "ads_municipal_transactiondata_detail": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"asset_origin": {"type": "String", "comment": "资产来源/国资监管机构"}, "business_type": {"type": "String", "comment": "业务类型"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期"}, "proj_belong_center": {"type": "String", "comment": "所属中心"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "total_price_incre": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增值金额"}, "trans_type": {"type": "String", "comment": "交易方式"}}}, "ads_prj_bhvr_smy": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"brw_num": {"type": "Nullable(Decimal(16, 0))", "comment": "浏览量"}, "fav_num": {"type": "Nullable(Decimal(16, 0))", "comment": "收藏量"}, "prj_id": {"type": "String", "comment": "项目id"}, "prj_src_stm": {"type": "Nullable(String)", "comment": "项目来源系统"}, "udt_tm": {"type": "Nullable(String)", "comment": "更新时间"}}}, "ads_prj_info_cqzr": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"approval_institution": {"type": "Nullable(String)", "comment": "核准(备案)机构"}, "approval_unit": {"type": "Nullable(String)", "comment": "批准单位名称"}, "assessed_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产评估值(万元)"}, "assets_assessed_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计/评估价值(万元)"}, "assets_book_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计/账面价值(万元)"}, "assignee_broker": {"type": "Nullable(String)", "comment": "受让方经纪会员名称"}, "assignee_contact": {"type": "Nullable(String)", "comment": "受让方联系人"}, "assignee_economic_type": {"type": "Nullable(String)", "comment": "受让方经济类型"}, "assignee_id": {"type": "Nullable(String)", "comment": "受让方证件号码"}, "assignee_name": {"type": "Nullable(String)", "comment": "受让方名称"}, "assignee_phone": {"type": "Nullable(String)", "comment": "受让方手机"}, "assignee_province": {"type": "Nullable(String)", "comment": "受让方所在地（省）"}, "assignee_type": {"type": "Nullable(String)", "comment": "受让方类型"}, "audit_net_profit": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "审计报告_净利润（万元）"}, "audit_year": {"type": "Nullable(String)", "comment": "审计报告_年度"}, "book_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产账面价值(万元)"}, "contract_effective": {"type": "Nullable(String)", "comment": "合同生效日期"}, "contract_signing": {"type": "Nullable(String)", "comment": "合同签订日期"}, "control_transfer": {"type": "Nullable(String)", "comment": "是否导致标的企业实际控制权转移"}, "debt_amount": {"type": "Nullable(String)", "comment": "债权金额（万元）"}, "department": {"type": "Nullable(String)", "comment": "所属部门"}, "department_head": {"type": "Nullable(String)", "comment": "部门负责人"}, "disclosure_end": {"type": "Nullable(String)", "comment": "信息披露期满日期"}, "disclosure_period": {"type": "Nullable(String)", "comment": "披露公告期（工作日）"}, "disclosure_start": {"type": "Nullable(String)", "comment": "信息披露起始日期"}, "held_equity": {"type": "Nullable(<PERSON><PERSON><PERSON>(12, 6))", "comment": "持有产股权比例（%）"}, "industry": {"type": "Nullable(String)", "comment": "标的企业所属行业"}, "intended_assignees": {"type": "Nullable(String)", "comment": "意向受让方数量"}, "is_original_shareholder": {"type": "Nullable(String)", "comment": "是否原股东"}, "payment_method": {"type": "Nullable(String)", "comment": "交易价款支付方式"}, "pre_disclosure_start": {"type": "Nullable(String)", "comment": "预披露开始日期"}, "premium_rate": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "溢价率（%）"}, "preselect_mode": {"type": "Nullable(String)", "comment": "预选交易方式"}, "price_transfer_date": {"type": "Nullable(String)", "comment": "价款划出日期"}, "project_manager": {"type": "Nullable(String)", "comment": "项目负责人"}, "project_name": {"type": "Nullable(String)", "comment": "项目名称"}, "project_no": {"type": "String", "comment": "项目编号"}, "project_source": {"type": "Nullable(String)", "comment": "项目来源"}, "project_status": {"type": "Nullable(String)", "comment": "项目状态"}, "project_tags": {"type": "Nullable(String)", "comment": "项目标签"}, "proposed_equity": {"type": "Nullable(<PERSON><PERSON><PERSON>(12, 6))", "comment": "拟转让产股权比例（%）"}, "report_date": {"type": "Nullable(String)", "comment": "最近一期财务报表_报表日期"}, "report_net_profit": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "最近一期财务报表_净利润（万元）"}, "report_type": {"type": "Nullable(String)", "comment": "最近一期财务报表_报表类型"}, "target_name": {"type": "Nullable(String)", "comment": "标的企业名称"}, "target_valuation": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让标的评估值(万元)"}, "total_equity": {"type": "Nullable(<PERSON><PERSON><PERSON>(12, 6))", "comment": "转让产股权比例之和（%）"}, "transaction_amount": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额(万元)"}, "transaction_date": {"type": "Nullable(String)", "comment": "成交日期"}, "transaction_increase": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "交易增减值(%)"}, "transaction_increase_amount": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "交易增值额(万元)"}, "transaction_method": {"type": "Nullable(String)", "comment": "成交方式"}, "transaction_mode": {"type": "Nullable(String)", "comment": "交易方式"}, "transfer_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价(万元)"}, "transferor_area": {"type": "Nullable(String)", "comment": "转让方监管地区"}, "transferor_broker": {"type": "Nullable(String)", "comment": "转让方经纪会员名称"}, "transferor_city": {"type": "Nullable(String)", "comment": "转让方所在地（市）"}, "transferor_department": {"type": "Nullable(String)", "comment": "转让方国家出资企业或主管部门"}, "transferor_district": {"type": "Nullable(String)", "comment": "转让方所在地（区/县）"}, "transferor_economic_type": {"type": "Nullable(String)", "comment": "转让方经济类型"}, "transferor_name": {"type": "Nullable(String)", "comment": "转让方名称"}, "transferor_province": {"type": "Nullable(String)", "comment": "转让方所在地（省）"}, "transferor_regulatory": {"type": "Nullable(String)", "comment": "转让方国资监管机构"}}}, "ads_prj_info_qyzz": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"add_fndd_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "增加出资额(万元)"}, "apprv_rec_org": {"type": "String", "comment": "核准（备案）机构"}, "asset_sum_evalu_val": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "资产总计/评估价值（万元）"}, "audt_rpt_anul": {"type": "String", "comment": "审计报告_年度"}, "audt_rpt_asset_sum": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "审计报告_资产总计（万元）"}, "audt_rpt_bsn_incm": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "审计报告_营业收入（万元）"}, "audt_rpt_bsn_pft": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "审计报告_营业利润（万元）"}, "audt_rpt_liab_sum": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "审计报告_负债总计（万元）"}, "audt_rpt_netprofit": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "审计报告_净利润（万元）"}, "audt_rpt_owner_equity": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "审计报告_所有者权益（万元）"}, "authorize_file_type": {"type": "String", "comment": "批准文件类型"}, "authorize_unit_nm": {"type": "String", "comment": "批准单位名称"}, "business_scope": {"type": "String", "comment": "经营范围"}, "buyer_name": {"type": "String", "comment": "投资方名称"}, "capital_plan": {"type": "String", "comment": "增资方案主要内容"}, "data_dt": {"type": "String", "comment": "数据日期"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deposit_or_pct": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "保证金金额或比例（%）"}, "deposit_pcsg_mod": {"type": "String", "comment": "保证金处理方式"}, "dept_pnp": {"type": "String", "comment": "部门负责人"}, "emp_is_pcp_icap": {"type": "String", "comment": "职工是否参与增资"}, "equity_book_val": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "净资产账面价值（万元）"}, "etl_load_dt": {"type": "String", "comment": "etl载入日期"}, "evaluate_equity": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "净资产评估值（万元）"}, "final_ratio": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "增资后出资比例（%）"}, "fincer_brkr_mbsh_nm": {"type": "String", "comment": "融资方经纪会员名称"}, "fincer_cty_contri_corp_lead_dept": {"type": "String", "comment": "融资方国家出资企业或主管部门"}, "fincer_ds_mk_file_tp": {"type": "String", "comment": "融资方决策文件类型"}, "fincer_economy_type": {"type": "String", "comment": "融资方经济类型"}, "fincer_idy_tp": {"type": "String", "comment": "融资方行业类型"}, "fincer_industry": {"type": "String", "comment": "融资方所属行业"}, "fincer_nm": {"type": "String", "comment": "融资方名称"}, "fincer_oasset_reg_org": {"type": "String", "comment": "融资方国资监管机构"}, "fincer_site_city": {"type": "String", "comment": "融资方所在地（市）"}, "fincer_site_prov": {"type": "String", "comment": "融资方所在地（省）"}, "fincer_site_zon_cnty": {"type": "String", "comment": "融资方所在地（区/县）"}, "fincer_zone": {"type": "String", "comment": "融资方所在地区"}, "holder_percent": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "增资前出资比例（%）"}, "icap_af_entp_stock": {"type": "String", "comment": "增资后企业股权结构"}, "icap_cd": {"type": "String", "comment": "增资条件"}, "icap_mod": {"type": "String", "comment": "增资方式"}, "icap_rch_or_suspsn_cd": {"type": "String", "comment": "增资达成或中止条件"}, "info_anc_exp_ar": {"type": "String", "comment": "信息发布期满安排"}, "info_esr_beg_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_esr_exp_dt": {"type": "String", "comment": "信息披露期满日期"}, "intnd_new_investor_num": {"type": "Nullable(Decimal(10, 0))", "comment": "拟新增投资方数量"}, "intnd_rs_cptl_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "拟募集资金金额（万元）"}, "intnd_rs_cptl_crpnd_hold_shr_pct": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "拟募集资金对应持股比例（%）"}, "intnt_investor_num": {"type": "String", "comment": "意向投资方数量"}, "intnt_investor_num_payed": {"type": "String", "comment": "意向投资方数量（已交保）"}, "investor_accept_result": {"type": "String", "comment": "投资方机构审核结果"}, "investor_brkr_mbsh_nm": {"type": "String", "comment": "投资方经纪会员名称"}, "investor_business_scope": {"type": "String", "comment": "投资方经营范围"}, "investor_economy_type": {"type": "String", "comment": "投资方经济类型"}, "investor_ivs_pct": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "投资方投资所占比例（%）"}, "investor_postulate": {"type": "String", "comment": "投资方资格条件"}, "investor_reg_capital": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "投资方注册资本（万元）"}, "investor_site_city": {"type": "String", "comment": "投资方所在地（市）"}, "investor_site_prov": {"type": "String", "comment": "投资方所在地（省）"}, "investor_site_zon_cnty": {"type": "String", "comment": "投资方所在地（区/县）"}, "investor_sts": {"type": "String", "comment": "投资方状态"}, "investor_tp": {"type": "String", "comment": "投资方类型"}, "is_cntr_ent": {"type": "String", "comment": "是否央企"}, "is_deposit": {"type": "String", "comment": "是否交纳保证金"}, "is_fnc_cptl_investor": {"type": "String", "comment": "是否为金融资本投资方"}, "ivs_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "投资金额（万元）"}, "ivs_way": {"type": "String", "comment": "投资方式"}, "mdl_mod": {"type": "String", "comment": "成交方式"}, "mdl_tot_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "成交总金额(万元)"}, "orig_shrh_is_pcp_icap": {"type": "String", "comment": "原股东是否参与增资"}, "othr_esr_itm": {"type": "String", "comment": "其他披露事项"}, "paicl_capital": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "实收资本（万元）"}, "pay_mode": {"type": "String", "comment": "支付方式"}, "pay_tm": {"type": "String", "comment": "交纳时间"}, "prefer_mod": {"type": "String", "comment": "择优方式"}, "prefer_scm": {"type": "String", "comment": "择优方案"}, "prep_invest_total": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "拟投资资金总额(万元)"}, "prep_new_reg_capital": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "拟新增注册资本（万元）"}, "prj_blng_dept": {"type": "String", "comment": "项目所属部门"}, "prj_lbl": {"type": "String", "comment": "项目标签"}, "prj_pnp": {"type": "String", "comment": "项目负责人"}, "prj_sts": {"type": "String", "comment": "项目状态"}, "project_code": {"type": "String", "comment": "项目编号"}, "project_name": {"type": "String", "comment": "项目名称"}, "rctly_1_prd_fin_rpt_asset_sum": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "最近一期财务报表_资产总计（万元）"}, "rctly_1_prd_fin_rpt_bsn_incm": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "最近一期财务报表_营业收入（万元）"}, "rctly_1_prd_fin_rpt_bsn_pft": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "最近一期财务报表_营业利润（万元）"}, "rctly_1_prd_fin_rpt_liab_sum": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "最近一期财务报表_负债总计（万元）"}, "rctly_1_prd_fin_rpt_owner_equity": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "最近一期财务报表_所有者权益（万元）"}, "rctly_fin_rpt_netprofit": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "最近一期财务报表_净利润（万元）"}, "rctly_fin_rpt_stmt_date": {"type": "String", "comment": "最近一期财务报表_报表日期"}, "rctly_fin_rpt_stmt_type": {"type": "String", "comment": "最近一期财务报表_报表类型"}, "reg_capital": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "注册资本（万元）"}, "rslt_pbc_end_tm": {"type": "String", "comment": "结果公示结束时间"}, "rslt_pbc_strt_tm": {"type": "String", "comment": "结果公示开始时间"}, "shrh_nm": {"type": "String", "comment": "股东名称"}, "shrh_num": {"type": "Nullable(Decimal(10, 0))", "comment": "股东个数"}, "shrh_tp": {"type": "String", "comment": "股东类型"}, "sign_dt": {"type": "String", "comment": "签约日期"}, "tot_rlse_eqty_pct": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "释放股权比例之和(%)"}, "unit_reg_capital_crpnd_evalu": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "单位注册资本对应评估值(元)"}, "use_of_raised_funds": {"type": "String", "comment": "募集资金用途"}, "workers_num": {"type": "Nullable(Decimal(10, 0))", "comment": "职工人数"}}}, "ads_prj_invest_brw_bhvr_smy": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"bhvr_tm": {"type": "Nullable(String)", "comment": "行为时间"}, "brw_cnt": {"type": "Nullable(Decimal(16, 0))", "comment": "浏览次数"}, "brw_dtl": {"type": "Nullable(String)", "comment": "浏览明细（访问方式、访问来源、浏览时间）"}, "email": {"type": "Nullable(String)", "comment": "邮箱"}, "invest_id": {"type": "Nullable(String)", "comment": "投资人id"}, "invest_nm": {"type": "Nullable(String)", "comment": "投资人名称"}, "invest_phone": {"type": "Nullable(String)", "comment": "投资人手机号"}, "invest_position": {"type": "Nullable(String)", "comment": "投资人职位"}, "invest_rmrk": {"type": "Nullable(String)", "comment": "投资人备注"}, "invest_tp": {"type": "Nullable(String)", "comment": "投资人类型"}, "is_cle_out": {"type": "String", "comment": "是否注销"}, "org_nm": {"type": "Nullable(String)", "comment": "机构名称"}, "pref_lbl": {"type": "Nullable(String)", "comment": "偏好标签"}, "prj_id": {"type": "String", "comment": "项目编号"}}}, "ads_prj_invest_fav_bhvr_dtl": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"bhvr_tm": {"type": "String", "comment": "行为时间"}, "email": {"type": "String", "comment": "邮箱"}, "invest_id": {"type": "String", "comment": "投资人id"}, "invest_nm": {"type": "String", "comment": "投资人名称"}, "invest_phone": {"type": "String", "comment": "投资人手机号"}, "invest_position": {"type": "String", "comment": "投资人职位"}, "invest_rmrk": {"type": "String", "comment": "投资人备注"}, "invest_tp": {"type": "String", "comment": "投资人类型"}, "is_cle_out": {"type": "String", "comment": "是否注销"}, "org_nm": {"type": "String", "comment": "机构名称"}, "pref_lbl": {"type": "String", "comment": "偏好标签"}, "prj_id": {"type": "String", "comment": "项目编号"}, "udt_tm": {"type": "String", "comment": "更新时间"}}}, "ads_prj_listg_dtl": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"Prj_Bsn_Tp_Cd": {"type": "Nullable(String)", "comment": "项目业务类型代码"}, "Prj_Bsn_Tp_Cd_Dsc": {"type": "Nullable(String)", "comment": "项目业务类型代码描述"}, "Prj_Id": {"type": "Nullable(String)", "comment": "项目编号"}, "Prj_Nm": {"type": "Nullable(String)", "comment": "项目名称"}, "Prj_SubCls_Cd": {"type": "Nullable(String)", "comment": "项目子类代码"}, "Prj_SubCls_Cd_Dsc": {"type": "Nullable(String)", "comment": "项目子类代码描述"}, "anc_tm": {"type": "Nullable(String)", "comment": "项目发布时间"}, "ast_cgy": {"type": "Nullable(String)", "comment": "资产类别"}, "ast_cl": {"type": "Nullable(String)", "comment": "资产分类"}, "ast_cl_hs_bld": {"type": "Nullable(String)", "comment": "资产分类（房屋建筑物）"}, "bjht_data_id": {"type": "Nullable(String)", "comment": "北交汇投数据id"}, "bld_area_hs": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "建筑面积（房屋）"}, "bld_area_hs_bld": {"type": "Nullable(String)", "comment": "建筑面积（房屋建筑物）"}, "blng_cntr": {"type": "Nullable(String)", "comment": "所属中心"}, "blng_dept": {"type": "Nullable(String)", "comment": "所属部门"}, "book_val_clm": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "账面价值（债权）"}, "bscrc_hs": {"type": "Nullable(String)", "comment": "商圈（房屋）"}, "clm_ast_crn_sttn_dsc": {"type": "Nullable(String)", "comment": "债权资产现状描述"}, "clm_book_nm": {"type": "Nullable(String)", "comment": "债权账面名称"}, "clm_book_val": {"type": "Nullable(String)", "comment": "债权账面价值"}, "clm_cgy": {"type": "Nullable(String)", "comment": "债权类别 0 单户 1债权资产包"}, "court_trust": {"type": "Nullable(String)", "comment": "委托法院"}, "curr_use": {"type": "Nullable(String)", "comment": "用途"}, "data_src": {"type": "String", "comment": "数据来源"}, "dcrt_sttn": {"type": "Nullable(String)", "comment": "装修"}, "eqp_lift": {"type": "Nullable(String)", "comment": "是否有电梯"}, "esr_end_dt": {"type": "Nullable(String)", "comment": "披露结束日期"}, "esr_strt_dt": {"type": "Nullable(String)", "comment": "披露开始日期"}, "fix_val": {"type": "Nullable(String)", "comment": "固定值"}, "fuel_ctlg": {"type": "Nullable(String)", "comment": "燃油种类"}, "hs_stl": {"type": "Nullable(String)", "comment": "户型"}, "hs_use_hs": {"type": "Nullable(String)", "comment": "房屋用途（房屋）"}, "inf_esr_id": {"type": "Nullable(String)", "comment": ""}, "intnd_coll_invest_max_num": {"type": "Nullable(String)", "comment": "拟征集投资人最大数量"}, "intnd_coll_invest_min_num": {"type": "Nullable(String)", "comment": "拟征集投资人最小数量"}, "intnd_coll_invest_num_fix_val": {"type": "Nullable(String)", "comment": "拟征集投资人数量固定值"}, "intnd_coll_invest_num_tp": {"type": "Nullable(String)", "comment": "拟征集投资人数量0区间 1 面议 2固定"}, "intnd_fnc_amt_fix_val": {"type": "Nullable(String)", "comment": "拟融资金额固定值"}, "intnd_fnc_amt_tp": {"type": "Nullable(String)", "comment": "拟融资金额 0区间 1 面议 2固定"}, "intnd_rs_cptl_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "拟募集资金金额"}, "intnd_rs_cptl_crpnd_hold_shr_pct": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 8))", "comment": "拟募集资金对应持股比例"}, "is_bjdc_oprt": {"type": "Nullable(String)", "comment": "是否地产运营"}, "is_bjht_oprt": {"type": "Nullable(String)", "comment": "是否汇投运营"}, "is_lose_act_control": {"type": "Nullable(String)", "comment": "是否失去实际控制权"}, "is_oprt_hs_src_inf": {"type": "Nullable(String)", "comment": "是否运营房源信息"}, "is_pblc": {"type": "Nullable(String)", "comment": "是否公开：0.不公开 1.公开"}, "is_quota": {"type": "Nullable(String)", "comment": "是否限购"}, "land_area_land": {"type": "Nullable(String)", "comment": "土地面积（土地）"}, "nal_non_nal": {"type": "Nullable(String)", "comment": "国有非国有"}, "obj_bld": {"type": "Nullable(String)", "comment": "标的建筑面积"}, "obj_blng_city": {"type": "Nullable(String)", "comment": "标的所属市"}, "obj_blng_prov": {"type": "Nullable(String)", "comment": "标的所属省"}, "obj_crn_sttn_dsc": {"type": "Nullable(String)", "comment": "标的现状描述"}, "obj_entp_business_scope": {"type": "Nullable(String)", "comment": "标的企业经营范围/融资方主营业务介绍"}, "obj_entp_main_oprt_bsn_intd": {"type": "Nullable(String)", "comment": "标的企业主营业务介绍"}, "obj_entp_nm": {"type": "Nullable(String)", "comment": "标的企业名称"}, "obj_entp_wbt_idy": {"type": "Nullable(String)", "comment": "标的企业所在行业/融资方所在行业"}, "obj_land": {"type": "Nullable(String)", "comment": "标的占地面积"}, "obj_lo": {"type": "Nullable(String)", "comment": "标的位置"}, "obj_nm": {"type": "Nullable(String)", "comment": "标的名称"}, "obj_pty_tp": {"type": "Nullable(String)", "comment": "标的物业类型"}, "obj_sell_amt_max_amt": {"type": "Nullable(String)", "comment": "标的转让金额最大金额"}, "obj_sell_amt_min_amt": {"type": "Nullable(String)", "comment": "标的转让金额最小金额"}, "obj_unit_prc": {"type": "Nullable(String)", "comment": "标的单价"}, "obj_use_yrlmt": {"type": "Nullable(String)", "comment": "标的使用年限"}, "object_cl": {"type": "Nullable(String)", "comment": "标的物分类"}, "obligor_blng_idy": {"type": "Nullable(String)", "comment": "债务人所属行业"}, "obligor_ecn_char": {"type": "Nullable(String)", "comment": "债务人经济性质"}, "obligor_nm": {"type": "Nullable(String)", "comment": "债务人名称（或自然人姓名）"}, "obligor_oprt_scop": {"type": "Nullable(String)", "comment": "债务人经营范围"}, "obligor_rgst_addr": {"type": "Nullable(String)", "comment": "债务人注册地址"}, "orig_shrh_is_pcp_icap": {"type": "Nullable(String)", "comment": "原股东是否参与增资"}, "othr_req_esr_of_cntnt": {"type": "Nullable(String)", "comment": "其他需要披露的内容"}, "pkl": {"type": "Nullable(String)", "comment": "停车场"}, "pl_lbl": {"type": "Nullable(String)", "comment": "房源标签"}, "plform_prj_id": {"type": "String", "comment": "平台项目id"}, "plg_obj": {"type": "Nullable(String)", "comment": "抵质押物类型  商业房产、住宅房产、土地、在建工程、船舶等交通工具、机械设备、股权、知识产权、无形资产、矿权、林权、其他（可多选）"}, "pnp": {"type": "Nullable(String)", "comment": "本金"}, "posit_hs_bld": {"type": "Nullable(String)", "comment": "坐落位置（房屋建筑物）"}, "posit_land": {"type": "Nullable(String)", "comment": "坐落位置（土地）"}, "pre_publ_prj_id": {"type": "Nullable(String)", "comment": "预披露项目id"}, "prj_brgh_dt": {"type": "Nullable(String)", "comment": "项目亮点"}, "prj_bsc_inf": {"type": "Nullable(String)", "comment": "项目基本信息"}, "prj_bsn_tp_mrk": {"type": "Nullable(String)", "comment": "项目业务类型（营销）"}, "prj_int": {"type": "Nullable(String)", "comment": "利息"}, "prj_lbl": {"type": "Nullable(String)", "comment": "项目标签"}, "prj_prin_nm": {"type": "Nullable(String)", "comment": "项目负责人名称"}, "prj_prin_phone": {"type": "Nullable(String)", "comment": "项目负责人手机号"}, "prj_site": {"type": "Nullable(String)", "comment": "所在地"}, "prj_smlcls_mrk": {"type": "Nullable(String)", "comment": "项目小类（营销）"}, "prj_sts": {"type": "Nullable(String)", "comment": "项目状态"}, "project_category": {"type": "Nullable(String)", "comment": "项目类别"}, "project_type": {"type": "Nullable(String)", "comment": "项目类型"}, "reference_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "参考价格"}, "reference_price_max": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "参考价格最大"}, "reference_price_min": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "参考价格最小"}, "reference_price_tp": {"type": "Nullable(String)", "comment": "参考价格类型"}, "reg_capital": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本"}, "rent_prc": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "出租价格"}, "rnt_fee_mod": {"type": "Nullable(String)", "comment": "租金方式"}, "sell_amt_min_pct_tp": {"type": "Nullable(String)", "comment": "转让金额最小值类型"}, "sell_amt_pct_fix_val": {"type": "Nullable(String)", "comment": "转让金额固定值"}, "sell_eqty_max_pct": {"type": "Nullable(String)", "comment": "转让股权最大比例（百分比）/拟释放股权最大比例"}, "sell_eqty_min_pct": {"type": "Nullable(String)", "comment": "转让股权最小比例（百分比）/拟释放股权最小比例"}, "sell_eqty_min_pct_tp": {"type": "Nullable(String)", "comment": "转让股权最小比例（百分比）/拟释放股权最小比例 是否面议 0区间 1 面议 2固定"}, "sell_max_prc": {"type": "Nullable(String)", "comment": "转让最大价格/拟融资最大金额"}, "sell_min_prc": {"type": "Nullable(String)", "comment": "转让最小价格/拟融资最小金额"}, "sell_prc_max_pct": {"type": "Nullable(String)", "comment": "转让价格最大值"}, "sell_prc_min_pct": {"type": "Nullable(String)", "comment": "转让价格最小值"}, "sell_prc_min_pct_tp": {"type": "Nullable(String)", "comment": "转让价格最小值类型"}, "sell_prc_pct_fix_val": {"type": "Nullable(String)", "comment": "转让价格固定值"}, "sell_side_inr_ds_mk_sttn": {"type": "Nullable(String)", "comment": "转让方内部决策情况"}, "seller_fincer_attr_inves_sbj": {"type": "Nullable(String)", "comment": "转让方/融资方（招商主体）"}, "seller_nm": {"type": "Nullable(String)", "comment": "转让方名称"}, "seller_tp": {"type": "Nullable(String)", "comment": "转让方类型 0银行 1 AMC 2 其他金融机构 3 其他"}, "src_stm": {"type": "Nullable(String)", "comment": "来源系统"}, "tbl_show_mile": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "表显里程"}, "tp": {"type": "Nullable(String)", "comment": "类型"}, "tp_land": {"type": "Nullable(String)", "comment": "类型（土地）"}, "trade_category": {"type": "Nullable(String)", "comment": "交易品类"}, "transfer_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价"}, "twrd": {"type": "Nullable(String)", "comment": "朝向"}, "txn_smlcls": {"type": "Nullable(String)", "comment": "交易小类"}, "udt_tm": {"type": "Nullable(String)", "comment": "更新时间"}, "use_land": {"type": "Nullable(String)", "comment": "用途（土地）"}, "vhcl_brnd": {"type": "Nullable(String)", "comment": "车辆品牌"}, "vhcl_tp": {"type": "Nullable(String)", "comment": "车辆类型"}, "wbt_floor": {"type": "Nullable(String)", "comment": "楼层"}, "wrnt_cgy": {"type": "Nullable(String)", "comment": "担保类别  0信用类、1抵押/质押、2保证、3其他"}}}, "ads_prj_psh_bhvr_dtl": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"bhvr_sts": {"type": "String", "comment": "行为状态（未查看/已查看/已收藏/不感兴趣）"}, "bjht_prj_data_id": {"type": "String", "comment": "汇投项目数据id"}, "blng_psh": {"type": "String", "comment": "所属推送"}, "invest_id": {"type": "String", "comment": "投资人id"}, "invest_position": {"type": "String", "comment": "投资人职位"}, "is_fcs_oa": {"type": "String", "comment": "是否关注公众号"}, "is_rgst": {"type": "String", "comment": "是否注册"}, "online_lined": {"type": "String", "comment": "线上/线下"}, "org_nm": {"type": "String", "comment": "机构名称"}, "plform_prj_id": {"type": "String", "comment": "平台项目id"}, "prj_src_stm": {"type": "String", "comment": "来源系统"}}}, "ads_prj_psh_bhvr_smy": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"bjht_prj_data_id": {"type": "String", "comment": "汇投项目数据id"}, "blng_psh": {"type": "String", "comment": "所属推送"}, "plform_prj_id": {"type": "String", "comment": "平台项目id"}, "prj_code": {"type": "String", "comment": "项目编号"}, "prj_nm": {"type": "String", "comment": "项目名称"}, "prj_src_stm": {"type": "String", "comment": "项目来源系统"}, "psh_alrdy_check_num": {"type": "Nullable(Decimal(16, 0))", "comment": "推送已查看数量"}, "psh_alrdy_fav_num": {"type": "Nullable(Decimal(16, 0))", "comment": "推送已收藏数量"}, "psh_alrdy_outbnd_num": {"type": "Nullable(Decimal(16, 0))", "comment": "推送不感兴趣数量"}, "psh_not_check_num": {"type": "Nullable(Decimal(16, 0))", "comment": "推送未查看数量"}}}, "ads_project_ext_ordr_no": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"ext_ordr_no": {"type": "String", "comment": "外部订单编号"}, "ordr_no": {"type": "String", "comment": "订单编号"}, "prj_id": {"type": "String", "comment": "项目ID"}, "py_aplc_no": {"type": "String", "comment": "支付申请编号"}}}, "ads_project_label": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"blng_prj": {"type": "String", "comment": "所属项目"}, "create_psn": {"type": "String", "comment": "创建人"}, "create_time": {"type": "String", "comment": "创建时间"}, "data_id": {"type": "String", "comment": "数据ID"}, "entp_nm": {"type": "String", "comment": "企业名称"}, "lbl_ecd": {"type": "String", "comment": "标签编码"}, "lbl_id": {"type": "String", "comment": "标签ID"}, "lbl_nm": {"type": "String", "comment": "标签名称"}, "lbl_rule": {"type": "String", "comment": "标签规则"}, "lgc_del": {"type": "String", "comment": "逻辑删除"}, "mnplt_tp": {"type": "String", "comment": "操作类型"}, "prj_id": {"type": "String", "comment": "项目id"}, "prj_src_stm": {"type": "String", "comment": "项目来源系统"}, "synz_prj_id": {"type": "String", "comment": "同步汇投（互联）的项目ID"}, "udt_psn": {"type": "String", "comment": "更新人"}, "udt_tm": {"type": "String", "comment": "更新时间"}}}, "ads_reg_users_info": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"address": {"type": "String", "comment": "详细地址"}, "city": {"type": "String", "comment": "所在城市"}, "contact_person_name": {"type": "String", "comment": "联系人"}, "contact_person_phone": {"type": "String", "comment": "联系电话"}, "district": {"type": "String", "comment": "所在辖区"}, "province": {"type": "String", "comment": "所在省"}, "reg_date": {"type": "String", "comment": "注册日期"}, "user_name": {"type": "String", "comment": "全称"}, "user_type": {"type": "String", "comment": "用户类型"}}}, "ads_special_prj_detail": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"hit_num": {"type": "Nullable(Decimal(15, 0))", "comment": "点击数量"}, "ht_amt": {"type": "String", "comment": "挂牌金额（万元）"}, "info_dclo_begin_dt": {"type": "String", "comment": "披露开始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "披露截止日期"}, "is_pl": {"type": "String", "comment": "是否为不披露项目"}, "origin_name": {"type": "String", "comment": "官方专栏二级标签（所属机构）"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "prj_sts": {"type": "String", "comment": "项目所在地区"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}}}, "ads_special_prj_info": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"metric_type": {"type": "String", "comment": "指标类型"}, "monthly": {"type": "String", "comment": "月度"}, "since_launch": {"type": "String", "comment": "上线至今统计"}, "statistic_type": {"type": "String", "comment": "统计类型"}, "y_month": {"type": "String", "comment": "统计年月"}, "year_to_date": {"type": "String", "comment": "本年累计"}}}, "ads_stateownership_prj_info": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"central_amount": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "央企_交易额"}, "central_count": {"type": "Nullable(Decimal(15, 0))", "comment": "央企_项目数"}, "city_amount": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "市级_交易额"}, "city_count": {"type": "Nullable(Decimal(15, 0))", "comment": "市级_项目数"}, "data_dt": {"type": "String", "comment": "统计日期"}, "district_amount": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "区级_交易额"}, "district_count": {"type": "Nullable(Decimal(15, 0))", "comment": "区级_项目数"}, "proj_type": {"type": "String", "comment": "项目类型（产权转让、实物）"}}}, "ads_stateownership_prj_info_detail": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"asset_source": {"type": "String", "comment": "资产来源"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额(万元)"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "final_buyer_count": {"type": "Nullable(Decimal(10, 0))", "comment": "最终征集合格意向受让方个数"}, "oasset_custd_org": {"type": "String", "comment": "国资监管机构"}, "prj_status": {"type": "String", "comment": "项目状态"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_type": {"type": "String", "comment": "业务类型"}, "regulatory_city": {"type": "String", "comment": "监管属地（市）"}, "regulatory_level": {"type": "String", "comment": "监管级别"}, "regulatory_province": {"type": "String", "comment": "监管属地（省）"}, "transferee_name": {"type": "String", "comment": "受让方"}, "transferee_region": {"type": "String", "comment": "受让方所在地"}}}, "ads_transferagent_feestats": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "公司名称"}, "agent_no": {"type": "String", "comment": "会员编号"}, "bulk_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "大宗实物收入"}, "deal_date": {"type": "String", "comment": "成交日期"}, "equity_raise_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "企业增资收入"}, "investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "投融资"}, "property_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "产权转让收入"}, "rental_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "房屋出租收入"}, "small_bulk_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "小宗实物收入"}, "vehicle_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "机动车收入"}}}, "ads_transferagent_feestats_cqzr": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "buyer_name": {"type": "String", "comment": "受让方名称"}, "buyer_name_y": {"type": "String", "comment": "（意向）受让方名称"}, "cbex_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_type": {"type": "String", "comment": "成交方式"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额（万元）"}, "fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收费总额（元）"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_type": {"type": "String", "comment": "交易方式"}, "transfer_reserve_price": {"type": "Nullable(String)", "comment": "转让底价（万元）"}, "transferee_agent": {"type": "String", "comment": "受让方经纪会员名称"}}}, "ads_transferagent_feestats_dzsw": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方经纪会员名称"}, "buyer_name": {"type": "String", "comment": "受让方名称"}, "buyer_name_y": {"type": "String", "comment": "（意向）受让方名称"}, "cbex_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_type": {"type": "String", "comment": "成交方式"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额（万元）"}, "fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收费总额（元）"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "trans_type": {"type": "String", "comment": "交易方式"}, "transfer_reserve_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}, "transferee_agent": {"type": "String", "comment": "受让方经纪会员名称"}}}, "ads_transferagent_feestats_fwcz": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"asset_type": {"type": "String", "comment": "资产类别"}, "cbex_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交总价（元）"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收费总额（元）"}, "info_dclo_begin_dt": {"type": "String", "comment": "披露开始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "披露结束日期"}, "lessee_agent": {"type": "String", "comment": "承租方受托交易服务会员"}, "lessee_agent_final": {"type": "String", "comment": "最终承租方受托交易服务会员"}, "lessee_name": {"type": "String", "comment": "承租方名称"}, "lessor_agent": {"type": "String", "comment": "出租方受托交易服务会员"}, "lessor_name": {"type": "String", "comment": "出租方名称"}, "listing_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌总价（元）"}, "listing_price": {"type": "Nullable(String)", "comment": "挂牌价格（元）"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "prj_type": {"type": "String", "comment": "项目类型"}, "proj_belong_dept_name": {"type": "String", "comment": "项目所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "selection_method": {"type": "String", "comment": "遴选方式"}}}, "ads_transferagent_feestats_jdc": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "任务接收方单位"}, "cbex_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期（解体过户确认日期）"}, "deal_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交价格（元）"}, "disposal_method": {"type": "String", "comment": "处置方式"}, "fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收费总额（元）"}, "free_bid_end_time": {"type": "String", "comment": "自由报价结束时间"}, "free_bid_start_time": {"type": "String", "comment": "自由报价开始时间"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门（任务来源）"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人（业务来源经办）"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（元）"}, "starting_bid": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "起拍价（元）"}, "trans_method": {"type": "String", "comment": "交易方式名称"}, "trans_status": {"type": "String", "comment": "交易状态名称"}}}, "ads_transferagent_feestats_qyzz": {"comment": "", "update_time": "2025-02-24 09:39:09", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "融资方经纪会员名称"}, "cbex_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "deal_date": {"type": "String", "comment": "成交日期"}, "fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收费总额（元）"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "investment_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "投资总额（万元）"}, "prj_stat_cd_dsc": {"type": "String", "comment": "项目状态"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "selection_method": {"type": "String", "comment": "择优方式"}, "seller_fincer_name": {"type": "String", "comment": "融资方名称"}}}, "ads_transferagent_feestats_xzsw": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "任务接收方单位"}, "cbex_fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "北交所收入（元）"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交价格（元）"}, "deal_date": {"type": "String", "comment": "交割日期"}, "deal_status": {"type": "String", "comment": "成交状态"}, "disposal_method": {"type": "String", "comment": "处置方式"}, "fee_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收费总额（元）"}, "free_bid_end_time": {"type": "String", "comment": "自由报价结束时间"}, "free_bid_start_time": {"type": "String", "comment": "自由报价开始时间"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（元）"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称"}, "starting_bid": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "起拍价（元）"}}}, "ads_yqzx_biz_trans_sum_physical_details": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"business_type": {"type": "String", "comment": "业务类型"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "evaluation_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "评估值"}, "incre_rate": {"type": "Decimal(15, 2)", "comment": "增值率%"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价"}, "total_price_incre": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增值金额"}}}, "ads_yqzx_ce_annual_trading_com": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"bid_num1": {"type": "Nullable(Decimal(10, 0))", "comment": "竞价数量1"}, "bid_num2": {"type": "Nullable(Decimal(10, 0))", "comment": "竞价数量2"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_prj_num": {"type": "Nullable(Decimal(10, 0))", "comment": "成交数量"}, "icap_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "拟增资金额"}, "incm": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "收入情况"}, "lit_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额"}, "lit_prj_num": {"type": "Nullable(Decimal(10, 0))", "comment": "挂牌数量"}, "month": {"type": "String", "comment": "归属月"}, "prj_blng_dept_nm": {"type": "String", "comment": "项目所属部门"}, "proj_type": {"type": "String", "comment": "项目类型（产权转让、实物、企业增资）"}, "seller_num": {"type": "Nullable(Decimal(10, 0))", "comment": "转让项目数"}, "year": {"type": "String", "comment": "归属年"}}}, "ads_yqzx_ce_annual_trd_cmp_detail": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方代理会员"}, "bid_method": {"type": "String", "comment": "竞价方式"}, "blng_grp": {"type": "String", "comment": "所属集团"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "evaluation_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "评估价"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "is_valid_bid": {"type": "String", "comment": "是否有效竞价"}, "oasset_custd_org": {"type": "String", "comment": "国资监管机构"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称/融资方名称"}}}, "ads_yqzx_ce_phy_asst_busn_vol_det": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "代理会员"}, "blng_grp": {"type": "String", "comment": "所属集团"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "fund_source": {"type": "String", "comment": "资金来源"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "oasset_custd_org": {"type": "String", "comment": "国资监管机构"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称/融资方名称"}}}, "ads_yqzx_ce_phy_asst_busn_vol_stat": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"cur_or_cum": {"type": "String", "comment": "月度值or累计值"}, "data_dt": {"type": "String", "comment": "统计日期"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_num": {"type": "Nullable(Decimal(10, 0))", "comment": "成交数量"}, "entp_type": {"type": "String", "comment": "企业类型（总计、央企、部委、金融、地方）"}, "lit_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额"}, "lit_prj_num": {"type": "Nullable(Decimal(10, 0))", "comment": "挂牌数量"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}}}, "ads_yqzx_ce_three_busn_mkt_share_stat": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"entp_type": {"type": "String", "comment": "企业类型（央企、部委、金融、地方）"}, "exch": {"type": "String", "comment": "交易所"}, "is_delete": {"type": "String", "comment": "是否删除重复挂牌"}, "lit_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额"}, "lit_prj_num": {"type": "Nullable(Decimal(24, 0))", "comment": "挂牌数量"}, "month": {"type": "String", "comment": "归属月"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_class": {"type": "String", "comment": "项目分类（股权、实物、增资）"}, "year": {"type": "String", "comment": "归属年"}}}, "ads_yqzx_ce_trd_cmp_cap_inc_det": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "代理会员"}, "blng_grp": {"type": "String", "comment": "所属集团"}, "buyer_name": {"type": "String", "comment": "投资方名称"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "economy_type": {"type": "String", "comment": "经济类型"}, "evaluate_equity": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产评估值"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "ivs_way": {"type": "String", "comment": "投资方式"}, "oasset_custd_org": {"type": "String", "comment": "国资监管机构"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称/融资方名称"}}}, "ads_yqzx_gmys_gpxm_qyjt_detail": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"blng_grp": {"type": "String", "comment": "所属集团"}, "data_dt": {"type": "String", "comment": "统计日期"}, "exch": {"type": "String", "comment": "交易所"}, "is_repeat_ht": {"type": "String", "comment": "是否重复挂牌"}, "lit_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_type": {"type": "String", "comment": "项目类型（产权转让、实物、企业增资）"}}}, "ads_yqzx_gmys_gpxm_qyjt_jg_detail": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"belong_group": {"type": "String", "comment": "所属集团"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "exch": {"type": "String", "comment": "交易所"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "is_incre": {"type": "String", "comment": "是否增值"}, "is_repeat_ht": {"type": "String", "comment": "是否重复挂牌"}, "lit_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额"}, "oasset_custd_org": {"type": "String", "comment": "国资监管机构"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "seller_fincer_name": {"type": "String", "comment": "转让方"}}}, "ads_yqzx_jjl_detail": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额（万元）"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "is_incre": {"type": "String", "comment": "是否增值"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}}}, "ads_yqzx_monly_each_index_tot_model": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"data_dt": {"type": "String", "comment": "数据日期"}, "etl_grp_desc": {"type": "String", "comment": "etl组描述"}, "exch": {"type": "String", "comment": "交易所"}, "monly_ind_val": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "月度指标值"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "year_ind_val": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "年度指标值"}}}, "ads_yqzx_nas_trd_inc_prj_stat": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "ent_type": {"type": "String", "comment": "企业类型（央企、部委、金融、地方）"}, "incre_rate": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "溢价率"}, "month": {"type": "String", "comment": "归属月"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "total_price_incre": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增值额"}, "year": {"type": "String", "comment": "归属年"}}}, "ads_yqzx_proj_belong_name_dim": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"c_code": {"type": "String", "comment": "编码"}, "c_name": {"type": "String", "comment": "名称"}, "map": {"type": "String", "comment": "企业类型"}, "type_name": {"type": "String", "comment": "类型"}}}, "ads_yqzx_slyw_cjqk_summary": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"deal_date": {"type": "String", "comment": "成交日期"}, "indicator_type": {"type": "String", "comment": "指标类型"}, "monly_ind_val": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "月度指标值"}, "month": {"type": "String", "comment": "归属月"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_type": {"type": "String", "comment": "项目类型（产权转让、实物、企业增资）"}, "year": {"type": "String", "comment": "归属月"}, "year_ind_val": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "年度指标值"}}}, "ads_yqzx_slyw_cjqk_summary_detail": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "evaluation_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "评估值"}, "incre_rate": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增值率%"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价"}, "total_price_incre": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增值金额"}}}, "ads_yqzx_transaction_summary_cap_detail": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"buyer_name": {"type": "String", "comment": "投资方名称"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_date": {"type": "String", "comment": "成交日期"}, "economy_type": {"type": "String", "comment": "经济类型"}, "evaluate_equity": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产评估值"}, "incre_rate": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增值率%=（溢价率）"}, "ivs_way": {"type": "String", "comment": "投资方式"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "seller_fincer_name": {"type": "String", "comment": "融资方名称"}, "total_price_incre": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增值金额=（增值额）"}}}, "ads_yqzx_trd_cmp_eq_detail": {"comment": "", "update_time": "2025-02-24 09:39:10", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方代理会员"}, "blng_grp": {"type": "String", "comment": "所属集团"}, "deal_date": {"type": "String", "comment": "成交日期"}, "deal_value": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_way_name": {"type": "String", "comment": "成交方式"}, "evaluation_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "评估价"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "oasset_custd_org": {"type": "String", "comment": "国资监管机构"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "sell_price": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称/融资方名称"}}}, "ads_yqzx_zyr_detail": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"agent_mem": {"type": "String", "comment": "转让方代理会员"}, "blng_grp": {"type": "String", "comment": "所属集团"}, "exch": {"type": "String", "comment": "挂牌交易所"}, "ht_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额（万元）"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_expire_dt": {"type": "String", "comment": "信息披露期满日期"}, "is_repeat_ht": {"type": "String", "comment": "是否重复挂牌"}, "oasset_custd_org": {"type": "String", "comment": "国资监管机构"}, "pro_tp": {"type": "String", "comment": "项目类型"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}, "seller_fincer_name": {"type": "String", "comment": "转让方名称/融资方名称"}}}, "ads_yqzx_zzxm_gpqk_detail": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"belong_group": {"type": "String", "comment": "所属集团"}, "data_dt": {"type": "String", "comment": "统计日期"}, "entp_type": {"type": "String", "comment": "企业类型（央企、部委、金融、地方）"}, "exch": {"type": "String", "comment": "交易所"}, "lit_prj_num1": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "挂牌数量（已剔除重复挂牌）"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}}}, "ads_yqzx_zzxm_gpqklj_detail": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"exch": {"type": "String", "comment": "交易所"}, "info_dclo_begin_dt": {"type": "String", "comment": "信息披露起始日期"}, "info_dclo_end_dt": {"type": "String", "comment": "信息披露期满日期"}, "inst_attr": {"type": "String", "comment": "央企/部委"}, "lit_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额"}, "proj_belong_dept_name": {"type": "String", "comment": "所属部门"}, "proj_name": {"type": "String", "comment": "项目名称"}, "proj_no": {"type": "String", "comment": "项目编号"}, "proj_princ_name": {"type": "String", "comment": "项目负责人"}}}, "api_p1_bsn_pcs_aprv_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"aprv_pcs_id": {"type": "String", "comment": "审批流程ID"}, "audit_pass_tm": {"type": "String", "comment": "审核通过时间"}, "audit_rmrk": {"type": "String", "comment": "审核备注"}, "audit_stat": {"type": "String", "comment": "审核状态"}, "audit_tm": {"type": "String", "comment": "审核时间"}, "auditor_blng_cetr_id": {"type": "String", "comment": "审核人所属中心ID"}, "auditor_blng_cetr_nm": {"type": "String", "comment": "审核人所属中心名称"}, "auditor_blng_dept_id": {"type": "String", "comment": "审核人所属部门ID"}, "auditor_blng_dept_nm": {"type": "String", "comment": "审核人所属部门名称"}, "auditor_code": {"type": "String", "comment": "审核人编码"}, "auditor_id": {"type": "String", "comment": "审核人ID"}, "auditor_nm": {"type": "String", "comment": "审核人名称"}, "data_dt": {"type": "String", "comment": "统计日期"}, "exec_act": {"type": "String", "comment": "执行动作"}, "itt_tm": {"type": "String", "comment": "发起时间"}, "node_audit_durt": {"type": "String", "comment": "节点审核时长"}, "node_ster": {"type": "String", "comment": "节点提交人名称"}, "pcs_aprv_tot_durt": {"type": "String", "comment": "流程审批总时长"}, "pcs_ariv_tm": {"type": "String", "comment": "流程到达时间"}, "pcs_cd": {"type": "String", "comment": "流程代码"}, "pcs_id": {"type": "String", "comment": "流程ID"}, "pcs_nm": {"type": "String", "comment": "流程名称"}, "pcs_node_id": {"type": "String", "comment": "流程节点ID"}, "pcs_node_nm": {"type": "String", "comment": "流程节点名称"}, "pcs_stat_cd": {"type": "String", "comment": "流程状态代码"}, "pcs_stat_cd_dsc": {"type": "String", "comment": "流程状态代码描述"}, "pcs_title": {"type": "String", "comment": "流程标题"}, "pmer_blng_cetr_id": {"type": "String", "comment": "发起人所属中心ID"}, "pmer_blng_cetr_nm": {"type": "String", "comment": "发起人所属中心名称"}, "pmer_blng_dept_id": {"type": "String", "comment": "发起人所属部门ID"}, "pmer_blng_dept_nm": {"type": "String", "comment": "发起人所属部门名称"}, "pmer_code": {"type": "String", "comment": "发起人编码"}, "pmer_id": {"type": "String", "comment": "发起人ID"}, "pmer_nm": {"type": "String", "comment": "发起人名称"}, "prewer_id": {"type": "String", "comment": "待审核人ID"}, "prewer_nm": {"type": "String", "comment": "待审核人名称"}, "prj_blng_cetr": {"type": "String", "comment": "项目所属中心"}, "prj_blng_dept": {"type": "String", "comment": "项目所属部门"}, "prj_bsn_tp": {"type": "String", "comment": "项目业务类型"}, "prj_id": {"type": "String", "comment": "项目编号"}, "prj_nm": {"type": "String", "comment": "项目名称"}, "prj_prin": {"type": "String", "comment": "项目负责人"}, "prj_prin_dept": {"type": "String", "comment": "项目负责人部门"}, "prj_stg": {"type": "String", "comment": "项目阶段"}, "prj_tp": {"type": "String", "comment": "项目类型"}, "prj_wrd": {"type": "String", "comment": "项目关键字"}, "publ_tp": {"type": "String", "comment": "披露类型"}, "sort_no": {"type": "String", "comment": "排序号"}, "stm_tp": {"type": "String", "comment": "系统类型"}}}, "api_p1_prj_deal_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"actl_txn_mth_cd": {"type": "Nullable(String)", "comment": "实际交易方式代码"}, "actl_txn_mth_cd_dsc": {"type": "Nullable(String)", "comment": "实际交易方式代码描述"}, "bsn_buyer_id": {"type": "Nullable(String)", "comment": "业务买受方ID"}, "bsn_deal_rec_id": {"type": "Nullable(String)", "comment": "业务成交记录ID"}, "bsn_prj_wrd": {"type": "Nullable(String)", "comment": "业务项目关键字"}, "crt_tm": {"type": "Nullable(String)", "comment": "创建时间"}, "cust_wrd": {"type": "Nullable(String)", "comment": "客户关键字"}, "data_dt": {"type": "String", "comment": "统计日期"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_dt": {"type": "Nullable(String)", "comment": "成交日期"}, "deal_rec_id": {"type": "Nullable(String)", "comment": "成交记录ID"}, "deal_rent_tot_prc": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交租金总价"}, "lease_area": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "出租面积"}, "prj_id": {"type": "Nullable(String)", "comment": "项目编号"}, "prj_nm": {"type": "Nullable(String)", "comment": "项目名称"}, "prj_wrd": {"type": "Nullable(String)", "comment": "项目关键字"}}}, "api_p1_prj_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ast_cgy": {"type": "Nullable(String)", "comment": "资产类别"}, "ast_src_tp": {"type": "Nullable(String)", "comment": "资产来源类型"}, "blng_idy_tp_cd": {"type": "Nullable(String)", "comment": "所属行业类型代码"}, "blng_idy_tp_cd_dsc": {"type": "Nullable(String)", "comment": "所属行业类型代码描述"}, "bond_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "债权金额"}, "bsn_prj_id": {"type": "Nullable(String)", "comment": "业务项目ID"}, "bsn_prj_wrd": {"type": "Nullable(String)", "comment": "业务项目关键字"}, "data_dt": {"type": "String", "comment": "统计日期"}, "eval": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "评估值"}, "is_bakpy_ast_displ": {"type": "Nullable(String)", "comment": "是否破产资产处置"}, "is_civil_court_ast_displ": {"type": "Nullable(String)", "comment": "是否民事法院资产处置"}, "is_milty_court_ast_displ": {"type": "Nullable(String)", "comment": "是否军事法院资产处置"}, "judl_litgn_cgy": {"type": "Nullable(String)", "comment": "司法/诉讼类别"}, "lit_amt": {"type": "Nullable(String)", "comment": "挂牌金额"}, "lit_end_dt": {"type": "Nullable(String)", "comment": "挂牌结束日期"}, "lit_prt_cust_nm": {"type": "Nullable(String)", "comment": "挂牌方客户名称"}, "lit_prt_cust_no": {"type": "Nullable(String)", "comment": "挂牌方客户编号"}, "lit_prt_cust_wrd": {"type": "Nullable(String)", "comment": "挂牌方客户关键字"}, "lit_star_dt": {"type": "Nullable(String)", "comment": "挂牌开始日期"}, "mber_cust_nm": {"type": "Nullable(String)", "comment": "会员客户名称"}, "mber_cust_no": {"type": "Nullable(String)", "comment": "会员客户编号"}, "mber_wrd": {"type": "Nullable(String)", "comment": "会员关键字"}, "ocpy_af_incptl_pct": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 8))", "comment": "占增资后比例"}, "plan_lease_area": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "拟出租面积"}, "plform_prj_id": {"type": "Nullable(String)", "comment": "平台项目ID"}, "pre_publ_prj_wrd": {"type": "Nullable(String)", "comment": "预披露项目关键字"}, "prj_blng_dept_id": {"type": "Nullable(String)", "comment": "项目所属部门ID"}, "prj_blng_dept_nm": {"type": "Nullable(String)", "comment": "项目所属部门名称"}, "prj_bsn_tp_cd": {"type": "Nullable(String)", "comment": "项目业务类型代码"}, "prj_bsn_tp_cd_dsc": {"type": "Nullable(String)", "comment": "项目业务类型代码描述"}, "prj_id": {"type": "Nullable(String)", "comment": "项目编号"}, "prj_nm": {"type": "Nullable(String)", "comment": "项目名称"}, "prj_prin_id": {"type": "Nullable(String)", "comment": "项目负责人ID"}, "prj_prin_nm": {"type": "Nullable(String)", "comment": "项目负责人名称"}, "prj_stat_cd": {"type": "Nullable(String)", "comment": "项目状态代码"}, "prj_stat_cd_dsc": {"type": "Nullable(String)", "comment": "项目状态代码描述"}, "prj_tp_cd": {"type": "Nullable(String)", "comment": "项目类型代码"}, "prj_tp_cd_dsc": {"type": "Nullable(String)", "comment": "项目类型代码描述"}, "prj_wrd": {"type": "Nullable(String)", "comment": "项目关键字"}, "rent_lit_tot_prc": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "租金挂牌总价"}, "txn_stat_cd": {"type": "Nullable(String)", "comment": "交易状态代码"}, "txn_stat_cd_dsc": {"type": "Nullable(String)", "comment": "交易状态代码描述"}}}, "api_p1_stb_prj_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"agen_mber_nm": {"type": "String", "comment": "代理会员名称"}, "blng_grp_nm": {"type": "String", "comment": "所属集团名称"}, "blng_grp_no": {"type": "String", "comment": "所属集团编号"}, "blng_idy": {"type": "String", "comment": "所属行业"}, "cust_cl": {"type": "String", "comment": "客户分类"}, "data_dt": {"type": "String", "comment": "统计日期"}, "deal_amt": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "deal_dt": {"type": "String", "comment": "成交日期"}, "dept_nm": {"type": "String", "comment": "部门名称"}, "exg_nm": {"type": "String", "comment": "交易所名称"}, "exg_no": {"type": "String", "comment": "交易所编号"}, "info_publ_start_dt": {"type": "String", "comment": "信息披露起始日期"}, "prin_nm": {"type": "String", "comment": "负责人名称"}, "prj_id": {"type": "String", "comment": "项目编号"}, "prj_nm": {"type": "String", "comment": "项目名称"}, "prj_tp": {"type": "String", "comment": "项目类型"}, "spvs_tp": {"type": "String", "comment": "监管类型"}, "stb_prj_wrd": {"type": "String", "comment": "同业项目关键字"}, "tfr_prc": {"type": "String", "comment": "转让底价"}, "trsfer_nm": {"type": "String", "comment": "转让方名称"}}}, "api_trsfer_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"acc_bank_int_no": {"type": "String", "comment": "开户行联行号"}, "acc_bank_lo": {"type": "String", "comment": "开户行所在地"}, "acc_bank_nm": {"type": "String", "comment": "开户银行名称"}, "acc_bank_no": {"type": "String", "comment": "开户银行编号"}, "acc_bank_subbr_nm": {"type": "String", "comment": "开户银行支行名称"}, "aprv_dt": {"type": "String", "comment": "批准日期"}, "aprv_file_nm": {"type": "String", "comment": "批准文件名称"}, "aprv_unit_file_tp_cd": {"type": "String", "comment": "批准单位决议文件类型代码"}, "aprv_unit_file_tp_cd_dsc": {"type": "String", "comment": "批准单位决议文件类型代码描述"}, "aprv_unit_nm": {"type": "String", "comment": "批准单位名称"}, "bank_acc_acc_no": {"type": "String", "comment": "银行账户账号"}, "bank_acc_nm": {"type": "String", "comment": "银行账户名称"}, "blng_grp_nm": {"type": "String", "comment": "所属集团名称"}, "blng_idy_cd": {"type": "String", "comment": "所属行业代码"}, "blng_idy_cd_dsc": {"type": "String", "comment": "所属行业代码描述"}, "blng_idy_tp_cd": {"type": "String", "comment": "所属行业类型代码"}, "blng_idy_tp_cd_dsc": {"type": "String", "comment": "所属行业类型代码描述"}, "bsn_prj_wrd": {"type": "String", "comment": "业务项目关键字"}, "bsn_src": {"type": "String", "comment": "业务来源,来源系统编码"}, "bus_lice_exp_dt": {"type": "String", "comment": "营业执照失效日期"}, "cert_no": {"type": "String", "comment": "证件编号"}, "cert_tp_cd": {"type": "String", "comment": "证件类型代码"}, "cert_tp_cd_dsc": {"type": "String", "comment": "证件类型代码描述"}, "city_cd": {"type": "String", "comment": "市代码"}, "city_cd_dsc": {"type": "String", "comment": "市代码描述"}, "cntry_sfep_or_mgr_dept_nm": {"type": "String", "comment": "国家出资企业或主管部门名称"}, "cntry_sfep_or_mgr_dept_no": {"type": "String", "comment": "国家出资企业或主管部门编号"}, "ctacts_addr": {"type": "String", "comment": "联系人地址"}, "ctacts_cert_no": {"type": "String", "comment": "联系人证件编号"}, "ctacts_cert_tp_cd": {"type": "String", "comment": "联系人证件类型代码"}, "ctacts_cert_tp_cd_dsc": {"type": "String", "comment": "联系人证件类型代码描述"}, "ctacts_nm": {"type": "String", "comment": "联系人名称"}, "ctacts_nm_ipubl": {"type": "String", "comment": "联系人名称（外网披露）"}, "ctacts_phon": {"type": "String", "comment": "联系人手机"}, "ctacts_phone_no_ipubl": {"type": "String", "comment": "联系人手机号（外网披露）"}, "ctacts_tel": {"type": "String", "comment": "联系人电话"}, "cust_blng_dept_nm": {"type": "String", "comment": "客户所属部门名称"}, "cust_wrd": {"type": "String", "comment": "客户关键字"}, "ecn_tp_cd": {"type": "String", "comment": "经济类型代码"}, "ecn_tp_cd_dsc": {"type": "String", "comment": "经济类型代码描述"}, "edw_data_dt": {"type": "String", "comment": "EDW数据日期"}, "edw_dt_src_tbl": {"type": "String", "comment": "EDW数据来源主表名"}, "edw_stm_dt": {"type": "String", "comment": "EDW系统日期"}, "email": {"type": "String", "comment": "电子邮件"}, "entp_bus_lice_pht": {"type": "String", "comment": "企业营业执照照片"}, "entp_tp_cd": {"type": "String", "comment": "企业类型代码"}, "entp_tp_cd_dsc": {"type": "String", "comment": "企业类型代码描述"}, "estb_dt": {"type": "String", "comment": "成立日期"}, "fax": {"type": "String", "comment": "传真"}, "is_bus_lice_lotm": {"type": "String", "comment": "是否营业执照为长期"}, "is_sown": {"type": "String", "comment": "是否国有"}, "is_trsfer_anms_publ": {"type": "String", "comment": "是否转让方匿名披露"}, "job": {"type": "String", "comment": "职务"}, "lpsn_nm": {"type": "String", "comment": "法定代表人名称"}, "mailbox": {"type": "String", "comment": "邮箱"}, "oprt_scale_cd": {"type": "String", "comment": "经营规模代码"}, "oprt_scale_cd_dsc": {"type": "String", "comment": "经营规模代码描述"}, "pay_bank_no": {"type": "String", "comment": "支付行号"}, "phone_no": {"type": "String", "comment": "手机号"}, "plan_tfr_pct": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 8))", "comment": "拟转让比例"}, "post_addr": {"type": "String", "comment": "通讯地址"}, "pre_publ_prj_wrd": {"type": "String", "comment": "预披露项目关键字"}, "prj_prt_id": {"type": "String", "comment": "项目方ID"}, "prj_prt_nm": {"type": "String", "comment": "项目方名称"}, "prj_prt_pcds_file_tp_cd": {"type": "String", "comment": "项目方决策文件类型代码"}, "prj_prt_pcds_file_tp_cd_dsc": {"type": "String", "comment": "项目方决策文件类型代码描述"}, "prj_prt_pcds_file_tp_othr_cmnt": {"type": "String", "comment": "项目方决策文件类型其他说明"}, "prj_prt_tp_cd": {"type": "String", "comment": "项目方类型代码"}, "prj_prt_tp_cd_dsc": {"type": "String", "comment": "项目方类型代码描述"}, "prj_prt_wrd": {"type": "String", "comment": "项目方关键字"}, "prj_tp_cd": {"type": "String", "comment": "项目类型代码"}, "prj_tp_cd_dsc": {"type": "String", "comment": "项目类型代码描述"}, "prj_wrd": {"type": "String", "comment": "项目关键字"}, "prov_cd": {"type": "String", "comment": "省代码"}, "prov_cd_dsc": {"type": "String", "comment": "省代码描述"}, "real_incm_cptl": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "实收资本"}, "real_incm_cptl_curr_cd": {"type": "String", "comment": "实收资本币种代码"}, "real_incm_cptl_curr_cd_dsc": {"type": "String", "comment": "实收资本币种代码描述"}, "refund_dire_cd": {"type": "String", "comment": "返款方向代码"}, "refund_dire_cd_dsc": {"type": "String", "comment": "返款方向代码描述"}, "refund_unit_nm": {"type": "String", "comment": "返款单位名称"}, "regn_cnty_cd": {"type": "String", "comment": "区(县)代码"}, "regn_cnty_cd_dsc": {"type": "String", "comment": "区(县)代码描述"}, "rgst_addr": {"type": "String", "comment": "注册地（地址）"}, "rgst_cptl": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本"}, "rgst_cptl_curr_cd": {"type": "String", "comment": "注册资本币种代码"}, "rgst_cptl_curr_cd_dsc": {"type": "String", "comment": "注册资本币种代码描述"}, "rmrk": {"type": "String", "comment": "备注"}, "rslt_file_nm": {"type": "String", "comment": "决议文件名称"}, "sfep_uscc_org_code": {"type": "String", "comment": "出资企业统一社会信用码/组织机构码"}, "sms_ntc_rcpt_nm1": {"type": "String", "comment": "短信通知接收人名称1"}, "sms_ntc_rcpt_nm2": {"type": "String", "comment": "短信通知接收人名称2"}, "sms_ntc_rcpt_phon1": {"type": "String", "comment": "短信通知接收人手机1"}, "sms_ntc_rcpt_phon2": {"type": "String", "comment": "短信通知接收人手机2"}, "sown_spvs_org_cd": {"type": "String", "comment": "国资监管机构代码"}, "sown_spvs_org_cd_dsc": {"type": "String", "comment": "国资监管机构代码描述"}, "spvs_org_city_cd": {"type": "String", "comment": "监管机构属地(市)代码"}, "spvs_org_city_cd_dsc": {"type": "String", "comment": "监管机构属地(市)代码描述"}, "spvs_org_prov_cd": {"type": "String", "comment": "监管机构属地(省)代码"}, "spvs_org_prov_cd_dsc": {"type": "String", "comment": "监管机构属地(省)代码描述"}, "supr_mgr_unit_nm": {"type": "String", "comment": "上级主管单位名称"}, "trsfer_anms_nm": {"type": "String", "comment": "转让方匿名名称"}, "unit_char_cd": {"type": "String", "comment": "单位性质代码"}, "unit_char_cd_dsc": {"type": "String", "comment": "单位性质代码描述"}, "uscc_org_code": {"type": "String", "comment": "统一社会信用代码/组织机构码"}, "utld_nm": {"type": "String", "comment": "单位负责人名称"}, "work_unit": {"type": "String", "comment": "工作单位"}, "zip": {"type": "String", "comment": "邮编"}}}, "g1_prop_sell_alr_deal_proj_audit_fin_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ASSET_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计（万元）"}, "BSN_INCM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "营业收入（万元）"}, "BSN_PFT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "营业利润（万元）"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "LIAB_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "负债总计（万元）"}, "NETPROFIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净利润（万元）"}, "OWNER_EQUITY": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "所有者权益（万元）"}, "PROFIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "利润总额（万元）"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "YEAR": {"type": "String", "comment": "年份"}}}, "g1_prop_sell_alr_deal_proj_buyer_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ADDR": {"type": "String", "comment": "通讯地址/注册地（住所）"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUYER_NAME": {"type": "String", "comment": "意向受让方名称"}, "CONTACT_TEL": {"type": "String", "comment": "联系电话"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "EMAIL": {"type": "String", "comment": "电子邮件"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "IS_UNITE_BUY": {"type": "String", "comment": "是否联合受让"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本（万元）"}, "TYPE_MAN": {"type": "String", "comment": "类型（法人、自然人）"}, "UNITE_NAME": {"type": "String", "comment": "联合体名称"}, "WX_NO": {"type": "String", "comment": "微信号"}}}, "g1_prop_sell_alr_deal_proj_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"APPRV_REC_DT": {"type": "String", "comment": "核准备案日期"}, "APPRV_REC_ORG": {"type": "String", "comment": "核准（备案）机构"}, "ASSET_SUM_BOOK_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计/账面价值（万元）"}, "ASSET_SUM_EVALU_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计/评估价值（万元）"}, "BEE_CC": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "北交所分佣（元）"}, "BID_SCHEME_RELA_MTRL": {"type": "String", "comment": "竞价方案相关资料"}, "BID_WAY": {"type": "String", "comment": "竞价方式"}, "BROK_MEM_CD": {"type": "String", "comment": "经纪会员编码"}, "BROK_MEM_NAME": {"type": "String", "comment": "经纪会员名称"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUYER_CNT": {"type": "Nullable(Decimal(10, 0))", "comment": "意向受让方个数（全部）"}, "BUYER_CNT_DEPOSIT": {"type": "Nullable(Decimal(10, 0))", "comment": "意向受让方个数（已交纳保证金）"}, "BUYER_UNITE_NAME": {"type": "String", "comment": "受让方名称（联合体名称)"}, "BUY_POSTULATE": {"type": "String", "comment": "受让资格条件"}, "CITY": {"type": "String", "comment": "市"}, "CLAIM_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "债权金额（万元）"}, "COMPANY_CODE": {"type": "String", "comment": "统一社会信用代码/组织机构代码"}, "CORP_TYPE": {"type": "String", "comment": "公司类型"}, "CZB_INDUS_CLASS": {"type": "String", "comment": "CZB财政部行业分类"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DCLO_NOTICE_TERM": {"type": "Nullable(Decimal(10, 0))", "comment": "披露公告期（工作日）"}, "DC_APPOINT_WDAY": {"type": "Nullable(Decimal(10, 0))", "comment": "延牌约定工作日"}, "DEAL_AMT_PYMT_MODE": {"type": "String", "comment": "交易价款付款方式"}, "DEAL_TIME": {"type": "String", "comment": "成交时间"}, "DEAL_VALUE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额（万元)"}, "DEPOSIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "保证金金额（万元）"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "ENTP_TYPE": {"type": "String", "comment": "企业类型"}, "EQUITY_BOOK_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产/账面价值（万元）"}, "EQUITY_EVALU_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产/评估价值（万元）"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EVALUATE_DATE": {"type": "String", "comment": "评估基准日"}, "EVALUATE_DATE_AUDITOR": {"type": "String", "comment": "基准日审计机构"}, "EVALUATE_ORG": {"type": "String", "comment": "评估机构"}, "FOUND_DT": {"type": "String", "comment": "成立日期"}, "HOTSPOT_TAG": {"type": "String", "comment": "热点标签"}, "INDUSTRY": {"type": "String", "comment": "所属行业"}, "INDUSTRY_TYPE": {"type": "String", "comment": "所属行业类型"}, "INFO_DCLO_BEGIN_DT": {"type": "String", "comment": "信息披露起始日期"}, "INFO_DCLO_EXPIRE_DT": {"type": "String", "comment": "信息披露期满日期"}, "INT_DECIS_SITU": {"type": "String", "comment": "内部决策情况"}, "IS_CONTAIN_GROUND": {"type": "String", "comment": "是否含有国有划拨土地"}, "IS_CONTN_TECH_IASSET": {"type": "String", "comment": "是否含有技术类无形资产"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "IS_GZ": {"type": "String", "comment": "是否国资"}, "IS_INVO_EMP_SETTLE": {"type": "String", "comment": "是否涉及职工安置"}, "IS_RTMS_PROJ": {"type": "String", "comment": "是否混改项目"}, "LAW_FIRM": {"type": "String", "comment": "律师事务所"}, "LEAD_OBJECT_CORP_ACTL_CTRL_TFR": {"type": "String", "comment": "导致标的企业实际控制权转移"}, "LEGAL_PERSON": {"type": "String", "comment": "法定代表人"}, "LIAB_SUM_BOOK_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "负债总计/账面价值（万元）"}, "LIAB_SUM_EVALU_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "负债总计/评估价值（万元）"}, "MANAGER_SCALE": {"type": "String", "comment": "经营规模"}, "MEM_LEGAL_CONTACT": {"type": "String", "comment": "会员联系人"}, "MGMT_IS_PRTCPT_BUY": {"type": "String", "comment": "管理层是否参与受让"}, "NOT_COLL_BUYER_OPTION": {"type": "String", "comment": "未征集到意向受让方选项"}, "OBJECT_CORP_NAME": {"type": "String", "comment": "标的企业名称"}, "OTHER_CONDITIONS": {"type": "String", "comment": "与转让相关的其他条件"}, "OTHER_DCLO_CONTENT": {"type": "String", "comment": "其他披露的内容"}, "OTHER_SHARD_PBUY_WEIGHT": {"type": "String", "comment": "其他股东是否放弃优先受让权"}, "PAICL_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "实收资本(万元)"}, "PAY_TM": {"type": "String", "comment": "交纳时间"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROJ_BELONG_DEPT": {"type": "String", "comment": "项目所属部门"}, "PROJ_PRINC": {"type": "String", "comment": "项目负责人"}, "PROJ_STATUS": {"type": "String", "comment": "项目状态"}, "PROV": {"type": "String", "comment": "省"}, "REGION_COUNTY": {"type": "String", "comment": "区（县）"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本(万元)"}, "RGST_ADDR": {"type": "String", "comment": "注册地(住所)"}, "SELL_OBJECT_EVALU": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让标的对应评估值（万元）"}, "SELL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}, "WDAY": {"type": "Nullable(Decimal(10, 0))", "comment": "工作日"}, "WEB_BID_TYPE": {"type": "String", "comment": "网络竞价类型"}, "WORKERS_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "职工人数"}}}, "g1_prop_sell_alr_deal_proj_stock_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "HOLDER_NAME": {"type": "String", "comment": "股东名称"}, "HOLDER_TYPE": {"type": "String", "comment": "股东类型（原股东，新股东)"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "SELL_BACK_STOCK_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "转让后持股比例"}, "SELL_BEFORE_STOCK_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "转让前持股比例"}}}, "g1_prop_sell_frml_dclo_proj_audit_fin_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ASSET_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计（万元）"}, "BSN_INCM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "营业收入（万元）"}, "BSN_PFT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "营业利润(万元)"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "INFO_DCLO_BEGIN_DT": {"type": "String", "comment": "信息披露起始日期"}, "LIAB_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "负债总计（万元）"}, "NETPROFIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净利润（万元）"}, "OWNER_EQUITY": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "所有者权益（万元）"}, "PROFIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "利润总额（万元）"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "YEAR": {"type": "String", "comment": "年份"}}}, "g1_prop_sell_frml_dclo_proj_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"APPRV_REC_DT": {"type": "String", "comment": "核准备案日期"}, "APPRV_REC_ORG": {"type": "String", "comment": "核准（备案）机构"}, "ASSET_SUM_BOOK_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计/账面价值(万元)"}, "ASSET_SUM_EVALU_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计/评估价值(万元)"}, "AUTHORIZE_UNIT_DECIS_DOC_TYPE": {"type": "String", "comment": "批准单位决议文件类型"}, "AUTHORIZE_UNIT_NAME": {"type": "String", "comment": "批准单位名称"}, "AUTHORZE_DATE": {"type": "String", "comment": "批准日期"}, "AUTH_ORG": {"type": "String", "comment": "批准机构"}, "BID_SCHEME_RELA_MTRL": {"type": "String", "comment": "竞价方案相关资料"}, "BID_WAY": {"type": "String", "comment": "竞价方式"}, "BROK_MEM_CD": {"type": "String", "comment": "经纪会员编码"}, "BROK_MEM_NAME": {"type": "String", "comment": "经纪会员名称"}, "BSN_LCNS_IS_LTERM": {"type": "String", "comment": "营业执照是否长期"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUY_POSTULATE": {"type": "String", "comment": "受让资格条件"}, "CITY": {"type": "String", "comment": "市"}, "CLAIM_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "债权金额（万元）"}, "COMPANY_CODE": {"type": "String", "comment": "统一社会信用代码/组织机构代码"}, "CORP_TYPE": {"type": "String", "comment": "公司类型"}, "CTY_CONTRI_CORP_LEAD_DEPT": {"type": "String", "comment": "国家出资企业或主管部门"}, "CUSTD_ORG_DEPDC_CITY": {"type": "String", "comment": "监管机构属地(市)"}, "CUSTD_ORG_DEPDC_PROV": {"type": "String", "comment": "监管机构属地(省)"}, "CZB_INDUS_CLASS": {"type": "String", "comment": "CZB财政部行业分类"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DCLO_NOTICE_TERM": {"type": "Nullable(Decimal(10, 0))", "comment": "披露公告期（工作日）"}, "DC_APPOINT_WDAY": {"type": "Nullable(Decimal(10, 0))", "comment": "延牌约定工作日"}, "DEAL_AMT_PYMT_MODE": {"type": "String", "comment": "交易价款付款方式"}, "DECISION_FILE_TYPE": {"type": "String", "comment": "转让方决策文件类型"}, "DECIS_DOC_NAME": {"type": "String", "comment": "决议文件名称"}, "DEPOSIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "保证金金额（万元）"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "ENTP_TYPE": {"type": "String", "comment": "企业类型"}, "EQUITY_BOOK_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产/账面价值（万元）"}, "EQUITY_EVALU_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产/评估价值（万元）"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EVALUATE_DATE": {"type": "String", "comment": "评估基准日"}, "EVALUATE_DATE_AUDITOR": {"type": "String", "comment": "基准日审计机构"}, "EVALUATE_ORG": {"type": "String", "comment": "评估机构"}, "FOUND_DT": {"type": "String", "comment": "成立日期"}, "HOLD_STOCK_PERCENT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "持有(产)股权比例(%)"}, "HOTSPOT_TAG": {"type": "String", "comment": "热点标签"}, "INDUSTRY": {"type": "String", "comment": "所属行业"}, "INDUSTRY_TYPE": {"type": "String", "comment": "所属行业类型"}, "INFO_DCLO_BEGIN_DT": {"type": "String", "comment": "信息披露起始日期"}, "INFO_DCLO_EXPIRE_DT": {"type": "String", "comment": "信息披露期满日期"}, "INT_DECIS_SITU": {"type": "String", "comment": "内部决策情况"}, "IS_CONTAIN_GROUND": {"type": "String", "comment": "是否含有国有划拨土地"}, "IS_CONTN_TECH_IASSET": {"type": "String", "comment": "是否含有技术类无形资产"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "IS_GZ": {"type": "String", "comment": "是否国资"}, "IS_INVO_EMP_SETTLE": {"type": "String", "comment": "是否涉及职工安置"}, "IS_RTMS_PROJ": {"type": "String", "comment": "是否混改项目"}, "LAW_FIRM": {"type": "String", "comment": "律师事务所"}, "LEAD_OBJECT_CORP_ACTL_CTRL_TFR": {"type": "String", "comment": "导致标的企业实际控制权转移"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "LEGAL_CONTACT_PHONE_NO": {"type": "String", "comment": "联系人手机号"}, "LEGAL_PERSON": {"type": "String", "comment": "法定代表人"}, "LIAB_SUM_BOOK_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "负债总计/账面价值(万元)"}, "LIAB_SUM_EVALU_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "负债总计/评估价值（万元）"}, "LICENSE_END_DATE": {"type": "String", "comment": "营业执照失效日期"}, "MANAGER_SCALE": {"type": "String", "comment": "经营规模"}, "MEM_LEGAL_CONTACT": {"type": "String", "comment": "会员联系人"}, "MGMT_IS_PRTCPT_BUY": {"type": "String", "comment": "管理层是否参与受让"}, "NOT_COLL_BUYER_OPTION": {"type": "String", "comment": "未征集到意向受让方选项"}, "OASSET_CUSTD_ORG": {"type": "String", "comment": "国资监管机构"}, "OBJECT_CORP_NAME": {"type": "String", "comment": "标的企业名称"}, "OTHER_CONDITIONS": {"type": "String", "comment": "与转让相关的其他条件"}, "OTHER_DCLO_CONTENT": {"type": "String", "comment": "其他披露的内容"}, "OTHER_SHARD_PBUY_WEIGHT": {"type": "String", "comment": "其他股东是否放弃优先受让权"}, "PAICL_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "实收资本(万元)"}, "PAY_TM": {"type": "String", "comment": "交纳时间"}, "PREP_SELL_STOCK_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "拟转让(产)股权比例(%)"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROJ_BELONG_DEPT": {"type": "String", "comment": "项目所属部门"}, "PROJ_PRINC": {"type": "String", "comment": "项目负责人"}, "PROJ_STATUS": {"type": "String", "comment": "项目状态"}, "PROV": {"type": "String", "comment": "省"}, "REGION_COUNTY": {"type": "String", "comment": "区（县）"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额(万元)"}, "RGST_ADDR": {"type": "String", "comment": "注册地(住所)"}, "SELLER_CITY": {"type": "String", "comment": "市"}, "SELLER_CORP_TYPE": {"type": "String", "comment": "企业类型"}, "SELLER_ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "SELLER_FOUND_DT": {"type": "String", "comment": "成立日期"}, "SELLER_INDUSTRY": {"type": "String", "comment": "所属行业"}, "SELLER_INDUSTRY_TYPE": {"type": "String", "comment": "所属行业类型"}, "SELLER_LEGAL_PERSON": {"type": "String", "comment": "法定代表人"}, "SELLER_MANAGER_SCALE": {"type": "String", "comment": "经营规模"}, "SELLER_NAME": {"type": "String", "comment": "转让方名称"}, "SELLER_PAICL_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "实收资本(万元)"}, "SELLER_PROV": {"type": "String", "comment": "省"}, "SELLER_REGION_COUNTY": {"type": "String", "comment": "区（县）"}, "SELLER_REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本(万元)"}, "SELLER_RGST_ADDR": {"type": "String", "comment": "注册地（地址）"}, "SELLER_TYPE": {"type": "String", "comment": "转让方类型"}, "SELL_OBJECT_EVALU": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让标的对应评估值（万元）"}, "SELL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}, "WDAY": {"type": "Nullable(Decimal(10, 0))", "comment": "工作日"}, "WEB_BID_TYPE": {"type": "String", "comment": "网络竞价类型"}, "WORKERS_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "职工人数"}}}, "g1_prop_sell_frml_dclo_proj_stock_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"DATA_DT": {"type": "String", "comment": "数据日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "HOLDER_NAME": {"type": "String", "comment": "股东名称"}, "HOLDER_TYPE": {"type": "String", "comment": "股东类型（原股东，新股东)"}, "INFO_DCLO_BEGIN_DT": {"type": "String", "comment": "信息披露起始日期"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "SELL_BEFORE_STOCK_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "转让前持股比例"}}}, "g2_corp_icap_deal_proj_apply_investor_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ADDR": {"type": "String", "comment": "通讯地址/注册地（住所）"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUYER_NAME": {"type": "String", "comment": "意向投资方名称"}, "CONTACT_TEL": {"type": "String", "comment": "联系电话"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "EMAIL": {"type": "String", "comment": "电子邮件"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本（万元）"}, "TYPE_MAN": {"type": "String", "comment": "类型（法人、自然人）"}}}, "g2_rsch_cntr_icap_prj_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ADD_FNDD_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "增加出资额(万元)"}, "APPRV_REC_ORG": {"type": "String", "comment": "核准（备案）机构"}, "ASSET_SUM_EVALU_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计/评估价值（万元）"}, "AUDT_RPT_ANUL": {"type": "String", "comment": "审计报告_年度"}, "AUDT_RPT_ASSET_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "审计报告_资产总计（万元）"}, "AUDT_RPT_BSN_INCM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "审计报告_营业收入（万元）"}, "AUDT_RPT_BSN_PFT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "审计报告_营业利润（万元）"}, "AUDT_RPT_LIAB_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "审计报告_负债总计（万元）"}, "AUDT_RPT_NETPROFIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "审计报告_净利润（万元）"}, "AUDT_RPT_OWNER_EQUITY": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "审计报告_所有者权益（万元）"}, "AUTHORIZE_FILE_TYPE": {"type": "String", "comment": "批准文件类型"}, "AUTHORIZE_UNIT_NM": {"type": "String", "comment": "批准单位名称"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUYER_NAME": {"type": "String", "comment": "投资方名称"}, "CAPITAL_PLAN": {"type": "String", "comment": "增资方案主要内容"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "DEPOSIT_OR_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 8))", "comment": "保证金金额或比例（%）"}, "DEPOSIT_PCSG_MOD": {"type": "String", "comment": "保证金处理方式"}, "DEPT_PNP": {"type": "String", "comment": "部门负责人"}, "EMP_IS_PCP_ICAP": {"type": "String", "comment": "职工是否参与增资"}, "EQUITY_BOOK_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产账面价值（万元）"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EVALUATE_EQUITY": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产评估值（万元）"}, "FINAL_RATIO": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 8))", "comment": "增资后出资比例（%）"}, "FINCER_BRKR_MBSH_NM": {"type": "String", "comment": "融资方经纪会员名称"}, "FINCER_CTY_CONTRI_CORP_LEAD_DEPT": {"type": "String", "comment": "融资方国家出资企业或主管部门"}, "FINCER_DS_MK_FILE_TP": {"type": "String", "comment": "融资方决策文件类型"}, "FINCER_ECONOMY_TYPE": {"type": "String", "comment": "融资方经济类型"}, "FINCER_IDY_TP": {"type": "String", "comment": "融资方行业类型"}, "FINCER_INDUSTRY": {"type": "String", "comment": "融资方所属行业"}, "FINCER_NM": {"type": "String", "comment": "融资方名称"}, "FINCER_OASSET_REG_ORG": {"type": "String", "comment": "融资方国资监管机构"}, "FINCER_SITE_CITY": {"type": "String", "comment": "融资方所在地（市）"}, "FINCER_SITE_PROV": {"type": "String", "comment": "融资方所在地（省）"}, "FINCER_SITE_ZON_CNTY": {"type": "String", "comment": "融资方所在地（区/县）"}, "FINCER_ZONE": {"type": "String", "comment": "融资方所在地区"}, "HOLDER_PERCENT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 8))", "comment": "增资前出资比例（%）"}, "ICAP_AF_ENTP_STOCK": {"type": "String", "comment": "增资后企业股权结构"}, "ICAP_CD": {"type": "String", "comment": "增资条件"}, "ICAP_MOD": {"type": "String", "comment": "增资方式"}, "ICAP_RCH_OR_SUSPSN_CD": {"type": "String", "comment": "增资达成或中止条件"}, "INFO_ANC_EXP_AR": {"type": "String", "comment": "信息发布期满安排"}, "INFO_ESR_BEG_DT": {"type": "String", "comment": "信息披露起始日期"}, "INFO_ESR_EXP_DT": {"type": "String", "comment": "信息披露期满日期"}, "INTND_NEW_INVESTOR_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "拟新增投资方数量"}, "INTND_RS_CPTL_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "拟募集资金金额（万元）"}, "INTND_RS_CPTL_CRPND_HOLD_SHR_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 8))", "comment": "拟募集资金对应持股比例（%）"}, "INTNT_INVESTOR_NUM": {"type": "String", "comment": "意向投资方数量"}, "INTNT_INVESTOR_NUM_PAYED": {"type": "String", "comment": "意向投资方数量（已交保）"}, "INVESTOR_ACCEPT_RESULT": {"type": "String", "comment": "投资方机构审核结果"}, "INVESTOR_BRKR_MBSH_NM": {"type": "String", "comment": "投资方经纪会员名称"}, "INVESTOR_BUSINESS_SCOPE": {"type": "String", "comment": "投资方经营范围"}, "INVESTOR_ECONOMY_TYPE": {"type": "String", "comment": "投资方经济类型"}, "INVESTOR_IVS_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 8))", "comment": "投资方投资所占比例（%）"}, "INVESTOR_POSTULATE": {"type": "String", "comment": "投资方资格条件"}, "INVESTOR_REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "投资方注册资本（万元）"}, "INVESTOR_SITE_CITY": {"type": "String", "comment": "投资方所在地（市）"}, "INVESTOR_SITE_PROV": {"type": "String", "comment": "投资方所在地（省）"}, "INVESTOR_SITE_ZON_CNTY": {"type": "String", "comment": "投资方所在地（区/县）"}, "INVESTOR_STS": {"type": "String", "comment": "投资方状态"}, "INVESTOR_TP": {"type": "String", "comment": "投资方类型"}, "IS_CNTR_ENT": {"type": "String", "comment": "是否央企"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "IS_FNC_CPTL_INVESTOR": {"type": "String", "comment": "是否为金融资本投资方"}, "IVS_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "投资金额（万元）"}, "IVS_WAY": {"type": "String", "comment": "投资方式"}, "MDL_MOD": {"type": "String", "comment": "成交方式"}, "MDL_TOT_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交总金额(万元)"}, "ORIG_SHRH_IS_PCP_ICAP": {"type": "String", "comment": "原股东是否参与增资"}, "OTHR_ESR_ITM": {"type": "String", "comment": "其他披露事项"}, "PAICL_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "实收资本（万元）"}, "PAY_MODE": {"type": "String", "comment": "支付方式"}, "PAY_TM": {"type": "String", "comment": "交纳时间"}, "PREFER_MOD": {"type": "String", "comment": "择优方式"}, "PREFER_SCM": {"type": "String", "comment": "择优方案"}, "PREP_INVEST_TOTAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "拟投资资金总额(万元)"}, "PREP_NEW_REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "拟新增注册资本（万元）"}, "PRJ_BLNG_DEPT": {"type": "String", "comment": "项目所属部门"}, "PRJ_LBL": {"type": "String", "comment": "项目标签"}, "PRJ_PNP": {"type": "String", "comment": "项目负责人"}, "PRJ_STS": {"type": "String", "comment": "项目状态"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "RCTLY_1_PRD_FIN_RPT_ASSET_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "最近一期财务报表_资产总计（万元）"}, "RCTLY_1_PRD_FIN_RPT_BSN_INCM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "最近一期财务报表_营业收入（万元）"}, "RCTLY_1_PRD_FIN_RPT_BSN_PFT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "最近一期财务报表_营业利润（万元）"}, "RCTLY_1_PRD_FIN_RPT_LIAB_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "最近一期财务报表_负债总计（万元）"}, "RCTLY_1_PRD_FIN_RPT_OWNER_EQUITY": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "最近一期财务报表_所有者权益（万元）"}, "RCTLY_FIN_RPT_NETPROFIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "最近一期财务报表_净利润（万元）"}, "RCTLY_FIN_RPT_STMT_DATE": {"type": "String", "comment": "最近一期财务报表_报表日期"}, "RCTLY_FIN_RPT_STMT_TYPE": {"type": "String", "comment": "最近一期财务报表_报表类型"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本（万元）"}, "RSLT_PBC_END_TM": {"type": "String", "comment": "结果公示结束时间"}, "RSLT_PBC_STRT_TM": {"type": "String", "comment": "结果公示开始时间"}, "SHRH_NM": {"type": "String", "comment": "股东名称"}, "SHRH_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "股东个数"}, "SHRH_TP": {"type": "String", "comment": "股东类型"}, "SIGN_DT": {"type": "String", "comment": "签约日期"}, "TOT_RLSE_EQTY_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 8))", "comment": "释放股权比例之和(%)"}, "UNIT_REG_CAPITAL_CRPND_EVALU": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "单位注册资本对应评估值(元)"}, "USE_OF_RAISED_FUNDS": {"type": "String", "comment": "募集资金用途"}, "WORKERS_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "职工人数"}}}, "g3_barti_alr_deal_proj_asset_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ASSET_CGY": {"type": "String", "comment": "资产类别"}, "CNSTRING_PROJ_DESC": {"type": "String", "comment": "在建工程描述"}, "CNSTRING_PROJ_POSIT": {"type": "String", "comment": "在建工程坐落位置"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "HOUSE_ARCH_AREA": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "房屋建筑面积（平方米）"}, "HOUSE_AUX_EQUIP": {"type": "String", "comment": "房屋附属设施"}, "HOUSE_CURR_USAGE": {"type": "String", "comment": "房屋（目前）用途"}, "HOUSE_POSIT": {"type": "String", "comment": "房屋坐落位置"}, "HOUSE_USED_YEARS": {"type": "Nullable(Decimal(3, 0))", "comment": "房屋已用年限"}, "HOUSE_USE_YEARS": {"type": "Nullable(Decimal(3, 0))", "comment": "房屋使用年限"}, "HS_PTY_CERT_NO": {"type": "String", "comment": "房产证号"}, "INVTRY_CNT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "存货数"}, "INVTRY_NAME": {"type": "String", "comment": "存货名称"}, "INVTRY_SITE": {"type": "String", "comment": "存货所在地"}, "INVTRY_SPEC_MODEL": {"type": "String", "comment": "存货（规格）型号"}, "INVTRY_UNIT": {"type": "String", "comment": "存货计量单位"}, "LAND_AREA": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "土地面积（平方米）"}, "LAND_CERT_NO": {"type": "String", "comment": "土地证号"}, "LAND_POSIT": {"type": "String", "comment": "土地坐落位置"}, "LAND_TYPE": {"type": "String", "comment": "土地类型"}, "LAND_USAGE": {"type": "String", "comment": "土地用途"}, "LAND_USED_YEARS": {"type": "Nullable(Decimal(3, 0))", "comment": "土地已用年限"}, "LAND_USE_YEARS": {"type": "Nullable(Decimal(3, 0))", "comment": "土地使用年限"}, "MACHINE_EQUIP_CNT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "机械设备数量"}, "MACHINE_EQUIP_NAME": {"type": "String", "comment": "机械设备名称"}, "MACHINE_EQUIP_NEW_RATE": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "机械设备成新率（%）"}, "MACHINE_EQUIP_SITE": {"type": "String", "comment": "机械设备所在地"}, "MACHINE_EQUIP_SPEC_MODEL": {"type": "String", "comment": "机械设备（规格）型号"}, "MACHINE_EQUIP_UNIT": {"type": "String", "comment": "机械设备计量单位"}, "OTHER_CNT": {"type": "Nullable(Decimal(10, 0))", "comment": "其他数量"}, "OTHER_MODEL": {"type": "String", "comment": "其他型号"}, "OTHER_NAME": {"type": "String", "comment": "其他名称"}, "OTHER_SITE": {"type": "String", "comment": "其他所在地"}, "OTHER_UNIT": {"type": "String", "comment": "其他计量单位"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "TRAFF_TOOL_BUY_DT": {"type": "String", "comment": "交通运输工具购置日期"}, "TRAFF_TOOL_CFG_SITU": {"type": "String", "comment": "交通运输工具配置情况"}, "TRAFF_TOOL_CNT": {"type": "Nullable(Decimal(10, 0))", "comment": "交通运输工具数量"}, "TRAFF_TOOL_COLOR": {"type": "String", "comment": "交通运输工具颜色"}, "TRAFF_TOOL_FLY_VKT": {"type": "String", "comment": "交通运输工具飞行里程数"}, "TRAFF_TOOL_IS_ACRAFT": {"type": "String", "comment": "交通运输工具是否飞行器"}, "TRAFF_TOOL_MODEL": {"type": "String", "comment": "交通运输工具型号"}, "TRAFF_TOOL_NATION": {"type": "String", "comment": "交通运输工具国籍"}, "TRAFF_TOOL_PLATE_NO": {"type": "String", "comment": "交通运输工具号牌号码"}, "TRAFF_TOOL_REGISTER_DATE": {"type": "String", "comment": "交通运输工具登记日期"}, "TRAFF_TOOL_SITE": {"type": "String", "comment": "交通运输工具所在地"}, "TRAFF_TOOL_STEER_VKT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "交通运输工具行驶公里数(万)"}, "TRAFF_TOOL_USE_TM": {"type": "Nullable(Decimal(3, 0))", "comment": "交通运输工具使用时间(年)"}}}, "g3_barti_alr_deal_proj_buyer_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ADDR": {"type": "String", "comment": "通讯地址/注册地（住所）"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUYER_NAME": {"type": "String", "comment": "意向受让方名称"}, "CITY": {"type": "String", "comment": "市"}, "CONTACT_TEL": {"type": "String", "comment": "联系电话"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "EMAIL": {"type": "String", "comment": "电子邮件"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "IS_RIGHT": {"type": "String", "comment": "是否权利人"}, "IS_UNITE_BUY": {"type": "String", "comment": "是否联合受让"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROV": {"type": "String", "comment": "省"}, "REGION_COUNTY": {"type": "String", "comment": "区县"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本（万元）"}, "REG_CAP_CURRENCY": {"type": "String", "comment": "注册资本币种"}, "TYPE_MAN": {"type": "String", "comment": "类型(法人、自然人)"}, "UNITE_NAME": {"type": "String", "comment": "联合体名称"}}}, "g3_barti_alr_deal_proj_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"APPRV_REC_DT": {"type": "String", "comment": "核准（备案）日期"}, "APPRV_REC_ORG": {"type": "String", "comment": "核准（备案）机构"}, "ASSET_CGY": {"type": "String", "comment": "资产类别"}, "ASSET_EVALU": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产评估值(万元)"}, "ASSET_SRC": {"type": "String", "comment": "资产来源"}, "AUTHORIZE_FILE_TYPE": {"type": "String", "comment": "批准文件类型"}, "AUTHORIZE_UNIT_NAME": {"type": "String", "comment": "批准单位名称"}, "AUTHORZE_DATE": {"type": "String", "comment": "批准日期"}, "AUTH_DOC_NAME": {"type": "String", "comment": "批准文件名称"}, "BID_CUTT_NAME": {"type": "String", "comment": "竞价场次名称"}, "BOOK_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "帐面价值(万元)"}, "BROK_MEM_CD": {"type": "String", "comment": "经纪会员编码"}, "BROK_MEM_NAME": {"type": "String", "comment": "经纪会员名称"}, "BUYER_CNT": {"type": "String", "comment": "意向受让方个数（全部）"}, "BUYER_CNT_DEPOSIT": {"type": "String", "comment": "意向受让方个数（已交纳保证金））"}, "BUYER_IS_MANU_CHECK": {"type": "String", "comment": "受让方是否需人工审核"}, "BUYER_PRTCPT_TYPE": {"type": "String", "comment": "受让方参与类型"}, "BUYER_UNITE_NAME": {"type": "String", "comment": "受让方名称（联合体名称)"}, "CHANGE_EXCHANGE_TYPE_REASON": {"type": "String", "comment": "更改交易方式原因"}, "CONTACT_ZIP_CODE": {"type": "String", "comment": "邮编"}, "CONT_EFFECT_DT": {"type": "String", "comment": "合同生效日期"}, "CONT_SIGN_DT": {"type": "String", "comment": "合同签订日期"}, "CTY_CONTRI_CORP_LEAD_DEPT_NAME": {"type": "String", "comment": "国家出资企业或主管部门名称"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DCLO_NOTICE_TERM": {"type": "Nullable(Decimal(10, 0))", "comment": "披露公告期（工作日）"}, "DEAL_ORG_CHECK_OPIN": {"type": "String", "comment": "交易机构审核意见"}, "DEAL_TIME": {"type": "String", "comment": "成交时间"}, "DEAL_VALUE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额（万元）"}, "DEAL_WAY": {"type": "String", "comment": "成交方式（实际交易方式）"}, "DECISION_FILE_TYPE": {"type": "String", "comment": "转让方决策文件类型"}, "DEPOSIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "保证金金额(万元)"}, "DISP_METHOD": {"type": "String", "comment": "处置方法"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "EMAIL": {"type": "String", "comment": "电子邮件"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EVALUATE_DATE": {"type": "String", "comment": "评估基准日"}, "EVALUATE_ORG_NAME": {"type": "String", "comment": "评估机构名称"}, "EXCHANGE_TYPE": {"type": "String", "comment": "交易方式"}, "EXERC_TERM": {"type": "Nullable(Decimal(10, 0))", "comment": "行权期(秒)"}, "EXPL_PLAN": {"type": "String", "comment": "踏勘安排"}, "FAX": {"type": "String", "comment": "传真"}, "FREE_BID_TERM": {"type": "Nullable(Decimal(10, 0))", "comment": "自由报价期(工作日)"}, "FREE_BID_TERM_END_TM": {"type": "String", "comment": "自由报价期结束时间"}, "GUAR_ITEM": {"type": "String", "comment": "保证事项"}, "HIGT_BID_CONSI_TERM": {"type": "Nullable(Decimal(10, 0))", "comment": "最高报价考虑期(秒)"}, "INCRE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "加价幅度(万元)"}, "INFO_DCLO_BEGIN_DT": {"type": "String", "comment": "信息披露起始日期"}, "INFO_DCLO_BEGIN_TM_OPTION": {"type": "String", "comment": "信息披露起始时间选项"}, "INFO_DCLO_EXPIRE_DT": {"type": "String", "comment": "信息披露期满日期"}, "IS_ALLOW_BEGIN_PRICE_BID": {"type": "String", "comment": "是否允许以起始价报价"}, "IS_BKRPT_CORP_ARTI_ASSET": {"type": "String", "comment": "是否破产企业实物资产"}, "IS_GZ": {"type": "String", "comment": "是否国有资产转让"}, "IS_SYNC_PLANE_CGY_ASSET_COL": {"type": "String", "comment": "是否同步飞机类资产专栏"}, "LEGAL_PERSON": {"type": "String", "comment": "法定代表人"}, "LIMIT_BID_PERIOD": {"type": "Nullable(Decimal(10, 0))", "comment": "限时报价周期(秒)"}, "MEM_LEGAL_CONTACT": {"type": "String", "comment": "会员联系人"}, "MONIT_ASSET_CGY": {"type": "String", "comment": "监测资产类别"}, "NOT_COLL_BUYER_OPTION": {"type": "String", "comment": "未征集到意向受让方选项"}, "OASSET_CUSTD_ORG": {"type": "String", "comment": "国资监管机构"}, "OBJECT_IS_EXIST_MTG_SITU": {"type": "String", "comment": "标的是否存在抵押情况"}, "OBJECT_SITE_CITY": {"type": "String", "comment": "标的所在地（市）"}, "OBJECT_SITE_PROV": {"type": "String", "comment": "标的所在地（省）"}, "OBJECT_SITE_REGION_COUNTY": {"type": "String", "comment": "标的所在地（区县）"}, "OTHER_DCLO_CONTENT": {"type": "String", "comment": "其他披露的内容"}, "PAYMENT_STYLE": {"type": "String", "comment": "价款支付方式"}, "PAY_STOP_TM": {"type": "String", "comment": "交纳截止时间"}, "PAY_WAY": {"type": "String", "comment": "交纳方式"}, "POSTA_ADDR": {"type": "String", "comment": "通讯地址"}, "PREP_TOTAL_PERCENT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "拟转让比例(%)"}, "PRICE_DSPLY_CORP": {"type": "String", "comment": "价格显示单位"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROJ_BELONG_DEPT": {"type": "String", "comment": "项目所属部门"}, "PROJ_PRINC": {"type": "String", "comment": "项目负责人"}, "PROJ_STATUS": {"type": "String", "comment": "项目状态"}, "PROJ_TAG": {"type": "String", "comment": "项目标签"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本(万元)"}, "REG_CAP_CURRENCY": {"type": "String", "comment": "注册资本币种"}, "RIGHT_IS_APPLY_EXERC_PRIORITY": {"type": "String", "comment": "权利人是否意向行使优先购买权"}, "SELLER_CONTACT_TEL": {"type": "String", "comment": "转让方联系电话"}, "SELLER_LEGAL_CONTACT": {"type": "String", "comment": "转让方联系人"}, "SELLER_NAME": {"type": "String", "comment": "转让方名称"}, "SELLER_SITE": {"type": "String", "comment": "转让方所在地注册地"}, "SELLER_SITE_CITY": {"type": "String", "comment": "转让方所在地（市）"}, "SELLER_SITE_PROV": {"type": "String", "comment": "转让方所在地（省）"}, "SELLER_SITE_REGION_COUNTY": {"type": "String", "comment": "转让方所在地（区/县）"}, "SELLER_TYPE": {"type": "String", "comment": "转让方类型"}, "SELLER_WAY": {"type": "String", "comment": "转让方式"}, "SELL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "转让底价（万元）"}, "TRANSFEREE_CONDITION": {"type": "String", "comment": "受让方资格条件"}, "TRANSFER_CONDITION": {"type": "String", "comment": "与转让相关其他条件"}}}, "g3_barti_alr_deal_proj_right_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "ID_CARD": {"type": "String", "comment": "证件号码"}, "NAME": {"type": "String", "comment": "名称"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "RIGHT_TYPE": {"type": "String", "comment": "权利人类型(法人、自然人)"}}}, "g4_house_rent_alr_deal_proj_asset_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ACRE_CERT_NO": {"type": "String", "comment": "地产证号"}, "AREA": {"type": "String", "comment": "建筑面积/土地面积（平方米）"}, "ASSET_CGY": {"type": "String", "comment": "资产类别"}, "BUSI": {"type": "String", "comment": "商圈"}, "CERT_NO": {"type": "String", "comment": "房屋证号/土地证号"}, "CITY": {"type": "String", "comment": "市"}, "CMPLT_CERT_NO": {"type": "String", "comment": "竣工证号"}, "CNSTR_CERT_NO": {"type": "String", "comment": "施工证号"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "ESTATE_WARR_NO": {"type": "String", "comment": "不动产权证号"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "FITMT_LEVEL_AUX_EQUIP": {"type": "String", "comment": "装修水平及附属设施"}, "HOUSE_USAGE": {"type": "String", "comment": "房屋用途"}, "HOUSE_USE_SITU": {"type": "String", "comment": "房屋使用现状"}, "PLAN_CERT_NO": {"type": "String", "comment": "规划证号"}, "POSIT": {"type": "String", "comment": "坐落位置"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROV": {"type": "String", "comment": "省"}, "REGION_COUNTY": {"type": "String", "comment": "区(县)"}, "RENT_HOUSE_USE_YEARS": {"type": "String", "comment": "出租房屋使用年限(年)"}}}, "g4_house_rent_alr_deal_proj_buyer_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ADDR": {"type": "String", "comment": "通讯地址/注册地（住所）"}, "ASSET_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "资产总计(万元)"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUYER_NAME": {"type": "String", "comment": "意向受让方名称"}, "CITY": {"type": "String", "comment": "市"}, "CONTACT_TEL": {"type": "String", "comment": "联系电话"}, "CORP_SCALE": {"type": "String", "comment": "企业规模"}, "CORP_TYPE": {"type": "String", "comment": "公司类型/经济类型"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "EMAIL": {"type": "String", "comment": "电子邮箱"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "FAX": {"type": "String", "comment": "传真"}, "INDUSTRY": {"type": "String", "comment": "所属行业"}, "INDUSTRY_TYPE": {"type": "String", "comment": "所属行业类型"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "IS_PRIOR_RENTW": {"type": "String", "comment": "是否有优先承租权"}, "IS_UNITE_BUY": {"type": "String", "comment": "是否联合受让"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "LIAB_SUM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "负债总计(万元)"}, "OTHER_PROMIS": {"type": "String", "comment": "其他承诺"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROV": {"type": "String", "comment": "省"}, "REGION_COUNTY": {"type": "String", "comment": "区(县)"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本（万元）"}, "REG_CAP_CURRENCY": {"type": "String", "comment": "注册资本币种"}, "RENT_APPLY_PRICE": {"type": "String", "comment": "承租意向价格"}, "RENT_AREA_USAGE": {"type": "String", "comment": "承租面积及用途"}, "TYPE_MAN": {"type": "String", "comment": "类型（法人、自然人）"}, "UNITE_NAME": {"type": "String", "comment": "联合体名称"}}}, "g4_house_rent_alr_deal_proj_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"APRV_NO": {"type": "String", "comment": "批准文号"}, "AST_CGY": {"type": "String", "comment": "资产类别"}, "AST_SRC": {"type": "String", "comment": "资产来源"}, "AUTHORIZE_UNIT": {"type": "String", "comment": "批准单位"}, "BLNG_GRP": {"type": "String", "comment": "所属集团"}, "BRKR_MBSH_ID": {"type": "String", "comment": "经纪会员编号"}, "BRKR_MBSH_NM": {"type": "String", "comment": "经纪会员名称"}, "BUYER_CNT": {"type": "Nullable(Decimal(10, 0))", "comment": "意向受让方个数（全部）"}, "BUYER_CNT_APY_DEPOSIT": {"type": "Nullable(Decimal(10, 0))", "comment": "意向受让方个数（已缴纳保证金）"}, "CHANGE_EXCHANGE_TYPE_RSN": {"type": "String", "comment": "更改交易方式原因"}, "CHRG_TO_COST_SP_CMNT": {"type": "String", "comment": "承担费用补充说明"}, "CITY": {"type": "String", "comment": "市"}, "CNTR_EXPY_DT": {"type": "String", "comment": "合同失效日期"}, "CNTR_SIGN_DT": {"type": "String", "comment": "合同签订日期"}, "CNTY_AND_DSTC": {"type": "String", "comment": "区县"}, "CTC_PSN": {"type": "String", "comment": "联系人"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DATE_EXPIRED": {"type": "String", "comment": "到期日"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "DEAL_RENT_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "成交租金价格（元）"}, "DEAL_RENT_TOTAL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交租金总价(万元)"}, "DEAL_WAY": {"type": "String", "comment": "成交方式（实际交易方式）"}, "DEPOSIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "保证金金额（万元）"}, "DEPOSIT_DISPL_MOD": {"type": "String", "comment": "保证金处置方式"}, "DEPOSIT_TK_OFF_PRVN": {"type": "String", "comment": "保证金扣除条款"}, "DS_MK_STTN": {"type": "String", "comment": "决策情况"}, "ECN_CHAR": {"type": "String", "comment": "经济性质"}, "EMAIL": {"type": "String", "comment": "电子邮件"}, "ESR_END_DT": {"type": "String", "comment": "披露结束日期"}, "ESR_NOTICE_TERM": {"type": "Nullable(Decimal(10, 0))", "comment": "披露公告期（工作日）"}, "ESR_STRT_DT": {"type": "String", "comment": "披露开始日期"}, "EST_IS_ONET_ESR": {"type": "String", "comment": "估价是否外网披露"}, "EST_UNIT": {"type": "String", "comment": "估价单位"}, "EST_UNIT_NM": {"type": "String", "comment": "估价单位名称"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "HS_PTY_USE_USE_RQS": {"type": "String", "comment": "房产使用用途要求"}, "INDUSTRY": {"type": "String", "comment": "所属行业"}, "INDUSTRY_TP": {"type": "String", "comment": "所属行业类型"}, "INIT_YEAR_AVG_RNT_FEE_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "原年平均租金价格（万元/年）"}, "INTND_COLL_RENT_MOD": {"type": "String", "comment": "拟征集承租方方式"}, "INTND_COLL_RENT_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "拟征集承租方个数"}, "IS_EXST_PRTY_RENTW": {"type": "String", "comment": "是否存在优先承租权"}, "IS_NAL_HS_RENT": {"type": "String", "comment": "是否国有房屋出租"}, "IS_PRMT_DCRT_RFM": {"type": "String", "comment": "是否允许装修改造"}, "IS_PY_DEPOSIT": {"type": "String", "comment": "是否缴纳保证金"}, "LESSOR_NM": {"type": "String", "comment": "出租方名称"}, "LESSOR_TP": {"type": "String", "comment": "出租方类型"}, "MAX_INTND_COLL_RENT_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "最多拟征集承租方个数"}, "MBSH_CTC_PSN": {"type": "String", "comment": "会员联系人"}, "MDL_RNT_FEE_PRC_UNIT": {"type": "String", "comment": "成交租金价格单位"}, "MIN_INTND_COLL_RENT_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "最少拟征集承租方个数"}, "MSG_ADDR": {"type": "String", "comment": "通讯地址"}, "NOT_COLL_INTNT_RENT_OPT": {"type": "String", "comment": "未征集到意向承租方选项"}, "NTW_BID_TP": {"type": "String", "comment": "网络竞价类型"}, "OTHR_ESR_ITM": {"type": "String", "comment": "其他披露事项"}, "OTHR_RGHT_STTN": {"type": "String", "comment": "其他权利情况"}, "PHONE": {"type": "String", "comment": "联系手机号"}, "PICK_MOD": {"type": "String", "comment": "遴选方式"}, "PICK_SCM_MCONTENT": {"type": "String", "comment": "遴选方案主要内容"}, "PREP_RENT_AREA_SQM": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "拟出租面积(平方米)"}, "PRJ_BLNG_DEPT": {"type": "String", "comment": "项目所属部门"}, "PRJ_PNP": {"type": "String", "comment": "项目负责人"}, "PRJ_PRC_UNIT": {"type": "String", "comment": "挂牌价单位"}, "PRJ_STS": {"type": "String", "comment": "项目状态"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROV": {"type": "String", "comment": "省"}, "PY_TM": {"type": "String", "comment": "缴纳时间"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本（万元）"}, "RENT_AREA": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "出租面积(平方米)"}, "RENT_BEAR_COST": {"type": "String", "comment": "承租方需承担费用"}, "RENT_NM": {"type": "String", "comment": "承租方名称"}, "RENT_POSTULATE": {"type": "String", "comment": "承租方资格条件"}, "RENT_PRJ_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "租金挂牌价(元)"}, "RENT_RELA_OTHER_COND": {"type": "String", "comment": "与出租相关的其他条件"}, "RGST_ADDR": {"type": "String", "comment": "注册地（住所）"}, "RNT_FEE_EST": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "租金估价"}, "RNT_FEE_PRJ_PRC_RMRK": {"type": "String", "comment": "租金挂牌价备注"}, "RNT_FEE_PY_RQS": {"type": "String", "comment": "租金支付要求"}, "RNT_FEE_SICH_PAY_QUEST": {"type": "String", "comment": "租金及押金支付要求"}, "STAR_RENT_DAY": {"type": "String", "comment": "起租日"}, "TXN_ORG_CHK_OPIN": {"type": "String", "comment": "交易机构审核意见"}, "UNIT_CHAR": {"type": "String", "comment": "单位性质"}}}, "g4_hs_rent_alr_deal_prj_sp_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"BELONG_GROUP": {"type": "String", "comment": "所属集团"}, "BELONG_REGION_COUNTY": {"type": "String", "comment": "所属区县"}, "COLL_APPLY_RENT_CNT": {"type": "Nullable(Decimal(10, 0))", "comment": "征集到意向承租方数量"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DCLO_BEGIN_DT": {"type": "String", "comment": "披露开始日期"}, "DCLO_END_DT": {"type": "String", "comment": "披露结束日期"}, "DEAL_PRICE": {"type": "String", "comment": "成交价格（元）"}, "DEAL_TOTAL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "成交总价（元）"}, "DEAL_YEAR_AVG_RENT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "成交年平均租金（元）"}, "ESTIM_CORP": {"type": "String", "comment": "估价单位"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EVALUATION_PRICE": {"type": "String", "comment": "评估价格（元）"}, "EVALU_INCRE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "评估增值（元）"}, "EVALU_TOTAL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "评估总价（元）"}, "FINAL_RENT": {"type": "String", "comment": "最终承租方"}, "HOUSE_AREA": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "房屋面积（平方米）"}, "HOUSE_POSIT": {"type": "String", "comment": "房屋位置"}, "HOUSE_USAGE": {"type": "String", "comment": "房屋用途"}, "HOUSE_USE_SITU": {"type": "String", "comment": "房屋使用现状"}, "INIT_RENT_PRICE": {"type": "String", "comment": "原出租价格(元)"}, "INIT_YEAR_AVG_RENT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "原年平均租金（元/年）"}, "IS_PRIOR_RENT": {"type": "String", "comment": "是否优先承租"}, "PICK_WAY": {"type": "String", "comment": "遴选方式"}, "PRJ_TOTAL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "挂牌总价（元）"}, "PRJ_YEAR_AVG_RENT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "挂牌年平均租金（元/年）"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROJECT_PRICE": {"type": "String", "comment": "挂牌价格（元）"}, "PROJECT_PRICE_M": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "挂牌价格（元/月）"}, "PROJECT_PRICE_Y": {"type": "String", "comment": "挂牌价格(元))"}, "PROJ_BELONG_DEPT": {"type": "String", "comment": "项目所属部门"}, "PROJ_PRINC": {"type": "String", "comment": "项目负责人"}, "RENT_NAME": {"type": "String", "comment": "出租方名称"}, "RENT_TERM": {"type": "String", "comment": "租期（年）"}, "STATUS": {"type": "String", "comment": "状态"}, "TOTAL_PRICE_INCRE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "总价增值（元）"}, "YEAR_AVG_RENT_INCRE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "年平均租金增值"}}}, "g6_lauc_alr_deal_proj_buyer_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ADDR": {"type": "String", "comment": "详细地址"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUYER_NAME": {"type": "String", "comment": "意向受让方名称"}, "CITY": {"type": "String", "comment": "市"}, "CONTACT_TEL": {"type": "String", "comment": "联系电话"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "EMAIL": {"type": "String", "comment": "电子邮件"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "IS_RIGHT": {"type": "String", "comment": "是否权利人"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "OBJECT_NAME": {"type": "String", "comment": "标的名称"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROV": {"type": "String", "comment": "省"}, "REGION": {"type": "String", "comment": "区"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本（万元）%"}, "TYPE": {"type": "String", "comment": "类型"}}}, "g6_lauc_alr_deal_proj_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"AUC_STAGE": {"type": "String", "comment": "拍卖阶段"}, "AUC_STATUS": {"type": "String", "comment": "拍卖状态"}, "AUC_TERM": {"type": "Nullable(Decimal(16, 0))", "comment": "拍卖期/变卖期（小时）"}, "AUC_WAY": {"type": "String", "comment": "拍卖方式"}, "BELONG_COURT": {"type": "String", "comment": "所属法院"}, "BID_CNT": {"type": "Nullable(Decimal(10, 0))", "comment": "报价次数"}, "BID_STATUS": {"type": "String", "comment": "竞价状态"}, "CASE_NO": {"type": "String", "comment": "案号"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "DEAL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "成交价格（元）"}, "DEAL_TIME": {"type": "String", "comment": "成交时间"}, "DELAY_CNT": {"type": "Nullable(Decimal(10, 0))", "comment": "延时次数"}, "DEPOSIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "保证金"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EVALUATION_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "评估价（元）"}, "HIGT_BID": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "最高报价"}, "NOTICE_BEGIN_TM": {"type": "String", "comment": "公告发布开始时间"}, "OBJECT_ADDR_DETAIL": {"type": "String", "comment": "标的地址详情"}, "OBJECT_BEGIN_TM": {"type": "String", "comment": "标的开始时间"}, "OBJECT_BID_END_TM": {"type": "String", "comment": "标的竞价结束时间"}, "OBJECT_NAME": {"type": "String", "comment": "标的名称"}, "OBJECT_NO": {"type": "String", "comment": "标的编号"}, "OBJECT_SITE": {"type": "String", "comment": "标的所在地"}, "PREM_RATE": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "溢价率%"}, "PRIOR_BID_CNT": {"type": "Nullable(Decimal(16, 0))", "comment": "优先竞买人数"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJ_MGR": {"type": "String", "comment": "项目经理"}, "RGST_CNT": {"type": "Nullable(Decimal(16, 0))", "comment": "报名人数"}, "START_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "起拍价(元)"}, "SYS_PROJECT_ID": {"type": "String", "comment": "系统项目ID"}}}, "g7_lasset_alr_deal_proj_buyer_info": {"comment": "", "update_time": "2025-02-24 09:39:11", "source": "clickhouse", "columns": {"ADDR": {"type": "String", "comment": "详细地址"}, "BUSINESS_SCOPE": {"type": "String", "comment": "经营范围"}, "BUYER_NAME": {"type": "String", "comment": "意向受让方名称"}, "CITY": {"type": "String", "comment": "市"}, "CONTACT_TEL": {"type": "String", "comment": "联系电话"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "EMAIL": {"type": "String", "comment": "电子邮件"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "IS_DEPOSIT": {"type": "String", "comment": "是否交纳保证金"}, "IS_RIGHT": {"type": "String", "comment": "是否权利人"}, "KHH_YXF": {"type": "String", "comment": "KHH_YXF"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "OBJECT_NAME": {"type": "String", "comment": "标的名称"}, "OBJECT_NO": {"type": "String", "comment": "标的编号"}, "PROV": {"type": "String", "comment": "省"}, "REGION": {"type": "String", "comment": "区"}, "REG_CAPITAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "注册资本(万元)"}, "TYPE": {"type": "String", "comment": "类型"}}}, "g7_lasset_alr_deal_proj_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"ASST_INPTER_TYPE": {"type": "String", "comment": "辅助录入方类型"}, "AUC_TYPE": {"type": "String", "comment": "拍卖类型"}, "BIDDER_QUALF_QUEST": {"type": "String", "comment": "竞买人资质要求"}, "BID_BEGIN_DT": {"type": "String", "comment": "竞价开始日期"}, "BID_BEGIN_TM": {"type": "String", "comment": "竞价开始时间"}, "BID_CUTT": {"type": "Nullable(Decimal(16, 0))", "comment": "竞价场次"}, "BID_PERIOD": {"type": "Nullable(Decimal(16, 0))", "comment": "竞价周期"}, "BID_PERIOD_CORP": {"type": "String", "comment": "竞价周期单位"}, "BID_SCSS_CFMTION_IS_PBC": {"type": "String", "comment": "竞价成功确认书是否公示"}, "BID_STATUS": {"type": "String", "comment": "竞价状态"}, "BKRPT_CORP_RGST_CITY": {"type": "String", "comment": "破产企业注册地（市）"}, "BKRPT_CORP_RGST_PROV": {"type": "String", "comment": "破产企业注册地（省）"}, "BKRPT_CORP_RGST_REGION_COUNTY": {"type": "String", "comment": "破产企业注册地（区县）"}, "BKRPT_PRGM": {"type": "String", "comment": "破产程序"}, "BUYER_CHARGE_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "买受人收费金额（元）"}, "BUYER_CHARGE_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "买受人收费比例（%）"}, "BUYER_CHARGE_WAY": {"type": "String", "comment": "买受人收费方式"}, "CASE_NO": {"type": "String", "comment": "案件号"}, "CFM_DT": {"type": "String", "comment": "确认日期"}, "CFM_TM": {"type": "String", "comment": "确认时间"}, "CLMT": {"type": "String", "comment": "认领人"}, "CLM_DEPT": {"type": "String", "comment": "认领部门"}, "CLM_TM": {"type": "String", "comment": "认领时间"}, "CMSN": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "手续费"}, "CONTACT_WAY": {"type": "String", "comment": "联系方式"}, "CURR": {"type": "String", "comment": "币种"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEALP_CONTACT_WAY": {"type": "String", "comment": "成交人联系方式"}, "DEALP_NAME": {"type": "String", "comment": "成交人姓名"}, "DEAL_DATE": {"type": "String", "comment": "成交日期"}, "DEAL_ID": {"type": "String", "comment": "成交编号"}, "DEAL_NUM": {"type": "Nullable(Decimal(10, 0))", "comment": "成交数量(手)"}, "DEAL_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "成交价格"}, "DEAL_STATUS": {"type": "String", "comment": "成交状态"}, "DEAL_SVC_PVDR": {"type": "String", "comment": "交易服务商"}, "DEAL_TIME": {"type": "String", "comment": "成交时间"}, "DEAL_VALUE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "成交金额"}, "DELAY_PERIOD": {"type": "Nullable(Decimal(16, 0))", "comment": "延时周期（秒）"}, "DELIVE_DT": {"type": "String", "comment": "交割日期"}, "DELIVE_TM": {"type": "String", "comment": "交割时间"}, "DEPOSIT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "保证金(元)"}, "DEPOSIT_CMPST_SERV_FEE": {"type": "String", "comment": "是否保证金抵扣服务费"}, "DEPOSIT_IS_TPRICE": {"type": "String", "comment": "保证金是否转价款"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EVALUATION_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "评估价(元)"}, "EXCH_CC_PCT_BUYER": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "交易所分佣比例_买受人（%）"}, "EXCH_CC_PCT_TRSTR": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "交易所分佣比例_委托方（%）"}, "INCRE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "加价幅度(元)"}, "INTER_IS_DCLO_TRSTR": {"type": "String", "comment": "外网是否披露委托方（处置单位）"}, "IS_ALLOW_START_PRICE_BID": {"type": "String", "comment": "是否允许以起拍价报价"}, "IS_CHARGE": {"type": "String", "comment": "是否收费"}, "IS_MKT_PRICE": {"type": "String", "comment": "是否有市场价"}, "IS_QUOTA": {"type": "String", "comment": "是否限购"}, "IS_RESERVE_PRICE": {"type": "String", "comment": "是否有保留价"}, "IS_SPRT_LOAN": {"type": "String", "comment": "是否支持贷款"}, "IS_SYS_COLL_CMSN": {"type": "String", "comment": "是否系统收取手续费"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "MAP": {"type": "String", "comment": "地图"}, "MKT_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "市场价（元）"}, "NOTICE_DETAIL": {"type": "String", "comment": "公告详情"}, "NOTICE_STATUS": {"type": "String", "comment": "公告状态"}, "NOTICE_TM": {"type": "String", "comment": "公告发布时间"}, "NOTICE_TM_OPTION": {"type": "String", "comment": "公告发布时间选项"}, "NOTICE_TTL": {"type": "String", "comment": "公告标题"}, "NOTICE_TYPE": {"type": "String", "comment": "公告类型"}, "OBJECT_CLASS": {"type": "String", "comment": "标的物分类"}, "OBJECT_DETAIL": {"type": "String", "comment": "标的物详情"}, "OBJECT_INFO": {"type": "String", "comment": "标的物信息"}, "OBJECT_NAME": {"type": "String", "comment": "标的物名称"}, "OBJECT_NO": {"type": "String", "comment": "标的物编号"}, "OBJECT_POSIT": {"type": "String", "comment": "标的物位置"}, "OBJECT_SERV_FEE_SETTLEMENT": {"type": "String", "comment": "标的服务费结算方式"}, "OBJECT_SITE_CITY": {"type": "String", "comment": "标的物所在地（市）"}, "OBJECT_SITE_PROV": {"type": "String", "comment": "标的物所在地（省）"}, "OBJECT_SITE_REGION_COUNTY": {"type": "String", "comment": "标的物所在地（区县）"}, "OBJECT_STATUS": {"type": "String", "comment": "标的物状态"}, "ORDER_CGY": {"type": "String", "comment": "委托类别"}, "OTHER_COMNT": {"type": "String", "comment": "其他说明"}, "PRIORITY": {"type": "String", "comment": "有无优先购买权人"}, "PROJECT_TYPE": {"type": "String", "comment": "项目类型"}, "PROJ_PRINC": {"type": "String", "comment": "项目负责人"}, "REMAIN_IS_ENTRY": {"type": "String", "comment": "尾款是否入场"}, "RESERVE_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "保留价(元)"}, "RGSTD_CNT": {"type": "Nullable(Decimal(16, 0))", "comment": "已报名人数"}, "SELL_PERIOD": {"type": "Nullable(Decimal(6, 0))", "comment": "变卖周期"}, "SELL_PERIOD_CORP": {"type": "String", "comment": "变卖周期单位"}, "SELL_PREPY": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "变卖预缴款（元）"}, "SERV_MEM_CC_PCT_BUYER": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "服务会员分佣比例_买受人（%）"}, "SERV_MEM_CC_PCT_TRSTR": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "服务会员分佣比例_委托方（%）"}, "SPVS_CORP": {"type": "String", "comment": "监督单位"}, "SPVS_TEL": {"type": "String", "comment": "监督电话"}, "START_PRICE": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "起拍价/变卖价（元）"}, "TRSTR_CHARGE_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 2))", "comment": "委托方收费金额（元）"}, "TRSTR_CHARGE_RATIO": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "委托方收费比例（%）"}, "TRSTR_CHARGE_WAY": {"type": "String", "comment": "委托方收费方式"}, "TRSTR_DISP_CORP": {"type": "String", "comment": "委托方（处置单位）"}, "UDT_TM": {"type": "String", "comment": "更新时间"}, "VD_ADDR": {"type": "String", "comment": "视频地址"}}}, "gc_fend_rgst_user_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"ADDR": {"type": "String", "comment": "详细地址"}, "CONTACT_TEL": {"type": "String", "comment": "联系电话"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "FULLNAME": {"type": "String", "comment": "全称"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "LOCAL_CITY": {"type": "String", "comment": "所在城市"}, "LOCAL_POPE": {"type": "String", "comment": "所在辖区"}, "LOCAL_PROV": {"type": "String", "comment": "所在省"}, "USER_TYPE": {"type": "String", "comment": "用户类型"}}}, "gc_mbsh_dept_sm_bsn_org_prj_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"AGENT_MEM_CONTACT_TEL": {"type": "String", "comment": "代理会员联系电话"}, "AGENT_MEM_LEGAL_CONTACT": {"type": "String", "comment": "代理会员联系人"}, "AGENT_MEM_NAME": {"type": "String", "comment": "代理会员名称"}, "BELONG_GROUP_DEPT_NAME": {"type": "String", "comment": "所属集团或主管部门名称"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "ECONOMY_TYPE": {"type": "String", "comment": "经济类型"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EXCHANGE_PICK_WAY": {"type": "String", "comment": "交易方式/遴选方式"}, "EXCH_NAME": {"type": "String", "comment": "交易所名称"}, "INFO_DCLO_BEGIN_DT": {"type": "String", "comment": "信息披露开始日期"}, "INFO_DCLO_DURA": {"type": "String", "comment": "信息披露时长"}, "INFO_DCLO_END_DT": {"type": "String", "comment": "信息披露结束日期"}, "PRJ_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "挂牌金额(万元)"}, "PROJECT_CODE": {"type": "String", "comment": "项目编号"}, "PROJECT_NAME": {"type": "String", "comment": "项目名称"}, "PROJ_CLASS": {"type": "String", "comment": "项目分类（股权、增资）"}, "SELLER_FINCER_NAME": {"type": "String", "comment": "转让方名称/融资方名称"}}}, "gc_mem_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"CONTACT_TEL": {"type": "String", "comment": "联系电话"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "LEGAL_CONTACT": {"type": "String", "comment": "联系人"}, "MEM_NAME": {"type": "String", "comment": "会员名称"}, "MEM_NO": {"type": "String", "comment": "会员编号"}, "MEM_TYPE": {"type": "String", "comment": "会员类型"}, "PHONE": {"type": "String", "comment": "联系手机号"}}}, "m_bsn_pcs_aprv_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"APRV_PCS_ID": {"type": "String", "comment": "审批流程ID"}, "AUDITOR_BLNG_CETR_NM": {"type": "String", "comment": "审核人所属中心名称"}, "AUDITOR_BLNG_DEPT_NM": {"type": "String", "comment": "审核人所属部门名称"}, "AUDITOR_NM": {"type": "String", "comment": "审核人名称"}, "AUDIT_PASS_TM": {"type": "String", "comment": "审核通过时间"}, "AUDIT_RMRK": {"type": "String", "comment": "审核备注"}, "AUDIT_TM": {"type": "String", "comment": "审核时间"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EXEC_ACT": {"type": "String", "comment": "执行动作"}, "ITT_TM": {"type": "String", "comment": "发起时间"}, "NODE_AUDIT_DURT": {"type": "String", "comment": "节点审核时长"}, "NODE_STER": {"type": "String", "comment": "节点提交人名称"}, "PCS_APRV_TOT_DURT": {"type": "String", "comment": "流程审批总时长"}, "PCS_ARIV_TM": {"type": "String", "comment": "流程到达时间"}, "PCS_NM": {"type": "String", "comment": "流程名称"}, "PCS_NODE_NM": {"type": "String", "comment": "流程节点名称"}, "PCS_STAT_CD": {"type": "String", "comment": "流程状态"}, "PMER_BLNG_CETR_NM": {"type": "String", "comment": "发起人所属中心名称"}, "PMER_BLNG_DEPT_NM": {"type": "String", "comment": "发起人所属部门名称"}, "PMER_NM": {"type": "String", "comment": "发起人名称"}, "PRJ_BLNG_CETR": {"type": "String", "comment": "项目所属中心"}, "PRJ_BLNG_DEPT": {"type": "String", "comment": "项目所属部门"}, "PRJ_BSN_TP": {"type": "String", "comment": "项目业务类型"}, "PRJ_ID": {"type": "String", "comment": "项目编号"}, "PRJ_NM": {"type": "String", "comment": "项目名称"}, "PRJ_PRIN": {"type": "String", "comment": "项目负责人"}, "PRJ_PRIN_DEPT": {"type": "String", "comment": "项目负责人部门"}, "PRJ_STG": {"type": "String", "comment": "项目阶段"}, "PRJ_TP": {"type": "String", "comment": "项目类型"}, "PUBL_TP": {"type": "String", "comment": "披露类型"}, "SORT_NO": {"type": "String", "comment": "排序号"}, "STM_TP": {"type": "String", "comment": "系统类型"}}}, "m_cenetp_cetr_indx_sum": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"DATA_DT": {"type": "String", "comment": "日期"}, "DEPT_NM": {"type": "String", "comment": "部门名称"}, "EQTY_BID_RATE1_M": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权竞价率1(本月)"}, "EQTY_BID_RATE1_Y": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权竞价率1(本年)"}, "EQTY_BID_RATE2_M": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权竞价率2(本月)"}, "EQTY_BID_RATE2_Y": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权竞价率2(本年)"}, "EQTY_BID_RATE3_Y": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权竞价率3（本年）"}, "EQTY_OCPY_RATE_AMT_M": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权占有率_金额(本月)"}, "EQTY_OCPY_RATE_AMT_Y": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权占有率_金额(本年)"}, "EQTY_OCPY_RATE_NUM_M": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权占有率_数量(本月)"}, "EQTY_OCPY_RATE_NUM_Y": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "股权占有率_数量(本年)"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EXG_NM": {"type": "String", "comment": "交易所名称"}, "INCPTL_OCPY_RATE_NUM_M": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "增资占有率_数量(本月)"}, "INCPTL_OCPY_RATE_NUM_Y": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 6))", "comment": "增资占有率_数量(本年)"}}}, "m_lgsn_colt_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"DATA_DT": {"type": "String", "comment": "数据日期"}, "DATA_STATS_DT": {"type": "String", "comment": "数据统计日期"}, "DIM_NM": {"type": "String", "comment": "维度名称"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "INDX_NM": {"type": "String", "comment": "指标名称"}, "INDX_NO": {"type": "String", "comment": "指标编号"}, "INDX_UNIT": {"type": "String", "comment": "指标单位"}, "INDX_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 8))", "comment": "指标值"}}}, "m_lgsn_itrt_prt_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"AST_SRC_TP": {"type": "String", "comment": "资产来源类型"}, "CUST_NM": {"type": "String", "comment": "客户名称"}, "CUST_NO": {"type": "String", "comment": "客户编号"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_YEAR": {"type": "String", "comment": "成交年份"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "MBER_CUST_NM": {"type": "String", "comment": "机构会员名称"}, "MBER_WRD": {"type": "String", "comment": "机构会员关键字"}, "PRJ_BSN_TP_CD": {"type": "String", "comment": "项目业务类型代码"}, "PRJ_BSN_TP_CD_DSC": {"type": "String", "comment": "项目业务类型代码描述"}, "PRJ_ID": {"type": "String", "comment": "项目编号"}, "PRJ_NM": {"type": "String", "comment": "项目名称"}, "PRJ_PRT_NM": {"type": "String", "comment": "项目方名称"}, "PRJ_PRT_WRD": {"type": "String", "comment": "项目方关键字"}, "REGT_STAT_CD": {"type": "String", "comment": "报名状态代码"}, "REGT_STAT_CD_DSC": {"type": "String", "comment": "报名状态代码描述"}}}, "m_lgsn_prj_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"AST_CGY": {"type": "String", "comment": "资产类别"}, "AST_SRC_TP": {"type": "String", "comment": "资产来源类型"}, "BOND_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "债权金额"}, "CENT_ENTP_PRJ_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 8))", "comment": "中央企业项目比例"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EVAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "评估值"}, "IS_CENT_ENTP_PRJ": {"type": "String", "comment": "是否中央企业项目"}, "JUDL_LITGN_CGY": {"type": "String", "comment": "司法/诉讼类别"}, "LIT_AMT": {"type": "Decimal(24, 6)", "comment": "挂牌金额"}, "LIT_STAR_DT": {"type": "String", "comment": "挂牌开始日期"}, "MBER_CUST_NM": {"type": "String", "comment": "机构会员名称"}, "MBER_WRD": {"type": "String", "comment": "机构会员关键字"}, "NETAST_ASES_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "净资产/评估价值"}, "OCPY_AF_INCPTL_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 8))", "comment": "占增资后比例"}, "PLAN_LEASE_AREA": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "拟出租面积"}, "PRJ_BSN_TP_CD": {"type": "String", "comment": "项目业务类型代码"}, "PRJ_BSN_TP_CD_DSC": {"type": "String", "comment": "项目业务类型代码描述"}, "PRJ_ID": {"type": "String", "comment": "项目编号"}, "PRJ_NM": {"type": "String", "comment": "项目名称"}, "PRJ_PRT_LCT_PROV_CITY": {"type": "String", "comment": "项目方所在省市"}, "PRJ_PRT_NM": {"type": "String", "comment": "项目方名称"}, "PRJ_PRT_WRD": {"type": "String", "comment": "项目方关键字"}, "PRJ_STAT_CD": {"type": "String", "comment": "项目状态代码"}, "PRJ_STAT_CD_DSC": {"type": "String", "comment": "项目状态代码描述"}, "PRJ_TP_CD": {"type": "String", "comment": "项目类型代码"}, "PRJ_TP_CD_DSC": {"type": "String", "comment": "项目类型代码描述"}, "RENT_LIT_TOT_PRC": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "租金挂牌总价"}, "TXN_STAT_CD": {"type": "String", "comment": "交易状态代码"}, "TXN_STAT_CD_DSC": {"type": "String", "comment": "交易状态代码描述"}}}, "m_prj_deal_info": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"AST_SRC_TP": {"type": "String", "comment": "资产来源类型"}, "CENT_ENTP_PRJ_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 8))", "comment": "中央企业项目比例"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEAL_AMT": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交金额"}, "DEAL_DT": {"type": "String", "comment": "成交日期"}, "DEAL_RENT_TOT_PRC": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "成交租金总价"}, "ENTP_TP": {"type": "String", "comment": "企业类型"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "IS_BAKPY_AST_DISPL": {"type": "String", "comment": "是否破产资产处置"}, "IS_CENT_ENTP_PRJ": {"type": "String", "comment": "是否中央企业项目"}, "IS_CIVIL_COURT_AST_DISPL": {"type": "String", "comment": "是否民事法院资产处置"}, "IS_MILTY_COURT_AST_DISPL": {"type": "String", "comment": "是否军事法院资产处置"}, "JUDL_LITGN_CGY": {"type": "String", "comment": "司法/诉讼类别"}, "LEASE_AREA": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "出租面积"}, "MBER_CUST_NM": {"type": "String", "comment": "机构会员名称"}, "MBER_WRD": {"type": "String", "comment": "机构会员关键字"}, "OCPY_AF_INCPTL_PCT": {"type": "Nullable(<PERSON><PERSON><PERSON>(15, 8))", "comment": "占增资后比例"}, "PRJ_BSN_TP_CD": {"type": "String", "comment": "项目业务类型代码"}, "PRJ_BSN_TP_CD_DSC": {"type": "String", "comment": "项目业务类型代码描述"}, "PRJ_ID": {"type": "String", "comment": "项目编号"}, "PRJ_NM": {"type": "String", "comment": "项目名称"}, "PRJ_PRT_NM": {"type": "String", "comment": "项目方名称"}, "PRJ_PRT_WRD": {"type": "String", "comment": "项目方关键字"}, "PRJ_STAT_CD": {"type": "String", "comment": "项目状态代码"}, "PRJ_STAT_CD_DSC": {"type": "String", "comment": "项目状态代码描述"}, "PRJ_TP_CD": {"type": "String", "comment": "项目类型代码"}, "PRJ_TP_CD_DSC": {"type": "String", "comment": "项目类型代码描述"}, "TXN_MTH_CD": {"type": "String", "comment": "交易方式代码"}, "TXN_MTH_CD_DSC": {"type": "String", "comment": "交易方式代码描述"}}}, "m_txn_org_incptl_prj_lit_stats": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"BLNG_GRP": {"type": "String", "comment": "所属集团"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DEPT": {"type": "String", "comment": "部门"}, "ETL_LOAD_DT": {"type": "String", "comment": "ETL载入日期"}, "EXG_NM": {"type": "String", "comment": "交易所名称"}, "ID": {"type": "String", "comment": "序号"}, "MON": {"type": "String", "comment": "月份"}, "PLAN_RAISE_CPTL": {"type": "String", "comment": "拟募集资金（万元）"}, "PRJ_ID": {"type": "String", "comment": "项目编号"}, "PRJ_NM": {"type": "String", "comment": "项目名称"}, "PRJ_PRIN": {"type": "String", "comment": "项目负责人"}, "TRUSTEE": {"type": "String", "comment": "受托机构"}}}, "std_bjhl_ttpglb": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"acc_nm": {"type": "String", "comment": "账户名称"}, "bnk_card_no": {"type": "String", "comment": "银行卡号"}, "bnk_cd": {"type": "String", "comment": "联行号"}, "bnk_nm": {"type": "String", "comment": "银行名称"}, "bsn_tp": {"type": "Decimal(12, 0)", "comment": "业务类型"}, "cptl_lo": {"type": "String", "comment": "资金位置"}, "cust_nm": {"type": "String", "comment": "客户名称"}, "dt": {"type": "String", "comment": ""}, "pcsg_sts": {"type": "Decimal(12, 0)", "comment": "处理状态"}, "py_mod": {"type": "Decimal(12, 0)", "comment": "支付方式"}, "re_wd_ordr_no": {"type": "String", "comment": "重新出金订单号"}, "re_wd_py_no": {"type": "String", "comment": "重新出金支付流水号"}, "ret_tckt_amt": {"type": "Decimal(16, 2)", "comment": "退票金额"}, "ret_tckt_cust": {"type": "String", "comment": "退票客户"}, "ret_tckt_dt": {"type": "Decimal(8, 0)", "comment": "退票日期"}, "ret_tckt_ordr_no": {"type": "String", "comment": "退票订单号"}, "ret_tckt_py_jrnl_no": {"type": "String", "comment": "退票支付流水号"}, "ret_tckt_rsn": {"type": "String", "comment": "退票原因"}, "ret_tckt_tm": {"type": "String", "comment": "退票时间"}, "subbr_nm": {"type": "String", "comment": "支行名称"}, "xgfj": {"type": "String", "comment": ""}, "zdjsyh": {"type": "String", "comment": ""}}}, "z_indx_fct_d": {"comment": "", "update_time": "2025-02-24 09:39:12", "source": "clickhouse", "columns": {"COTP_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "去年同期值"}, "CRT_DT": {"type": "String", "comment": "创建日期"}, "DATA_DT": {"type": "String", "comment": "数据日期"}, "DATA_PRD": {"type": "String", "comment": "数据周期"}, "DIM_NM_1": {"type": "String", "comment": "维度名称1"}, "DIM_NM_2": {"type": "String", "comment": "维度名称2"}, "DIM_NO_1": {"type": "String", "comment": "维度编号1"}, "DIM_NO_2": {"type": "String", "comment": "维度编号2"}, "DIM_VAL_1": {"type": "String", "comment": "维度值1"}, "DIM_VAL_2": {"type": "String", "comment": "维度值2"}, "DIM_VAL_NM_1": {"type": "String", "comment": "维度值名称1"}, "DIM_VAL_NM_2": {"type": "String", "comment": "维度值名称2"}, "INDX_CRN_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "指标本期值"}, "INDX_NO": {"type": "String", "comment": "指标编号"}, "INDX_UNIT": {"type": "String", "comment": "指标单位"}, "INTL_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "年初值"}, "IS_COLT": {"type": "String", "comment": "是否手工补录"}, "MOD_DT": {"type": "String", "comment": "更新日期"}, "ORG_CD": {"type": "String", "comment": "机构代码"}, "PRED_VAL": {"type": "Nullable(<PERSON><PERSON><PERSON>(24, 6))", "comment": "上期值"}}}}}