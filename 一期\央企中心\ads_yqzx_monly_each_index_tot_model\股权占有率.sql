WITH base_data AS (
    SELECT
        DISTINCT
        x.info_publ_start_dt, --挂牌日期
        x.Blng_Org AS blng_grp, -- 所属集团
        x.prj_blng_dept_nm AS proj_belong_dept_name, --所属部门
        x.exch, --交易所
        COALESCE(x.Lit_Amt, 0) / 10000 AS ht_amt, --挂牌金额（万元）
        SUBSTR(x.info_publ_start_dt, 1, 4) AS year, -- 挂牌年份
        SUBSTR(x.info_publ_start_dt, 6, 2) AS month, -- 挂牌月份
        x.prj_no, -- 项目编号
        x.prj_prt_sown_spvs_org_cd_dsc -- 项目监管机构
    FROM 
        dws.dws_all_trsfer_info x
    WHERE 
        x.prj_bsn_tp_cd_dsc = '产权转让'
      AND dt='${dmp_day}'
      and info_publ_start_dt is not null and prj_blng_dept_nm is not null  and exch is not null
      and is_repeat='否'
      AND (x.prj_prt_sown_spvs_org_cd_dsc not IN ('省级财政部门监管','市级财政部门或金融办监管','省级国资委监管','省级其他部门监管','市级国资委监管','市级其他部门监管') 
           OR (x.exch = '北交所' AND x.prj_prt_sown_spvs_org_cd_dsc is null))
      AND COALESCE(x.blng_org,'未知') NOT IN (
            SELECT CTY_CONTRI_CORP_LEAD_DEPT FROM ods.ods_bl_yqzx_fin_proj_list where dt='${dmp_day}'
        )
  and x.prj_no not like 'G0%'
  and x.prj_blng_dept_nm in ('央企一部','央企二部','央企三部','央企四部','央企五部')
),
date_data AS (
    -- 从日期维表中选择所需的月份
    SELECT DISTINCT
        SUBSTR(date_id, 1, 4) AS year,
        SUBSTR(date_id, 5, 2) AS month,
        year_month
    FROM dim.dim_date
    WHERE year_month >= '201901'
  AND year_month <= DATE_FORMAT(CURRENT_DATE(), 'YYYYMM')
),
monthly_totals1 AS (
    SELECT 
        d.year,
        d.month,
        b.exch, -- 交易所
        b.proj_belong_dept_name, -- 所属部门
        sum(case when b.prj_no is null then 0 else 1 end) AS monthly_project_count, -- 月挂牌数量
        SUM(b.ht_amt) AS monthly_total_amount -- 月挂牌总金额
    FROM 
        (select * from date_data  cross join (select distinct exch,proj_belong_dept_name from base_data )) d
    LEFT JOIN
        base_data b
    ON 
        d.year = b.year AND d.month = b.month and d.exch=b.exch and d.proj_belong_dept_name=b.proj_belong_dept_name
    GROUP BY 
        d.year, d.month, b.exch, b.proj_belong_dept_name
),
monthly_totals as(
select 
a.year,a.month,a.exch,a.proj_belong_dept_name,b.monthly_project_count,b.monthly_total_amount
from 
(select year,month,exch,proj_belong_dept_name from date_data cross join (select distinct exch,proj_belong_dept_name from monthly_totals1)) a
left join monthly_totals1 b on a.year=b.year and a.month=b.month and a.exch=b.exch and a.proj_belong_dept_name =b.proj_belong_dept_name
),
cumulative_totals1 AS (
    SELECT 
        m.year,
        m.month,
        m.exch, -- 交易所
        m.proj_belong_dept_name,
        m.monthly_project_count,
        m.monthly_total_amount,
        SUM(m.monthly_project_count) OVER (PARTITION BY m.year, m.exch, m.proj_belong_dept_name ORDER BY m.month) AS year_cumulative_project_count, -- 年累计挂牌数量
        SUM(m.monthly_total_amount) OVER (PARTITION BY m.year, m.exch, m.proj_belong_dept_name  ORDER BY m.month) AS total_year_cumulative_amount -- 总年累计挂牌金额
    FROM 
        monthly_totals m
),
cumulative_totals as(

    SELECT 
        m.year,
        m.month,
        m.exch, -- 交易所
        m.proj_belong_dept_name,
        m.monthly_project_count,
        m.monthly_total_amount,
        m.year_cumulative_project_count, -- 年累计挂牌数量
        m.total_year_cumulative_amount -- 总年累计挂牌金额
    FROM 
        cumulative_totals1 m
union all
    SELECT 
        m.year,
        m.month,
        m.exch, -- 交易所
        '中心' as proj_belong_dept_name,
        sum(m.monthly_project_count) as monthly_project_count,
        sum(m.monthly_total_amount) as monthly_total_amount,
        sum(m.year_cumulative_project_count) as year_cumulative_project_count, -- 年累计挂牌数量
        sum(m.total_year_cumulative_amount) as total_year_cumulative_amount -- 总年累计挂牌金额
    FROM 
        cumulative_totals1 m
    group by m.year,m.month,m.exch
)
SELECT 
    CONCAT(m.year,'-',m.month) AS data_dt,
    '股权占有率（数量）' AS etl_grp_desc,
    m.exch,
    m.proj_belong_dept_name AS proj_belong_dept_name,
    CASE 
        WHEN m.monthly_project_count = 0 THEN 0
        ELSE m.monthly_project_count * 1.0 / t.total_monthly_project_count
    END AS monly_ind_val, -- 当月股权占有率
    CASE 
        WHEN m.year_cumulative_project_count = 0 THEN 0
        ELSE m.year_cumulative_project_count * 1.0 / t.total_year_cumulative_project_count
    END AS year_ind_val -- 年累计股权占有率
FROM 
    cumulative_totals m
JOIN
    (SELECT year, month, SUM(monthly_project_count) AS total_monthly_project_count, SUM(year_cumulative_project_count) AS total_year_cumulative_project_count,proj_belong_dept_name
     FROM cumulative_totals
     GROUP BY year, month,proj_belong_dept_name) t
ON 
    m.year = t.year AND m.month = t.month and m.proj_belong_dept_name=t.proj_belong_dept_name

UNION ALL

SELECT 
    CONCAT(m.year,'-',m.month) AS data_dt,
    '股权占有率（金额）' AS etl_grp_desc,
    m.exch,
    m.proj_belong_dept_name AS proj_belong_dept_name,
    CASE 
        WHEN m.monthly_total_amount = 0 THEN 0
        ELSE m.monthly_total_amount * 1.0 / t.total_monthly_amount
    END AS monly_ind_val, -- 当月挂牌金额占比
    CASE 
        WHEN m.total_year_cumulative_amount = 0 THEN 0
        ELSE m.total_year_cumulative_amount * 1.0 / t.total_year_cumulative_amount
    END AS year_ind_val -- 年累计挂牌金额占比
FROM 
    cumulative_totals m
JOIN
    (SELECT year, month, SUM(monthly_total_amount) AS total_monthly_amount, SUM(total_year_cumulative_amount) AS total_year_cumulative_amount,proj_belong_dept_name
     FROM cumulative_totals
     GROUP BY year, month,proj_belong_dept_name) t
ON 
    m.year = t.year AND m.month = t.month and m.proj_belong_dept_name=t.proj_belong_dept_name
