-- 用户维
SELECT 
'BJHL'||usr_id||'QD' AS usr_wrd, -- 用户关键字
'BJHL'||a.cust_no AS cust_wrd, --客户关键字
NULL AS blng_org_wrd,	--所属机构关键字
CAST(usr_id AS STRING) AS usr_id, --用户ID
case when b.full_nm is not null then b.full_nm else a.user_name end AS usr_nm, --用户名称
-- b.full_nm AS usr_nm2, --用户名称
org_flg AS usr_cgy_cd,	--	用户类别代码
org_flg_dsc AS usr_cgy_cd_dsc,	--	用户类别代码描述
fix_tel AS tel, --电话
mbl_ph_no AS phon, --手机
user_email AS email, --电子邮件
is_vld_email AS is_vfcd_email, -- 是否验证邮箱
NULL AS fax, --传真
addr AS addr, -- 地址
usr_sts AS usr_stat_cd,	--用户状态代码
'' AS usr_stat_cd_dsc	--用户状态代码描述[关联维度表]
FROM std.std_bjhl_t_gifax_user_d a
LEFT JOIN 
(SELECT cust_no,full_nm FROM std.std_bjhl_tkhxx_d WHERE dt = '${dmp_day}') b
ON b.cust_no = a.cust_no
WHERE a.dt = '${dmp_day}'

UNION ALL 
-- 中台用户信息
SELECT 
'BJHL'||ID||'ZT' AS usr_wrd,--	用户关键字
NULL AS cust_wrd, --	客户关键字
'' AS blng_org_wrd, --	所属机构关键字
userid AS usr_id, --	用户ID
usr_nm AS usr_nm, --	用户名称
usr_cgy AS usr_cgy_cd, --	用户类别代码
usr_cgy_dsc AS usr_cgy_cd_dsc, --	用户类别代码描述
tel AS tel, --	电话
mbl_ph AS phon, --	手机
email AS email, --	电子邮件
NULL AS is_vfcd_email, --	是否验证邮箱
fax AS fax, --	传真
NULL AS addr,--	地址
NULL AS usr_stat_cd,--	用户状态代码
NULL AS usr_stat_cd_dsc --	用户状态代码描述
FROM std.std_bjhl_tuser_d 
WHERE dt = '${dmp_day}'