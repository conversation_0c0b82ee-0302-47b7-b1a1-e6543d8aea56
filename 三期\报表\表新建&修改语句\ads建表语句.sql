-- Active: 1725412518835@@10.254.99.150@8123@ads

-- 删除市属中心各类业务成交项目统计表
DROP TABLE IF EXISTS ads.ads_sszx_bsn_mdl_prj_stat;
--市属中心各类业务成交项目统计表
CREATE TABLE ads.ads_sszx_bsn_mdl_prj_stat (
    proj_type Nullable(String) COMMENT '业务类型',
    deal_num Nullable(Decimal(24, 0)) COMMENT '成交数量',
    deal_value Nullable(Decimal(24, 6)) COMMENT '成交金额',
    deal_date String COMMENT '成交日期',
    proj_category Nullable(String) COMMENT '项目类别',
    proj_status Nullable(String) COMMENT '项目状态',
    cagetory Nullable(String) COMMENT '类别'
) ENGINE = MergeTree()
ORDER BY deal_date;

-- 删除市属中心各类业务成交项目统计表-股权明细
DROP TABLE IF EXISTS ads.ads_sszx_eqty_mdl_dtl;
-- 市属中心各类业务成交项目统计表-股权明细
CREATE TABLE ads.ads_sszx_eqty_mdl_dtl (
    proj_no String COMMENT '项目编号',
    proj_name Nullable(String) COMMENT '项目名称',
    proj_status Nullable(String) COMMENT '项目状态',
    state_funded_enterprise Nullable(String) COMMENT '国家出资企业或主管部门名称',
    trans_name Nullable(String) COMMENT '转让方名称',
    holding_percentage Nullable(Decimal(24, 6)) COMMENT '持有(产)股权比例(%)',
    ass_value_of_trans Nullable(Decimal(24, 6)) COMMENT '转让标的对应评估值(万元)',
    info_dclo_begin_dt Nullable(String) COMMENT '信息披露起始日期',
    trans_proportion Nullable(Decimal(24, 6)) COMMENT '转让比例(%)',
    trans_type Nullable(String) COMMENT '实际交易方式',
    deal_date Nullable(String) COMMENT '成交日期',
    grantee_name Nullable(String) COMMENT '受让方名称',
    grantee_economic_type Nullable(String) COMMENT '受让方经济类型',
    total_assets Nullable(Decimal(24, 6)) COMMENT '资产总计/评估价值(万元)',
    deal_price Nullable(Decimal(24, 6)) COMMENT '成交价格(万元)',
    appreciation_amt Nullable(Decimal(24, 6)) COMMENT '增值额(万元)',
    trans_price Nullable(Decimal(24, 6)) COMMENT '转让底价(万元)',
    industry Nullable(String) COMMENT '所属行业',
    state_owned_asset_regulator Nullable(String) COMMENT '国资监管机构',
    regulator_location Nullable(String) COMMENT '监管机构属地',
    approval_unit_name Nullable(String) COMMENT '批准单位名称',
    approval_authority Nullable(String) COMMENT '核准(备案)机构',
    contract_sign_date Nullable(String) COMMENT '合同签订日期',
    proj_princ_name Nullable(String) COMMENT '项目负责人',
    proj_belong_dept_name Nullable(String) COMMENT '项目所属部门',
    is_org_shareholder Nullable(String) COMMENT '是否原股东',
    trans_level Nullable(String) COMMENT '转让方层级',
    income Nullable(Decimal(24, 6)) COMMENT '北交所收(元)',
    premium_rate Nullable(Decimal(24, 6)) COMMENT '溢价率',
    proj_type Nullable(String) COMMENT '项目类别',
    deal_type Nullable(String) COMMENT '成交类型',
    is_internal_adjust Nullable(String) COMMENT '集团内调整/跨集团调整',
    debt_amount Nullable(Decimal(24, 6)) COMMENT '债务金额',
    investor_type Nullable(String) COMMENT '投资人类型',
    proj_category Nullable(String) COMMENT '混改/压减/其他',
    bond_amt Nullable(Decimal(24, 6)) COMMENT '涉及债权转让金额',
    land_use_amt Nullable(Decimal(24, 6)) COMMENT '涉及土地使用权金额',
    tech_asset_amt Nullable(Decimal(24, 6)) COMMENT '涉及技术资产金额',
    mix_own_type Nullable(String) COMMENT '混改类型',
    investor_reg Nullable(String) COMMENT '投资人地区',
    red_type Nullable(String) COMMENT '压减类型'
) ENGINE = MergeTree()
ORDER BY proj_no;

-- 删除市属中心各类业务成交项目统计表-增资明细
DROP TABLE IF EXISTS ads.ads_sszx_icap_mdl_dtl;
-- 市属中心各类业务成交项目统计表-增资明细
CREATE TABLE ads.ads_sszx_icap_mdl_dtl (
    is_municipal_ent Nullable(String) COMMENT '是否为市管企业',
    transparency Nullable(String) COMMENT '公开/非公开',
    trans_type Nullable(String) COMMENT '交易类型',
    cap_inc_purpose Nullable(String) COMMENT '增资目的',
    group_name Nullable(String) COMMENT '所属集团',
    cap_inc_company Nullable(String) COMMENT '增资企业',
    listing_cap_inc_ratio Nullable(String) COMMENT '挂牌增资比例/股数',
    proj_id String COMMENT '项目编号',
    pre_cap_inc_nature Nullable(String) COMMENT '增资前标的企业性质',
    pre_cap_inc_shareholders Nullable(String) COMMENT '增资前的股东',
    pre_municipal_state_owned_ratio Nullable(String) COMMENT '增资前全部国有股东持股比例（仅含市国资委监管国有企业股东）',
    pre_all_state_owned_ratio Nullable(String) COMMENT '增资前全部国有股东持股比例（含其他国资监管机构下属企业股东）',
    cap_inc_company_region Nullable(String) COMMENT '增资企业所在地区',
    cap_inc_company_industry Nullable(String) COMMENT '增资企业所属行业',
    listing_date Nullable(String) COMMENT '挂牌时间',
    info_end_date Nullable(String) COMMENT '信息披露期满日期',
    listing_duration Nullable(String) COMMENT '挂牌持续时间总计（工作日）',
    target_funds_amt Nullable(String) COMMENT '拟募集资金金额（万元）',
    qualified_investors_num Nullable(String) COMMENT '征集合格意向投资人个数',
    deal_method Nullable(String) COMMENT '成交方式',
    deal_date Nullable(String) COMMENT '成交日期',
    investor Nullable(String) COMMENT '投资人',
    single_investor_cap_inc_amt Nullable(Decimal(24, 6)) COMMENT '单个投资人增资金额（万元）',
    single_investor_reg_cap_amt Nullable(Decimal(24, 6)) COMMENT '单个投资人增加注册资本金额（万元）',
    investor_type Nullable(String) COMMENT '投资人类型',
    investor_region Nullable(String) COMMENT '投资人所在地区',
    investor_industry Nullable(String) COMMENT '投资人所属行业',
    investor_state_owned_regulator Nullable(String) COMMENT '投资人为其他国有企业的，其所属国资监管机构（央企、其他地方国资）',
    total_new_capital Nullable(Decimal(24, 6)) COMMENT '新增出资资本合计数',
    total_investment_funds Nullable(Decimal(24, 6)) COMMENT '投资资金总额合计数',
    assessment_baseline_date Nullable(String) COMMENT '评估基准日',
    total_assets Nullable(Decimal(24, 6)) COMMENT '资产总额（万元）',
    total_liabilities Nullable(Decimal(24, 6)) COMMENT '负债总额（万元）',
    equity Nullable(Decimal(24, 6)) COMMENT '所有者权益（万元）',
    company_assets_assessment Nullable(Decimal(24, 6)) COMMENT '企业资产总额评估值（万元）',
    target_equity_assessment Nullable(Decimal(24, 6)) COMMENT '标的企业净资产评估值（万元）',
    project_progress Nullable(String) COMMENT '项目进展',
    post_project_shareholders Nullable(String) COMMENT '项目完成后的股东',
    post_project_shareholders_ratio Nullable(String) COMMENT '项目完成后的股东持股比例',
    post_municipal_state_owned_ratio Nullable(String) COMMENT '项目完成后全部国有股东持股比例（%）（仅含市国资委监管国有企业股东）',
    post_all_state_owned_ratio Nullable(String) COMMENT '项目完成后全部国有股东持股比例（%）（含其他国资监管机构下属企业股东）',
    post_project_nature Nullable(String) COMMENT '项目完成后标的企业性质',
    reporter Nullable(String) COMMENT '填报人',
    cap_inc_plan Nullable(String) COMMENT '增资方案',
    approval_date Nullable(String) COMMENT '批准日期',
    esop Nullable(String) COMMENT '股权激励/员工持股',
    financing Nullable(String) COMMENT '融资',
    reorg Nullable(String) COMMENT '集团内部重组/股权结构调整/所出资企业直接或指定其控股、实际控制的其他子企业参与增资/债转股',
    budget Nullable(String) COMMENT '国有资本经营预算拨款',
    equity_to_capital Nullable(String) COMMENT '所有者权益转增资本',
    original_shareholder_increase Nullable(String) COMMENT '原股东增资',
    state_owned_asset_regulator Nullable(String) COMMENT '国资监管机构',
    regulator_location Nullable(String) COMMENT '监管地区',
    bond_amt Nullable(Decimal(24, 6)) COMMENT '涉及债权转让金额',
    land_use_amt Nullable(Decimal(24, 6)) COMMENT '涉及土地使用权金额',
    tech_asset_amt Nullable(Decimal(24, 6)) COMMENT '涉及技术资产金额',
    mixed_type Nullable(String) COMMENT '混改类型',
    prj_blng_dept Nullable(String) COMMENT '项目所属部门',
    audit_year Nullable(String) COMMENT '审计年度',
    pre_shr_ratio Nullable(String) COMMENT '增资前的股东持股比例',
    biz_income Nullable(Decimal(24, 6)) COMMENT '营业收入',
    biz_profit Nullable(Decimal(24, 6)) COMMENT '营业利润',
    net_profit Nullable(Decimal(24, 6)) COMMENT '净利润',
    total_assets_amt Nullable(Decimal(24, 6)) COMMENT '资产总额',
    total_liab_amt Nullable(Decimal(24, 6)) COMMENT '负债总额',
    owner_equity Nullable(Decimal(24, 6)) COMMENT '所有者权益',
    biz_income_1 Nullable(Decimal(24, 6)) COMMENT '营业收入',
    biz_profit_1 Nullable(Decimal(24, 6)) COMMENT '营业利润',
    net_profit_1 Nullable(Decimal(24, 6)) COMMENT '净利润1',
    eqty_appr_rate Nullable(Decimal(15, 6)) COMMENT '增资股权增值率',
    post_shr_ratio Nullable(String) COMMENT '项目完成后的股东持股比例',
    muni_soe_ratio_chg Nullable(String) COMMENT '全部国有股东持股比例变动情况 （仅含市国资委监管国有企业股东）',
    all_soe_ratio_chg Nullable(String) COMMENT '全部国有股东持股比例变动情况 （含其他国资监管机构下属企业股东）',
    strtg_invst_rsn Nullable(String) COMMENT '引入战略投资人/引入特殊资质股东/经同级国有资产监督管理机构批准',
    is_new_shr_cap_inc Nullable(String) COMMENT '是否涉及新股东增资',
    ent_level Nullable(String) COMMENT '企业层级'
) ENGINE = MergeTree()
ORDER BY proj_id;

-- 删除市属中心产权转让业务成交项目统计表
DROP TABLE IF EXISTS ads.ads_sszx_eqty_mdl_prj_stat;
-- 市属中心产权转让业务成交项目统计表
CREATE TABLE ads.ads_sszx_eqty_mdl_prj_stat (
    deal_num Nullable(Decimal(24, 0)) COMMENT '成交数量',
    deal_value Nullable(Decimal(24, 6)) COMMENT '成交金额',
    deal_date String COMMENT '成交日期',
    deal_type Nullable(String) COMMENT '成交类型',
    proj_category Nullable(String) COMMENT '项目类别',
    proj_status Nullable(String) COMMENT '项目状态',
    appraisal_value Nullable(Decimal(24, 6)) COMMENT '评估值',
    increment_amt Nullable(Decimal(24, 6)) COMMENT '增值金额',
    ly_deal_quantity Nullable(Decimal(24, 0)) COMMENT '去年成交数量',
    ly_deal_amount Nullable(Decimal(24, 6)) COMMENT '去年成交金额',
    is_internal_adjust Nullable(String) COMMENT '集团内/跨集团调整',
    cagetory Nullable(String) COMMENT '类别'
) ENGINE = MergeTree()
ORDER BY deal_date;

-- 删除市属中心资产转让业务成交项目统计表
DROP TABLE IF EXISTS ads.ads_sszx_ast_mdl_prj_stat;
-- 市属中心资产转让业务成交项目统计表
CREATE TABLE ads.ads_sszx_ast_mdl_prj_stat (
    deal_num Nullable(Decimal(24, 0)) COMMENT '成交数量',
    deal_value Nullable(Decimal(24, 6)) COMMENT '成交金额',
    proj_status Nullable(String) COMMENT '项目状态',
    appraised_value Nullable(Decimal(24, 6)) COMMENT '资产评估值',
    deal_date String COMMENT '成交日期',
    asset_category Nullable(String) COMMENT '资产类别',
    monitored_asset_category Nullable(String) COMMENT '监测资产类别',
    cagetory Nullable(String) COMMENT '类别'
) ENGINE = MergeTree()
ORDER BY deal_date;

-- 删除市管企业交易总体情况汇总表
DROP TABLE IF EXISTS ads.ads_sszx_entp_txn_smy;
-- 市管企业交易总体情况汇总表
CREATE TABLE ads.ads_sszx_entp_txn_smy (
    bus_type Nullable(String) COMMENT '业务类型',
    deal_date String COMMENT '成交日期',
    deal_qty Nullable(Decimal(24, 0)) COMMENT '成交数量',
    st_own_qty Nullable(Decimal(24, 0)) COMMENT '市属国有成交数量',
    st_own_amt Nullable(Decimal(24, 6)) COMMENT '市属国有成交金额',
    oth_own_qty Nullable(Decimal(24, 0)) COMMENT '其他国有成交数量',
    oth_own_amt Nullable(Decimal(24, 6)) COMMENT '其他国有成交金额',
    priv_qty Nullable(Decimal(24, 0)) COMMENT '民营成交数量',
    priv_amt Nullable(Decimal(24, 6)) COMMENT '民营成交金额',
    for_qty Nullable(Decimal(24, 0)) COMMENT '外资成交数量',
    for_amt Nullable(Decimal(24, 6)) COMMENT '外资成交金额',
    indv_qty Nullable(Decimal(24, 0)) COMMENT '个人成交数量',
    indv_amt Nullable(Decimal(24, 6)) COMMENT '个人成交金额',
    bond_tran_qty Nullable(Decimal(24, 0)) COMMENT '涉及债券转让成交数量',
    bond_tran_amt Nullable(Decimal(24, 6)) COMMENT '涉及债券转让成交金额',
    land_use_qty Nullable(Decimal(24, 0)) COMMENT '涉及土地使用权成交数量',
    land_use_amt Nullable(Decimal(24, 6)) COMMENT '涉及土地使用权成交金额',
    tech_asset_qty Nullable(Decimal(24, 0)) COMMENT '涉及技术资产成交数量',
    tech_asset_amt Nullable(Decimal(24, 6)) COMMENT '涉及技术资产成交金额',
    st_own_trans Nullable(String) COMMENT '其中市属国有受让',
    proj_type Nullable(String) COMMENT '项目类型',
    proj_category Nullable(String) COMMENT '项目分类'
) ENGINE = MergeTree()
ORDER BY deal_date;

-- 删除混改情况汇总表
DROP TABLE IF EXISTS ads.ads_sszx_rtms_sttn_smy;
-- 混改情况汇总表
CREATE TABLE ads.ads_sszx_rtms_sttn_smy (
    bus_type Nullable(String) COMMENT '业务类型',
    deal_date String COMMENT '成交日期',
    deal_qty Nullable(Decimal(24, 0)) COMMENT '成交数量',
    st_own_loc_qty Nullable(Decimal(24, 0)) COMMENT '国有市属国企数量',
    st_own_loc_amt Nullable(Decimal(24, 6)) COMMENT '国有市属国企金额',
    st_own_cent_qty Nullable(Decimal(24, 0)) COMMENT '国有央属数量',
    st_own_cent_amt Nullable(Decimal(24, 6)) COMMENT '国有央属金额',
    st_own_oth_qty Nullable(Decimal(24, 0)) COMMENT '国有其他数量',
    st_own_oth_amt Nullable(Decimal(24, 6)) COMMENT '国有其他金额',
    priv_qty Nullable(Decimal(24, 0)) COMMENT '民营数量',
    priv_amt Nullable(Decimal(24, 6)) COMMENT '民营金额',
    for_qty Nullable(Decimal(24, 0)) COMMENT '外资数量',
    for_amt Nullable(Decimal(24, 6)) COMMENT '外资金额',
    indv_qty Nullable(Decimal(24, 0)) COMMENT '个人数量',
    indv_amt Nullable(Decimal(24, 6)) COMMENT '个人金额',
    jh_reg_qty Nullable(Decimal(24, 0)) COMMENT '津冀地区数量',
    jh_reg_amt Nullable(Decimal(24, 6)) COMMENT '津冀地区金额',
    oth_reg_qty Nullable(Decimal(24, 0)) COMMENT '其他地区数量',
    oth_reg_amt Nullable(Decimal(24, 6)) COMMENT '其他地区金额',
    mix_own_type Nullable(String) COMMENT '混改类型',
    proj_type Nullable(String) COMMENT '项目类型'
) ENGINE = MergeTree()
ORDER BY deal_date;

-- 删除压减项目汇总表
DROP TABLE IF EXISTS ads.ads_sszx_reduct_prj_smy;
-- 压减项目汇总表
CREATE TABLE ads.ads_sszx_reduct_prj_smy (
    deal_date String COMMENT '成交日期',
    deal_qty Nullable(Decimal(24, 0)) COMMENT '成交数量',
    oth_own_qty Nullable(Decimal(24, 0)) COMMENT '其他国有成交数量',
    oth_own_amt Nullable(Decimal(24, 6)) COMMENT '其他国有成交金额',
    priv_qty Nullable(Decimal(24, 0)) COMMENT '民营成交数量',
    priv_amt Nullable(Decimal(24, 6)) COMMENT '民营成交金额',
    for_qty Nullable(Decimal(24, 0)) COMMENT '外资成交数量',
    for_amt Nullable(Decimal(24, 6)) COMMENT '外资成交金额',
    indv_qty Nullable(Decimal(24, 0)) COMMENT '个人成交数量',
    indv_amt Nullable(Decimal(24, 6)) COMMENT '个人成交金额',
    red_type Nullable(String) COMMENT '压减类型',
    eval_value Nullable(Decimal(24, 6)) COMMENT '评估值',
    add_value Nullable(Decimal(24, 6)) COMMENT '增值金额'
) ENGINE = MergeTree()
ORDER BY deal_date;
