-- Active: 1725412082987@@10.254.99.145@10000@dws
SELECT  regexp_replace(t.Deal_Dt,'^(\\d{4})(\\d{2})(\\d{2})$','$1-$2-$3')                    AS deal_date -- 成交日期 
       ,CASE WHEN prj.Prj_Bsn_Tp_Cd_Dsc LIKE '诉讼资产-%' THEN '诉讼资产'
             WHEN prj.Prj_Bsn_Tp_Cd_Dsc = '资产转让' THEN '大宗实物' 
        ELSE prj.Prj_Bsn_Tp_Cd_Dsc END                                                       AS proj_type -- 业务类型 
       ,COUNT(DISTINCT prj.Prj_Id)                                                           AS deal_prj_num --成交项目数量 
       ,SUM(t.Deal_Amt)                                                                      AS deal_amt -- 成交金额 
       ,SUM(a.hpn_amt)                                                                       AS entry_price_amt -- 进场价款金额 
       ,SUM(a.hpn_amt)/NULLIF(SUM(t.Deal_Amt),0)                                             AS onsite_setl_rate -- 场内结算率 = 进场价款金额/成交金额 
FROM dwd.dwd_evt_deal_rec_fct t
LEFT JOIN dwd.dwd_prj_fct prj -- 项目基本维度表 
ON prj.prj_wrd = t.prj_wrd AND prj.dt = ${dmp_day}
INNER JOIN
(
	SELECT  k.project_code AS plform_prj_id -- 项目编号 
       ,SUM(k.hpn_amt) AS hpn_amt -- 发生金额 
	FROM
	(
		SELECT  a.project_code
			,a.hpn_amt
		FROM std.std_bjhl_tbid_zfdd t -- 支付订单 
		INNER JOIN std.std_bjhl_tbid_zfddmx a -- 支付订单明细 
		ON t.order_no = a.order_no AND a.dt = '${dmp_day}'
		WHERE t.dt = '${dmp_day}'
		AND t.ddzt IN ('4', '1', '3', '6')
		AND a.ywlx_code IN ('3', '19') 
		UNION ALL
		-- 保证金转价款 
		SELECT  b.plform_prj_id   AS plform_prj_id -- 项目编号 
			,a.mrgn_tf_prc_amt AS hpn_amt -- 发生金额 
		FROM dwd.dwd_evt_deal_rec_fct a
		LEFT JOIN dwd.dwd_prj_fct b
		ON a.prj_wrd = b.prj_wrd AND a.dt = b.dt
		WHERE a.is_mrgn_of_txn_prc = 1
		AND a.dt = '${dmp_day}'
		AND b.prj_id IS NOT NULL
	) k
	GROUP BY  k.project_code 
) a
ON a.plform_prj_id = prj.plform_prj_id
WHERE t.dt = ${dmp_day}
GROUP BY  regexp_replace(t.Deal_Dt,'^(\\d{4})(\\d{2})(\\d{2})$','$1-$2-$3') -- 成交日期 
         ,CASE WHEN prj.Prj_Bsn_Tp_Cd_Dsc LIKE '诉讼资产-%' THEN '诉讼资产'
               WHEN prj.Prj_Bsn_Tp_Cd_Dsc = '资产转让' THEN '大宗实物'  
               ELSE prj.Prj_Bsn_Tp_Cd_Dsc END