WITH DEAL_TABLE AS
(
	SELECT  DEAL_REC_ID
	       ,CUST_WRD
	       ,PRJ_WRD
	       ,ORDER_ID_YW
	FROM DWD.DWD_EVT_DEAL_REC_FCT 
	LATERAL VIEW EXPLODE(SPLIT(REGEXP_REPLACE(MRGN_TF_PRC_ORDER_ID, '^,|,$', ''), ',')) EXPLODED_TABLE AS ORDER_ID_YW -- 保证金转价款订单ID 
	WHERE DT = '${dmp_day}'
	AND IS_MRGN_OF_TXN_PRC = '1'
	AND MRGN_TF_PRC_ORDER_ID IS NOT NULL 
)
-- 产权转让 
SELECT  T.DEAL_REC_ID
       ,K.CPTL_LO
FROM DEAL_TABLE T
LEFT JOIN DWD.DWD_PRJ_FCT P
ON T.PRJ_WRD = P.PRJ_WRD 
AND P.DT = '${dmp_day}'
LEFT JOIN
(
	SELECT  A.ORDER_NO
	       ,'BJHL' || A.KHH                                                   AS CUST_WRD
	       ,A.CLASS                                                           AS PRJ_BSN_TP_CD
	       ,B.BSN_PRJ_ID
	       ,CASE WHEN A.SETL_BANK LIKE '%BJDJJS%' THEN '北京结算'  ELSE '北交所' END AS CPTL_LO -- 资金位置 
	       ,B.ID                                                               AS YW_ID
	FROM STD.STD_BJHL_TBID_ZFDD A
	LEFT JOIN
	(
		SELECT  ID
		       ,PRJ AS BSN_PRJ_ID
		       ,ORDR_ID_PLTFRM
		       ,PY_SIDE
		FROM STD.STD_BJHL_TCQZR_YWDD_D
		WHERE DT = '${dmp_day}'
		AND DEPOSIT_TFR_TXN_AMT = 1 
	) B
	ON A.ORDER_NO = B.ORDR_ID_PLTFRM 
       AND A.KHH = B.PY_SIDE
	WHERE B.ID IS NOT NULL
	AND A.CLASS = 'GQ' 
       AND A.DT = '${dmp_day}'
) K
ON T.ORDER_ID_YW = K.YW_ID AND P.BSN_PRJ_ID = K.BSN_PRJ_ID AND T.CUST_WRD = K.CUST_WRD
WHERE K.YW_ID IS NOT NULL
AND P.PRJ_BSN_TP_CD = 'GQ' 
UNION ALL
-- 大宗实物 
SELECT  T.DEAL_REC_ID
       ,K.CPTL_LO
FROM DEAL_TABLE T
LEFT JOIN DWD.DWD_PRJ_FCT P
ON T.PRJ_WRD = P.PRJ_WRD 
AND P.DT = '${dmp_day}'
LEFT JOIN
(
	SELECT  A.ORDER_NO
	       ,'BJHL' || A.KHH                                                   AS CUST_WRD
	       ,A.CLASS                                                           AS PRJ_BSN_TP_CD
	       ,B.BSN_PRJ_ID
	       ,CASE WHEN A.SETL_BANK LIKE '%BJDJJS%' THEN '北京结算'  ELSE '北交所' END AS CPTL_LO -- 资金位置 
	       ,B.ID                                                              AS YW_ID
	FROM STD.STD_BJHL_TBID_ZFDD A
	LEFT JOIN
	(
		SELECT  ID
		       ,PRJ AS BSN_PRJ_ID
		       ,ORDR_ID
		FROM STD.STD_BJHL_TDZSW_YWDD_D
		WHERE DT = '${dmp_day}'
		AND DEPOSIT_IS_TPRICE = 1 
	) B
	ON A.ORDER_NO = B.ORDR_ID
	WHERE B.ID IS NOT NULL
	AND A.CLASS = '1D' 
       AND A.DT = '${dmp_day}'
) K
ON T.ORDER_ID_YW = K.YW_ID AND P.BSN_PRJ_ID = K.BSN_PRJ_ID AND T.CUST_WRD = K.CUST_WRD
WHERE K.YW_ID IS NOT NULL
AND P.PRJ_BSN_TP_CD = '1D'