with temp_cust as(
        SELECT
        b.mdlg_usr_wrd,
        a.agent_no,
        a.compy_name
        FROM (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY a.cust_no ORDER BY a.update_time DESC) AS rn
        FROM ods.ods_bl_agent_info a
        WHERE a.dt = '${dmp_day}'
        ) a
        LEFT JOIN (
        SELECT * FROM dim.dim_pty_cust_usr_rel
        WHERE dt='${dmp_day}'
        AND edw_end_dt='20991231'
        ) b ON 'BJHL'||a.cust_no=b.cust_wrd
        WHERE a.rn = 1 --取最新的一条
)

select a.info_publ_start_dt as data_dt,
       coalesce(b.agent_no,'未知')    as agent_no,
       coalesce(b.compy_name,'未知')        as agent_name,
       a.prj_bsn_tp_cd,
       count(distinct case when a.prj_id like '%-%' then null else a.prj_id end )             as xmsl
from dwd.dwd_prj_fct a
left join temp_cust b on 'BJHL'||a.txn_svc_mber_id =b.mdlg_usr_wrd
left join dwd.dwd_bulk_obj_prj_fct d on a.bsn_prj_wrd = d.bsn_prj_wrd and d.dt = '${dmp_day}'
where a.dt = '${dmp_day}'
  and a.prj_bsn_tp_cd in ('GQ', '1D', '1C')
   and b.agent_no is not null
group by a.info_publ_start_dt, b.agent_no, b.compy_name, a.prj_bsn_tp_cd