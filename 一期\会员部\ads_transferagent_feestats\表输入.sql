with temp_cust as(
  SELECT
        DISTINCT
        b.mdlg_usr_wrd,
        a.agent_no,
        a.compy_name,
        a.cust_no,
        a.prof_srv_mem_type_dsc,
        a.mber_cgy_cd_dsc
        FROM (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY a.cust_no ORDER BY a.update_time DESC) AS rn
        FROM ods.ods_bl_agent_info a
        WHERE a.dt = '${dmp_day}'
        ) a
        LEFT JOIN (
        SELECT * FROM dim.dim_pty_cust_usr_rel
        WHERE dt='${dmp_day}'
        AND edw_end_dt='20991231'
        ) b ON 'BJHL'||a.cust_no=b.cust_wrd
        WHERE a.rn = 1 --取最新的一条
),
    temp_xzsw_cust as (
      select 
       DISTINCT
       a.prj_wrd,
       a.trsfer_cust_wrd,
       b.cust_full_nm as seller_fincer_name,
       a.task_recver_cust_wrd,
       D.compy_name as agent_mem,
       D.agent_no,
       D.prof_srv_mem_type_dsc,
       D.mber_cgy_cd_dsc
        from dwd.dwd_evt_task_list_fct a
                left join dim.dim_pty_cust b on a.trsfer_cust_wrd = b.cust_wrd and b.dt = '${dmp_day}'
                left join dim.dim_pty_cust c on a.task_recver_cust_wrd = c.cust_wrd and c.dt = '${dmp_day}'
                left join ods.ods_bl_agent_info d on a.task_recver_cust_wrd='BJHL'||d.cust_no and d.dt='${dmp_day}'
        where a.dt = '${dmp_day}'
),
temp_jdc_cust as (
     select 
       DISTINCT
       a.rltv_prj as prj_wrd,
       a.seller_fincer_attr_inves_sbj,
       b.cust_full_nm as seller_fincer_name,
       a.tsk_rcv_side_unit,
       d.compy_name as agent_mem,
       d.agent_no,
       D.prof_srv_mem_type_dsc,
       D.mber_cgy_cd_dsc
from dwd.dwd_bid_task_list_info a
         left join dim.dim_pty_cust b on 'BJHL'||a.seller_fincer_attr_inves_sbj = b.cust_wrd and b.dt = '${dmp_day}'
         left join dim.dim_pty_cust c on 'BJHL'||a.tsk_rcv_side_unit = c.cust_wrd and c.dt = '${dmp_day}'
         left join ods.ods_bl_agent_info d on a.tsk_rcv_side_unit=d.cust_no and d.dt='${dmp_day}'
where a.dt = '${dmp_day}'
)
select a.prj_bsn_tp_cd,
       case when a.prj_bsn_tp_cd in ('GQ', '1D', '1C', '1G')  then b.bsn_rec_deal_dt
           else b.delvry_dt end
           as deal_date,
       case when a.prj_bsn_tp_cd in ('GQ','1D','1C','1G') then  f.agent_no
           when a.prj_bsn_tp_cd ='1F' then t5.agent_no
           when a.prj_bsn_tp_cd ='1B' then t6.agent_no  end
           as lessor_agent,
           case when a.prj_bsn_tp_cd in ('GQ','1D','1C','1G') then  f.compy_name
           when a.prj_bsn_tp_cd ='1F' then t5.agent_mem
           when a.prj_bsn_tp_cd ='1B' then t6.agent_mem  end
           as lessee_agent,
       sum(e.bjssr) as cbex_fee_amt,
       case when a.prj_bsn_tp_cd in ('GQ','1D','1C','1G') then  f.prof_srv_mem_type_dsc
           when a.prj_bsn_tp_cd ='1F' then t5.prof_srv_mem_type_dsc
           when a.prj_bsn_tp_cd ='1B' then t6.prof_srv_mem_type_dsc  end
           as prof_srv_mem_type_dsc,
       case when a.prj_bsn_tp_cd in ('GQ','1D','1C','1G') then  f.mber_cgy_cd_dsc
           when a.prj_bsn_tp_cd ='1F' then t5.mber_cgy_cd_dsc
           when a.prj_bsn_tp_cd ='1B' then t6.mber_cgy_cd_dsc  end
       as mber_cgy_cd_dsc
from dwd.dwd_prj_fct a
         left join (select distinct prj_wrd,delvry_dt,bsn_rec_deal_dt from  dwd.dwd_evt_deal_rec_fct where dt='${dmp_day}') b on a.prj_wrd = b.prj_wrd
         left join dwd.dwd_prj_fee_fct e on a.prj_id = e.prj_id and e.dt='${dmp_day}'
         left join temp_cust f on 'BJHL'||a.txn_svc_mber_id = f.mdlg_usr_wrd
         left join dwd.dwd_bulk_obj_prj_fct t1 on a.bsn_prj_wrd = t1.bsn_prj_wrd and t1.dt='${dmp_day}'
         left join dwd.dwd_ast_lease_prj_fct t2 on a.bsn_prj_wrd = t2.bsn_prj_wrd and t2.dt='${dmp_day}'
         left join dwd.dwd_evt_task_list_fct t3 on a.prj_wrd = t3.prj_wrd and t3.dt='${dmp_day}'
         left join dwd.dwd_bid_task_list_info t4 on a.prj_wrd = t4.rltv_prj and t4.dt='${dmp_day}'
         left join temp_xzsw_cust t5 on a.prj_wrd=t5.prj_wrd
         left join temp_jdc_cust t6 on a.prj_wrd=t6.prj_wrd
where a.dt = '${dmp_day}'
  and a.prj_bsn_tp_cd in ('GQ', '1D', '1F', '1C', '1G', '1B')
  and (case when a.prj_bsn_tp_cd in ('GQ', '1D', '1C', '1G')  then b.bsn_rec_deal_dt
           else b.delvry_dt end) is not null
  and (f.agent_no is not null or t5.agent_no is not null or t6.agent_no is not null)
group by (case when a.prj_bsn_tp_cd in ('GQ', '1D', '1C', '1G')  then b.bsn_rec_deal_dt
           else b.delvry_dt end),
           (case when a.prj_bsn_tp_cd in ('GQ','1D','1C','1G') then  f.agent_no
           when a.prj_bsn_tp_cd ='1F' then t5.agent_no
           when a.prj_bsn_tp_cd ='1B' then t6.agent_no  end),
           (case when a.prj_bsn_tp_cd in ('GQ','1D','1C','1G') then  f.compy_name
           when a.prj_bsn_tp_cd ='1F' then t5.agent_mem
           when a.prj_bsn_tp_cd ='1B' then t6.agent_mem  end)
           , a.prj_bsn_tp_cd,case when a.prj_bsn_tp_cd in ('GQ','1D','1C','1G') then  f.prof_srv_mem_type_dsc
           when a.prj_bsn_tp_cd ='1F' then t5.prof_srv_mem_type_dsc
           when a.prj_bsn_tp_cd ='1B' then t6.prof_srv_mem_type_dsc  end,
       case when a.prj_bsn_tp_cd in ('GQ','1D','1C','1G') then  f.mber_cgy_cd_dsc
           when a.prj_bsn_tp_cd ='1F' then t5.mber_cgy_cd_dsc
           when a.prj_bsn_tp_cd ='1B' then t6.mber_cgy_cd_dsc  end
           