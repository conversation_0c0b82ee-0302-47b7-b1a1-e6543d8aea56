with temp_cust as(
        SELECT
        distinct
        b.mdlg_usr_wrd,
        a.agent_no,
        a.compy_name
        FROM (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY a.cust_no ORDER BY a.update_time DESC) AS rn
        FROM ods.ods_bl_agent_info a
        WHERE a.dt = '${dmp_day}'
        ) a
        LEFT JOIN (
        SELECT * FROM dim.dim_pty_cust_usr_rel
        WHERE dt='${dmp_day}'
        AND edw_end_dt='20991231'
        ) b ON 'BJHL'||a.cust_no=b.cust_wrd
        WHERE a.rn = 1 --取最新的一条
),
    ods_bl_center_formal_dcl_rownum AS (
    SELECT 
        *,
        ROW_NUMBER() OVER (PARTITION BY ht_proj_no ORDER BY udt_tm DESC) AS rn  -- 按ht_proj_no分组，按udt_tm倒序排，生成行号
    FROM 
        ods.ods_bl_center_formal_dcl
    WHERE dt = '${dmp_day}'
    and (is_delete='0' and is_delete is null)
),
    temp as(
select
      distinct
    a.prj_bsn_tp_cd_dsc as proj_type,
    '北交所' as exch,
    case when a.prj_id like '%-%' then '是' else '否' end as is_repeat,
    a.prj_id as proj_no,
    a.prj_nm as proj_name,
    a.info_publ_start_dt as info_dclo_begin_dt,
    a.info_publ_exprt_dt as info_dclo_expire_dt,
    a.lit_amt/10000  as ht_amt,
    a.prj_prt_cntry_sfep_mgr_dept as blng_grp,
    a.prj_prt_nm as seller_fincer_name,
    a.prj_prt_sown_spvs_org_cd_dsc as oasset_custd_org,
    a.prj_blng_dept_nm   as proj_belong_dept_name,
    a.prj_mgr_nm as proj_princ_name,
    a.txn_svc_mber_nm as agent_mem,
    b.agent_no as agent_no
    from dwd.dwd_prj_fct a
    left join temp_cust b on 'BJHL'||a.txn_svc_mber_id = b.mdlg_usr_wrd
where a.dt='${dmp_day}' and a.prj_bsn_tp_cd in ('GQ', '1C')
and a.txn_svc_mber_id is not null
and a.prj_id not like '%-%'
and a.info_publ_start_dt is not NULL
and a.info_publ_start_dt != ''
union  all
-- 补录数据
select 
    distinct
    a.proj_type  as proj_type,
    case
        when a.exch = '上海' then '上海联交所'
        when a.exch = '广东' then '广东联交所'
        when a.exch = '重庆' then '重庆联交所'
        when a.exch = '深圳' then '深圳联交所'
        when a.exch = '山东' then '山东交易所' end                   as exch, --交易所
    case when a.ht_proj_no like '%-%' then '是' else '否' end                           as is_repeat, --是否重复挂牌
    a.ht_proj_no as proj_no,
    a.subj_matter_name as proj_name,
    a.info_dclo_begin_dt as info_dclo_begin_dt, -- 纰漏开始日期
    pc.lit_expi_dt as info_dclo_expire_dt, --结束日期
    a.ht_amt_w_yuan as ht_amt, -- 挂牌金额
    a.belong_group as blng_grp, --所属集团
    a.seller_fincer_name as seller_fincer_name, --转让方名称
    a.custd_type as oasset_custd_org, --国家监管机构
    '无' as proj_belong_dept_name,
    '' as proj_princ_name,
    a.agent_mem as agent_mem, --代理会员
    c.agent_no as agent_no
from ods_bl_center_formal_dcl_rownum a
left join (
    select DISTINCT prj_id,lit_expi_dt,tfr_prc from dwd.dwd_stb_prj_fct  where dt='${dmp_day}'
) pc
on a.ht_proj_no = pc.prj_id
left join temp_cust c on a.agent_mem = c.compy_name
    where a.rn = 1
    and a.agent_mem is not null 
    and a.agent_mem != ''
    and a.exch in ('上海','广东','重庆','深圳','山东')
    and a.proj_type in ('产权转让','企业增资')
    and a.ht_proj_no not like '%-%'
    and a.info_dclo_begin_dt is not NULL
    and a.info_dclo_begin_dt != ''
  )
select
    info_dclo_begin_dt,agent_no as agnet_no,agent_mem,proj_type as business_type,exch,coalesce(proj_belong_dept_name,'未知') as proj_belong_dept_name,
    count(distinct proj_no) as lim_prj_num,
    sum(ht_amt) as lim_prj_amt
    from temp
    where agent_no is not null
group by info_dclo_begin_dt,agent_mem,proj_type,exch,proj_belong_dept_name,agent_no