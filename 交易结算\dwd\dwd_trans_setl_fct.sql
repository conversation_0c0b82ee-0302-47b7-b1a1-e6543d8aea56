select  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm) as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code
    ,d.bsn_sbj    --业务科目
    ,case when a.class='资产转让' then '大宗实物'   else a.class end as  bsn_type   --   订单类型

    ,case when    a.ywlx ='中台出金' and nvl(a.bsn_tab_nm,'1')  not in ('BID.tBID_ZJLS', 'tBID_ZJLS') then '出金'
    	when a.ywlx ='中台出金' AND nvl(a.bsn_tab_nm,'1')  in ('BID.tBID_ZJLS', 'tBID_ZJLS') and d.bsn_sbj is NULL then '出金'
    	else coalesce(m1.crj_flag, m2.crj_flag)  end as  crj_flag   --   出入金标识

    ,case when  a.ywlx ='中台出金' and nvl(a.bsn_tab_nm,'1')  not in ('BID.tBID_ZJLS', 'tBID_ZJLS') then '中台出金'
    	when a.ywlx ='中台出金' AND nvl(a.bsn_tab_nm,'1')  in ('BID.tBID_ZJLS', 'tBID_ZJLS') and d.bsn_sbj is NULL then '中台出金'
    	else coalesce(m1.cptl_type_lrgcls, m2.cptl_type_lrgcls) end as  cptl_type_lrgcls   --   资金类型-大类

    ,case when  a.ywlx ='中台出金' and nvl(a.bsn_tab_nm,'1')  not in ('BID.tBID_ZJLS', 'tBID_ZJLS') then '中台出金'
    	when a.ywlx ='中台出金' AND nvl(a.bsn_tab_nm,'1')  in ('BID.tBID_ZJLS', 'tBID_ZJLS') and d.bsn_sbj is NULL then '中台出金'
    	else coalesce(m1.cptl_type_smlcls, m2.cptl_type_smlcls) end as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,case when a.class = '珍品' then ywsq.clrq 
          else CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END
     end as  pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算'
          else '北交所'
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as  setl_type  --   结算方式
    ,t.paot_info AS col_pay_agent_name -- 代付方
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
on e.proj_no=b.prj_id
and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left join  dim.dim_bjhl_ywkm_map m1
on m1.map_type='1' and m1.bsn_sbj=d.bsn_sbj and m1.bsn_type_code=a.ywlx_code
left join  dim.dim_bjhl_ywkm_map m2
on m2.map_type='2' and m2.bsn_type_code=a.ywlx_code
-- left join  std.std_bjhl_ttpglb  e --退票管理表
-- on e.ret_tckt_ordr_no = a.order_no
-- and  e.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid
and tc.dt  ='${dmp_day}'
left join std.std_bjhl_tdsfzf_ywsq_d ywsq  -- 珍品，"支付成功日期"目前取值“清算发生日期”(珍品资金清算发起出金指令的日期）********
on t.py_aplc_no = ywsq.aply_no
and ywsq.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt IN ('4','6','16' ,'-2','5')
and (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
UNION ALL -- 定时出金
SELECT  mx.lsh 	as	ordr_no	 -- 	订单号
		,xmxx.prj_id	as	ordr_prj_no	 -- 	订单项目编号
		,xmxx.prj_nm	as	ordr_prj_name	 -- 	订单项目名称
		,null	as	prj_optr	 -- 	项目经办人
		,null	as	prj_optr_dept	 -- 	项目经办部门
		,null	as	prj_optr_org	 -- 	业务中心
		,null	as	cust_no	 -- 	客户号
		,null	as	cust_name	 -- 	客户名称
		,null	as	ast_type	 -- 	资产类型
        ,null as  bsn_sbj    --业务科目
		,'珍品'	AS bsn_type	 -- 	订单类型
		,'出金' 	as	crj_flag	 -- 	出入金标识
		,'价款'  as	cptl_type_lrgcls	 -- 	资金类型-大类
		,'定时出金'	AS cptl_type_smlcls	 -- 	资金类型-小类
		,mx.fsje	as	amt	 -- 	发生金额
		,null 	as	ccy	 -- 	币种
		,null	as	pay_mode	 -- 	支付方式
		,ywsq.clrq 	as	pay_success_date	 -- 	支付成功时间
		,null	as	setl_bank	 -- 	结算银行
		,CASE WHEN INSTR(pz.qddm_zc, 'BJDJJS') > 0 OR INSTR(pz.qddm_zc, 'BJDJJSLX') > 0  THEN '北京结算' ELSE '北交所' END AS cptl_lo -- 资金位置
		,hz.fsrq	as	bank_to_acc_date	 -- 	银行到账时间
		,null as 		setl_type	 -- 	结算方
		,null AS col_pay_agent_name -- 代付方
		,null AS order_creat_dt --订单创建日期
          ,'' as ext_ordr_no -- 机构订单号
          ,hz.zfsqh as py_aply_no -- 付款申请号
from ods.ods_bjhl_tzjqsmx mx
inner join ods.ods_bjhl_tzjqshz hz
on hz.cljg ='2' and mx.zjqshz_lsh = hz.lsh
AND hz.dt  ='${dmp_day}'
inner join ods.ods_bjhl_tzjqspz pz
ON  hz.zjqspz = pz.id
AND pz.dt  ='${dmp_day}'
LEFT JOIN (SELECT plform_prj_id,prj_id,prj_nm FROM dwd.dwd_prj_fct WHERE dt = '${dmp_day}' GROUP BY plform_prj_id,prj_id,prj_nm) xmxx-- 项目挂牌事实表
ON xmxx.plform_prj_id = mx.xgid
left join std.std_bjhl_tdsfzf_ywsq_d ywsq  -- 珍品，"支付成功日期"目前取值“清算发生日期”(珍品资金清算发起出金指令的日期）********
on hz.zfsqh = ywsq.aply_no
and ywsq.dt  ='${dmp_day}'
WHERE mx.dt  ='${dmp_day}'
UNION ALL -- 退货
select  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm)as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code
         ,d.bsn_sbj    --业务科目
    ,case when a.class='资产转让' then '大宗实物'   else a.class end as  bsn_type   --   订单类型
    ,'入金' as  crj_flag   --   出入金标识
    ,'退票' as  cptl_type_lrgcls   --   资金类型-大类
    ,'退票入金' as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END   pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算'
          else '北交所'
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as    setl_type  --   结算方式
    ,t.paot_info AS col_pay_agent_name -- 代付方
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
INNER JOIN  ods.ods_bjhl_ttpglb  tp --退票记录
ON  tp.tpddh = a.order_no
and tp.dt  ='${dmp_day}'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
on e.proj_no=b.prj_id
and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid
and tc.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt IN ('4','6','16' ,'-2','5')
and (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
 union all -- 房屋出租 价款抵扣服务费 入金'
select  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm)as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code
         ,d.bsn_sbj    --业务科目
    ,case when a.class='资产转让' then '大宗实物'   else a.class end as  bsn_type   --   订单类型
    ,'入金' as  crj_flag   --   出入金标识
    ,'服务费' as  cptl_type_lrgcls   --   资金类型-大类
    ,'价款抵扣服务费（服务费入金）' as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END  as  pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算'
          else '北交所'
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as  setl_type  --   结算方式
    ,t.paot_info AS col_pay_agent_name -- 代付方
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
inner join std.std_bjhl_tzccz_ywdd  zccz --资产出租业务订单
on a.order_no=zccz.ddbh
AND zccz.dt  ='${dmp_day}'
AND zccz.jksfdkfwf='1'
AND a.class='房屋出租'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
 left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
 on e.proj_no=b.prj_id
 and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid
and tc.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt  IN ('4','6','16' ,'-2','5')
and (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
UNION ALL -- 房屋出租 价款抵扣服务费 出金
select  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm)as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code
         ,d.bsn_sbj    --业务科目
    ,case when a.class='资产转让' then '大宗实物'   else a.class end as  bsn_type   --   订单类型
    ,'出金' as  crj_flag   --   出入金标识
    ,'价款' as  cptl_type_lrgcls   --   资金类型-大类
    ,'价款抵扣服务费（价款出金）' as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END  as  pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算'
          else '北交所'
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as    setl_type  --   结算方式
    ,t.paot_info AS col_pay_agent_name -- 代付方
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
inner join std.std_bjhl_tzccz_ywdd  zccz --资产出租业务订单
on a.order_no=zccz.ddbh
AND zccz.dt  ='${dmp_day}'
AND zccz.jksfdkfwf='1'
AND a.class='房屋出租'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
 left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
 on e.proj_no=b.prj_id
 and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid
and tc.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt  IN ('4','6','16' ,'-2','5')
and (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
 union all -- 房屋出租 保证金转服务费 入金'
select  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm)as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code
         ,d.bsn_sbj    --业务科目
    ,case when a.class='资产转让' then '大宗实物'   else a.class end as  bsn_type   --   订单类型
    ,'入金' as  crj_flag   --   出入金标识
    ,'服务费' as  cptl_type_lrgcls   --   资金类型-大类
    ,'保证金抵扣服务费（服务费入金）' as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END  as  pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算'
          else '北交所'
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as    setl_type  --   结算方式
    ,t.paot_info AS col_pay_agent_name -- 代付方
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
inner join std.std_bjhl_tzccz_ywdd  zccz --资产出租业务订单
on a.order_no=zccz.ddbh
AND zccz.dt  ='${dmp_day}'
AND zccz.bzjsfzfwf='1'
AND a.class='房屋出租'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
 left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
 on e.proj_no=b.prj_id
 and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid
and tc.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt  IN ('4','6','16' ,'-2','5')
and (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
UNION ALL -- 房屋出租 保证金转服务费 出金
select  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm)as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code
         ,d.bsn_sbj    --业务科目
    ,case when a.class='资产转让' then '大宗实物'   else a.class end as  bsn_type   --   订单类型
    ,'出金' as  crj_flag   --   出入金标识
    ,'保证金' as  cptl_type_lrgcls   --   资金类型-大类
    ,'保证金抵扣服务费（保证金出金）' as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END  as  pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算'
          else '北交所'
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as    setl_type  --   结算方式
    ,t.paot_info AS col_pay_agent_name -- 代付方
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
inner join std.std_bjhl_tzccz_ywdd  zccz --资产出租业务订单
on a.order_no=zccz.ddbh
AND zccz.dt  ='${dmp_day}'
AND zccz.bzjsfzfwf='1'
AND a.class='房屋出租'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
on e.proj_no=b.prj_id
and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid
and tc.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt  IN ('4','6','16' ,'-2','5')
and (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
 union all -- 企业增资 保证金转服务费 入金'
select  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm)as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code
         ,d.bsn_sbj    --业务科目
    ,case when a.class='资产转让' then '大宗实物'   else a.class end as  bsn_type   --   订单类型
    ,'入金' as  crj_flag   --   出入金标识
    ,'服务费' as  cptl_type_lrgcls   --   资金类型-大类
    ,'保证金抵扣服务费（服务费入金）' as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END  as  pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算'
          else '北交所'
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as    setl_type  --   结算方式
    ,t.paot_info AS col_pay_agent_name -- 代付方
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
inner join std.std_bjhl_tcgq_zfxw  zf --支付信息表
 on  zf.bzjsfzfwf=1
 and zf.zflx in(2,4,5)
 and zf.khjs=1
 and a.order_no=zf.zfbh
 and zf.dt  ='${dmp_day}'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
on e.proj_no=b.prj_id
and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid
and tc.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt  IN ('4','6','16' ,'-2','5')
and (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
UNION ALL -- 企业增资 保证金转服务费 出金
select  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm)as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code
         ,d.bsn_sbj    --业务科目
    ,case when a.class='资产转让' then '大宗实物'   else a.class end as  bsn_type   --   订单类型
    ,'出金' as  crj_flag   --   出入金标识
    ,'保证金' as  cptl_type_lrgcls   --   资金类型-大类
    ,'保证金抵扣服务费（保证金出金）' as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END  as  pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算'
          else '北交所'
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as    setl_type  --   结算方式
    ,t.paot_info AS col_pay_agent_name -- 代付方
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
inner join std.std_bjhl_tcgq_zfxw  zf --支付信息表
 on  zf.bzjsfzfwf=1
 and zf.zflx in(2,4,5)
 and zf.khjs=1
 and a.order_no=zf.zfbh
 and zf.dt  ='${dmp_day}'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
on e.proj_no=b.prj_id
and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid
and tc.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt  IN ('4','6','16' ,'-2','5')
and (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
UNION ALL
-- 房屋出租 虚拟订单-保证金转价款（价款入金）
-- tbid_zfddmx支付订单明细当【ywlx】为29(转换后未19)时，这条数据作为保证金的出金，同时新增一条数据做价款入金
SELECT  t.order_no  as  ordr_no  --   订单号
    ,nvl(b.prj_id,tc.pkg_id) as  ordr_prj_no  --   订单项目编号
    ,nvl(b.prj_nm ,tc.pkg_nm) as  ordr_prj_name  --   订单项目名称
    ,b.prj_prin_nm  as  prj_optr   --   项目经办人
    ,b.prj_blng_dept_nm as  prj_optr_dept  --   项目经办部门
    ,org1.org_nm  as  prj_optr_org   --   业务中心
    ,t.khh  as  cust_no  --   客户号
    ,t.cust_nm  as  cust_name  --   客户名称
    ,a.ywlx as  ast_type   --   资产类型
    -- ,a.ywlx_code 
    ,d.bsn_sbj    --业务科目
    ,'房屋出租' as  bsn_type   --   订单类型  
    ,'入金' as  crj_flag   --   出入金标识
    ,'价款' as  cptl_type_lrgcls   --   资金类型-大类	
    ,'保证金转价款（价款入金）' as  cptl_type_smlcls   --   资金类型-小类
    ,a.hpn_amt  as  amt  --   发生金额
    ,t.ccy  as  ccy  --   币种
    ,t.pay_mode as  pay_mode   --   支付方式
    ,CASE WHEN t.py_scss_dt = 0 OR t.py_scss_dt IS null then t.bank_toacct_dt ELSE t.py_scss_dt END  as  pay_success_date   --   支付成功时间
    ,t.setl_bank  as  setl_bank  --   结算银行
    ,case when t.setl_bank  like '%BJDJJS%'  OR a.ywlx_code = '28' then '北京结算' 
          else '北交所' 
     end  as  cptl_lo  --   资金位置
    ,CASE WHEN t.bank_toacct_dt = 0 OR t.bank_toacct_dt IS null then t.py_scss_dt ELSE t.bank_toacct_dt END as  bank_to_acc_date   --   银行到账时间
    ,CASE WHEN t.ddxz = 4 THEN '场外结算' ELSE '场内结算' END as  setl_type  --   结算方式 
    ,t.paot_info AS col_pay_agent_name -- 代付方   
    ,t.crt_dt AS order_creat_dt --订单创建日期
    ,t.ext_order_no AS ext_ordr_no -- 机构订单号
    ,t.py_aplc_no as py_aplc_no -- 支付申请号
from std.std_bjhl_tbid_zfdd  t -- 支付订单
inner join std.std_bjhl_tbid_zfddmx a --  支付订单明细
on  t.order_no=a.order_no
and a.dt  ='${dmp_day}'
left join dwd.dwd_prj_fct b -- 项目挂牌事实表
on a.project_code=b.plform_prj_id
and b.dt  ='${dmp_day}'
left join  dim.dim_org_info org 
on  org.dt  ='${dmp_day}'
and org.org_id=b.prj_blng_dept_id
-- and  between org.edw_end_dt and org.edw_end_dt
and org.edw_end_dt='********'
left join  dim.dim_org_info org1 
on  org1.dt ='${dmp_day}'
and org1.org_id=org.supr_org_id
-- and  between org1.edw_end_dt and org1.edw_end_dt
and org1.edw_end_dt='********'
left anti join ods.ods_bl_jsb_balance_zero_proj_list  e -- 结算部余额为零的项目清单 过滤
on e.proj_no=b.prj_id
and b.dt  ='${dmp_day}'
-- left join std.std_bjhl_tcgq_zfxw c --  支付支付信息表
-- on  t.order_no=a.zfbh
-- and c.dt ='${dmp_day}'
left join std.std_bjhl_tbid_zjls d -- 资金流程表  bsn_sbj
on a.bsn_serial_no=cast(d.keyid as string)
and d.dt  ='${dmp_day}'
left join  dim.dim_bjhl_ywkm_map m1
on m1.map_type='1' and m1.bsn_sbj=d.bsn_sbj and m1.bsn_type_code=a.ywlx_code
left join  dim.dim_bjhl_ywkm_map m2
on m2.map_type='2' and m2.bsn_type_code=a.ywlx_code
-- left join  std.std_bjhl_ttpglb  e --退票管理表
-- on e.ret_tckt_ordr_no = a.order_no
-- and  e.dt  ='${dmp_day}'
left JOIN std.std_bjhl_tbid_bzjtc tc                   --保证金套餐
on a.project_code = tc.keyid 
AND tc.dt  ='${dmp_day}'
where t.dt  ='${dmp_day}'
AND   t.ddzt IN ('4','6','16' ,'-2','5')
AND (t.setl_bank != 'BJDJJSLX' or t.setl_bank is NULL)
AND a.ywlx_code = '19' 
AND a.class='房屋出租'
