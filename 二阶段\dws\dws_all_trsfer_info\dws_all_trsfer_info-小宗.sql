-- Active: 1725602637096@@127.0.0.1@3306@hive
with all_data as(
select 
case when t.bsn_prj_wrd is null then t.prj_wrd else t.bsn_prj_wrd end as bsn_prj_wrd, --项目业务关键字
t.prj_id as prj_no,
t.prj_nm,--项目名称
SPLIT(zcly, ',')[0]  as zcly,
'' prj_prt_sown_spvs_org_cd, --国资监管机构ID(一级资产来源代码)
case when zcly like '行政事业%' then '-' else SPLIT(zcly, ',')[2] end as prj_prt_sown_spvs_org_cd_dsc,--国资监管机构
substr(t5.jjksrq,1,4)||'-'||substr(t5.jjksrq,5,2)||'-'||substr(t5.jjksrq,7,2) as info_publ_start_dt, --挂牌日期
t.tfr_prc as tfr_prc, --转让底价
t2.pgz as Eval_Prc,--评估值
t3.delvry_dt as BSN_REC_DEAL_DT,
t3.mrgn_displ_mth_cd as txn_mth_cd,
t3.mrgn_displ_mth_cd_dsc txn_mth_cd_dsc, --成交方式
t3.deal_amt as deal_amt,
t.Bid_Mth_Cd, --竞价方式
t.Bid_Mth_Cd_Dsc ,               --竞价方式
t.Bid_Stat_Cd , --竞价状态代码
t.Bid_Stat_Cd_Dsc , --竞价状态代码描述
t.prj_prt_cntry_sfep_mgr_dept as  Blng_Org , -- 所属集团
t.prj_prt_nm , --项目方名称
t.prj_blng_dept_id, --所属部门ID
t2.ssbm as prj_blng_dept_nm, --所属部门
t.prj_prin_id, --项目负责人ID
t.prj_prin_nm, --项目负责人
t.Txn_Svc_Mber_Id, --代理会员ID
t.Txn_Svc_Mber_Nm , --代理会员
t.Prj_Stat_Cd, --项目状态代码
t.Prj_Stat_Cd_Dsc, --项目状态代码
t.Prj_Tp_Cd, --项目类型代码
t.Prj_Tp_Cd_Dsc, --项目类型代码描述
t2.is_sown_ast_tfr as is_owned_State,-- 是否国资
t.Prj_Bsn_Tp_Cd, --项目业务类型代码
t.prj_bsn_tp_cd_dsc, --项目业务类型名称
case when zcly like '行政事业%' then '行政事业单位' 
when zcly like '企业资产%'  and  SPLIT(zcly, ',')[2]  = '中央其他部委监管' then '部委'
when zcly like '企业资产%'  and  SPLIT(zcly, ',')[2]  = '国务院国资委监管' then '央企'
when zcly like '企业资产%'  and  SPLIT(zcly, ',')[2] in ('市级国资委监管','省级其他部门监管','省级国资委监管')  then '地方'
else '未知' end
as entp_tp, --企业类型
t.Info_Publ_Exprt_Dt,-- 信息披露期满日期
case when prj_id like '%-%' and  prj_id not like '%-0%' then '是' else '否' end is_repeat, -- 是否重复挂牌
'北交所' exch, --'交易所'
t.Lit_Amt, --挂牌金额 
''ecn_tp_cd, --经济类型代码
''ecn_tp_cd_dsc --经济类型名称
from
dwd.dwd_prj_fct t
left join (select * from dwd.dwd_evt_task_list_fct where dt='${dmp_day}') t4 on t.prj_wrd=t4.prj_wrd
left join (select * from dim.dim_ast_smitm where dt='${dmp_day}') t2 on  replace(t4.prj_prt_ast_wrd,',','')=t2.prj_prt_ast_wrd
left join (select * from dwd.dwd_evt_deal_rec_fct where dt='${dmp_day}') t3 on t.prj_wrd=t3.prj_wrd
left join (select * from ods.ods_bjhl_tbid_jjcc where dt='${dmp_day}') t5 on t.prj_wrd='BJHL'||t5.xmid
where t.dt ='${dmp_day}' 
and t.prj_bsn_tp_cd in ('1F','1B')  
--and (t4.zcly like '行政事业%' OR t4.zcly like '企业资产,国资%' )
-- and t2.ssbm in('央企一部','央企二部','央企三部','央企四部','央企五部','央企六部')
)
-- 增加了机动车类型的资产类型、所属部门逻辑
-- 机动车评估价取取的是tcp_bdxx表里的评估值（left join ods.ods_bjhl_tcp_bdxx m on a.keyid=m.swdyxm）20241009
SELECT 
bsn_prj_wrd,
prj_no,
prj_nm,
CASE WHEN a.prj_bsn_tp_cd = '1B' THEN b.ast_src ELSE a.zcly END AS zcly,
prj_prt_sown_spvs_org_cd,
prj_prt_sown_spvs_org_cd_dsc,
info_publ_start_dt,
tfr_prc,
CASE WHEN a.prj_bsn_tp_cd = '1B' THEN d.evaluation_price ELSE a.eval_prc END AS eval_prc,
bsn_rec_deal_dt,
CASE WHEN a.txn_mth_cd is NULL THEN c.exchange_type ELSE a.txn_mth_cd END AS txn_mth_cd, --交易方式
CASE WHEN a.txn_mth_cd_dsc is NULL THEN c.exchange_type_dsc ELSE a.txn_mth_cd END AS txn_mth_cd_dsc, ----交易方式
deal_amt,
bid_mth_cd,
bid_mth_cd_dsc,
bid_stat_cd,
bid_stat_cd_dsc,
blng_org,
prj_prt_nm,
CASE WHEN a.prj_bsn_tp_cd = '1B' THEN b.prj_blng_dept ELSE a.prj_blng_dept_id END AS prj_blng_dept_id,
prj_blng_dept_nm,
prj_prin_id,
prj_prin_nm,
txn_svc_mber_id,
txn_svc_mber_nm,
prj_stat_cd,
prj_stat_cd_dsc,
prj_tp_cd,
prj_tp_cd_dsc,
is_owned_state,
prj_bsn_tp_cd,
prj_bsn_tp_cd_dsc,
entp_tp,
info_publ_exprt_dt,
is_repeat,
exch,
lit_amt,
ecn_tp_cd,
ecn_tp_cd_dsc
FROM all_data a
LEFT JOIN 
(SELECT rltv_prj,prj_blng_dept,ast_src FROM dwd.dwd_bid_task_list_info WHERE dt ='${dmp_day}') b
ON b.rltv_prj = a.bsn_prj_wrd
LEFT JOIN 
(select project_code,exchange_type,exchange_type_dsc FROM std.std_bjhl_tbid_gpxmxx_d 
WHERE dt = '${dmp_day}') c
ON a.prj_no = c.project_code
LEFT JOIN 
(
SELECT  concat('BJHL',mtrl_obj_crpnd_prj) AS mtrl_obj_crpnd_prj --项目ID 
       ,evaluation_price                  AS evaluation_price --评估价 
FROM std.std_bjhl_tcp_bdxx_d
WHERE dt = '${dmp_day}'
) d 
ON a.bsn_prj_wrd = d.mtrl_obj_crpnd_prj