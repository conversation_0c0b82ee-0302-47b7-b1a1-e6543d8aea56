from impala.dbapi import connect

def get_create_table_statements(tables):
    # 连接数据库
    conn = connect(host='*************', port=10000, user='root', auth_mechanism='PLAIN')
    cursor = conn.cursor()
    
    statements = []
    success_count = 0  # 成功处理的表数量
    
    for table in tables:
        # 获取建表语句
        cursor.execute(f"SHOW CREATE TABLE {table}")
        create_table_statement = "\n".join([row[0] for row in cursor.fetchall()])
        
        # 打印获取到的建表语句用于调试
        print(f"表 {table} 的创建语句:\n{create_table_statement}\n")
        
        # 找到 ROW FORMAT SERDE 之前的内容
        serde_index = create_table_statement.find("ROW FORMAT SERDE")
        print(f"SERDE index for {table}: {serde_index}")  # 调试信息
        
        if serde_index != -1:
            # 截取到 ROW FORMAT SERDE 之前的部分
            truncated_statement = create_table_statement[:serde_index].strip()  # 去掉多余的空格
            
            # 拼接上指定的语句
            final_statement = f"""{truncated_statement}
ROW FORMAT DELIMITED FIELDS TERMINATED BY '\\001'
LINES TERMINATED BY '\\n'
STORED AS ORC;
"""
            statements.append(final_statement)
            success_count += 1  # 成功处理的表计数加一
        else:
            print(f"警告: 在表 {table} 中未找到 ROW FORMAT SERDE。")
    
    # 关闭数据库连接
    cursor.close()
    conn.close()
    
    return statements, success_count

def save_statements_to_file(statements, file_path):
    with open(file_path, 'w') as f:
        for statement in statements:
            # 写入到文件，带调试信息
            print(f"保存语句:\n{statement}")
            f.write(statement + "\n\n")  # 每个建表语句之间用空行隔开

if __name__ == "__main__":
    # 你提供的表名
    tables = [
        'dim.dim_pty_cust',
        'dim.dim_pty_usr',
        'dwd.dwd_prj_fct',
        'dwd.dwd_company_label_info',
        'dwd.dwd_bidder_intention_info',
        'dwd.dwd_evt_deal_rec_fct',
        'dws.dws_reg_users_info',
        'dws.dws_pre_publ_prj_fct',
        'dws.dws_all_trsfer_info',
        'dws.dws_bidder_intention_info',
        'ods.ods_api_full_company_info',
        'ods.ods_bl_pilotreformenterprises_supplement',
        'std.std_api_full_company_info',
        'std.std_bl_pilotreformenterprises_supplement'
    ]
    
    # 获取建表语句
    statements, success_count = get_create_table_statements(tables)
    
    # 保存到文件
    save_statements_to_file(statements, 'create_table_statements.txt')

    # 打印处理结果
    total_tables = len(tables)
    print(f"处理的总表数: {total_tables}")
    print(f"成功处理的表数: {success_count}")
    print(f"处理失败的表数: {total_tables - success_count}")

    print("建表语句已保存到 create_table_statements.txt 文件中。")
