-- Active: 1725412636133@@10.254.99.153@3306@bl

DROP TABLE if exists bl.bl_icap_prj_data;
-- 企业增资项目数据纠正表
CREATE TABLE bl.bl_icap_prj_data (
    proj_no VARCHAR(255) COMMENT '项目编号',
    proj_name VARCHAR(255) COMMENT '项目名称',
    deal_date VARCHAR(50) COMMENT '成交日期',
    investor_name VARCHAR(255) COMMENT '投资人名称',
    financing_amt DECIMAL(24, 6) COMMENT '融资金额（万元）',
    single_investor_amt DECIMAL(24, 6) COMMENT '单个投资人增资金额（万元）',
    investor_type VARCHAR(255) COMMENT '投资人类型',
    is_mixed_ownership VARCHAR(50) COMMENT '是否混改',
    mix_own_type VARCHAR(255) COMMENT '混改类型',
    foreign_reg VARCHAR(50) COMMENT '融资方所在地区',
    is_related VARCHAR(50) COMMENT '是否涉及债券转让/土地使用权/技术资产',
    bond_amt DECIMAL(24, 6) COMMENT '涉及债券转让金额',
    land_use_amt DECIMAL(24, 6) COMMENT '涉及土地使用权金额',
    tech_asset_amt DECIMAL(24, 6) COMMENT '涉及技术资产金额',
    project_manager VARCHAR(255) COMMENT '项目负责人',
    udt_user VARCHAR(255) COMMENT '更新人',
    udt_tm TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业增资项目数据纠正表';

desc bl.bl_icap_prj_data;

DROP TABLE if exists bl.bl_eqty_prj_data;

-- 产权转让项目数据纠正表
CREATE TABLE bl.bl_eqty_prj_data (
    proj_no VARCHAR(255) COMMENT '项目编号',
    proj_name VARCHAR(255) COMMENT '项目名称',
    deal_date VARCHAR(50) COMMENT '成交日期',
    proj_category VARCHAR(50) COMMENT '项目类别（混改/压减/其他）',
    mix_own_type VARCHAR(255) COMMENT '混改类型',
    red_type VARCHAR(255) COMMENT '压减类型',
    is_related VARCHAR(50) COMMENT '是否涉及债券转让/土地使用权/技术资产',
    bond_amt DECIMAL(24, 6) COMMENT '涉及债券转让金额',
    land_use_amt DECIMAL(24, 6) COMMENT '涉及土地使用权金额',
    tech_asset_amt DECIMAL(24, 6) COMMENT '涉及技术资产金额',
    project_manager VARCHAR(255) COMMENT '项目负责人',
    investor_name VARCHAR(255) COMMENT '投资人名称',
    investor_type VARCHAR(255) COMMENT '投资人类型',
    udt_user VARCHAR(255) COMMENT '更新人',
    udt_tm TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产权转让项目数据纠正表';




