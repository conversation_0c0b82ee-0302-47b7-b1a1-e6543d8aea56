select a.prj_id,
       a.bsn_prj_wrd,
       a.prj_wrd,
       0 as zrffwf,
       0 as srffwf,
       b.fwfze,
       b.bjssr
from dwd.dwd_prj_fct a
         left join (select ordr_prj_no,

                           sum(case when cptl_type_smlcls = '服务费划出' then amt else 0 end) as fwfze,
                           sum(case
                                   when bsn_sbj in
                                        ('12664', '12665', '12666', '12676', '12689', '60311', '60312','60313', '60314') and
                                        crj_flag = '出金' then amt
                                   else 0 end)                                                as bjssr
                    from dwd.dwd_trans_setl_fct
                    where dt = '${dmp_day}'
                    group by ordr_prj_no) b on a.prj_id = b.ordr_prj_no
where a.dt = '${dmp_day}'
  and b.ordr_prj_no is not null
  and a.prj_bsn_tp_cd in ('1F', '1B')