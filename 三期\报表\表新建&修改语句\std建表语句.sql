-- Active: 1725412082987@@10.254.99.145@10000

DROP TABLE IF EXISTS std.std_bl_icap_prj_data_d;
-- 企业增资项目数据纠正表
CREATE TABLE std.std_bl_icap_prj_data_d (
    proj_no STRING COMMENT '项目编号',
    proj_name STRING COMMENT '项目名称',
    deal_date STRING COMMENT '成交日期',
    investor_name STRING COMMENT '投资人名称',
    financing_amt DECIMAL(24,6) COMMENT '融资金额（万元）/投资资金总额合计数',
    single_investor_amt DECIMAL(24,6) COMMENT '单个投资人增资金额（万元）',
    investor_type STRING COMMENT '投资人类型',
    mix_own_type STRING COMMENT '混改类型',
    foreign_reg STRING COMMENT '融资方所在地区',
    land_use_amt DECIMAL(24,6) COMMENT '涉及土地使用权金额',
    tech_asset_amt DECIMAL(24,6) COMMENT '涉及技术资产金额',
    project_manager STRING COMMENT '项目负责人',
    is_municipal_ent STRING COMMENT '是否为市管企业',
    transparency STRING COMMENT '公开/非公开',
    trans_type STRING COMMENT '交易类型',
    cap_inc_purpose STRING COMMENT '增资目的',
    group_name STRING COMMENT '所属集团',
    cap_inc_company STRING COMMENT '增资企业',
    listing_cap_inc_ratio STRING COMMENT '挂牌增资比例/股数',
    pre_cap_inc_nature STRING COMMENT '增资前标的企业性质',
    pre_cap_inc_shareholders STRING COMMENT '增资前的股东',
    pre_municipal_state_owned_ratio STRING COMMENT '增资前全部国有股东持股比例（仅含市国资委监管国有企业股东）',
    pre_all_state_owned_ratio STRING COMMENT '增资前全部国有股东持股比例（含其他国资监管机构下属企业股东）',
    cap_inc_company_region STRING COMMENT '增资企业所在地区',
    cap_inc_company_industry STRING COMMENT '增资企业所属行业',
    listing_date STRING COMMENT '挂牌时间',
    info_end_date STRING COMMENT '信息披露期满日期',
    listing_duration STRING COMMENT '挂牌持续时间总计（工作日）',
    target_funds_amt STRING COMMENT '拟募集资金金额（万元）',
    qualified_investors_num STRING COMMENT '征集合格意向投资人个数',
    deal_method STRING COMMENT '成交方式',
    single_investor_reg_cap_amt DECIMAL(24,6) COMMENT '单个投资人增加注册资本金额（万元）',
    investor_industry STRING COMMENT '投资人所属行业',
    investor_state_owned_regulator STRING COMMENT '投资人为其他国有企业的，其所属国资监管机构（央企、其他地方国资）',
    total_new_capital DECIMAL(24,6) COMMENT '新增出资资本合计数',
    assessment_baseline_date STRING COMMENT '评估基准日',
    total_assets DECIMAL(24,6) COMMENT '资产总额（万元）',
    total_liabilities DECIMAL(24,6) COMMENT '负债总额（万元）',
    equity DECIMAL(24,6) COMMENT '所有者权益（万元）',
    company_assets_assessment DECIMAL(24,6) COMMENT '企业资产总额评估值（万元）',
    target_equity_assessment DECIMAL(24,6) COMMENT '标的企业净资产评估值（万元）',
    project_progress STRING COMMENT '项目进展',
    post_project_shareholders STRING COMMENT '项目完成后的股东',
    post_project_shareholders_ratio STRING COMMENT '项目完成后的股东持股比例',
    post_municipal_state_owned_ratio STRING COMMENT '项目完成后全部国有股东持股比例（%）（仅含市国资委监管国有企业股东）',
    post_all_state_owned_ratio STRING COMMENT '项目完成后全部国有股东持股比例（%）（含其他国资监管机构下属企业股东）',
    post_project_nature STRING COMMENT '项目完成后标的企业性质',
    cap_inc_plan STRING COMMENT '增资方案',
    approval_date STRING COMMENT '批准日期',
    esop STRING COMMENT '股权激励/员工持股',
    financing STRING COMMENT '融资',
    reorg STRING COMMENT '集团内部重组/股权结构调整/所出资企业直接或指定其控股、实际控制的其他子企业参与增资/债转股',
    budget STRING COMMENT '国有资本经营预算拨款',
    equity_to_capital STRING COMMENT '所有者权益转增资本',
    original_shareholder_increase STRING COMMENT '原股东增资',
    state_owned_asset_regulator STRING COMMENT '国资监管机构',
    regulator_location STRING COMMENT '监管地区',
    prj_blng_dept STRING COMMENT '项目所属部门',
    audit_year STRING COMMENT '审计年度',
    pre_shr_ratio STRING COMMENT '增资前的股东持股比例',
    biz_income DECIMAL(24,6) COMMENT '营业收入',
    biz_profit DECIMAL(24,6) COMMENT '营业利润',
    net_profit DECIMAL(24,6) COMMENT '净利润',
    total_assets_amt DECIMAL(24,6) COMMENT '资产总额',
    total_liab_amt DECIMAL(24,6) COMMENT '负债总额',
    owner_equity DECIMAL(24,6) COMMENT '所有者权益',
    biz_income_1 DECIMAL(24,6) COMMENT '营业收入',
    biz_profit_1 DECIMAL(24,6) COMMENT '营业利润',
    net_profit_1 DECIMAL(24,6) COMMENT '净利润1',
    eqty_appr_rate DECIMAL(15,6) COMMENT '增资股权增值率',
    muni_soe_ratio_chg STRING COMMENT '全部国有股东持股比例变动情况 （仅含市国资委监管国有企业股东）',
    all_soe_ratio_chg STRING COMMENT '全部国有股东持股比例变动情况 （含其他国资监管机构下属企业股东）',
    strtg_invst_rsn STRING COMMENT '引入战略投资人/引入特殊资质股东/经同级国有资产监督管理机构批准',
    is_new_shr_cap_inc STRING COMMENT '是否涉及新股东增资',
    ent_level STRING COMMENT '企业层级',
    udt_user STRING COMMENT '更新人',
    udt_tm TIMESTAMP COMMENT '更新时间'
)
COMMENT '企业增资项目数据纠正表-补录'
PARTITIONED BY (dt STRING COMMENT '日期分区')
ROW FORMAT DELIMITED FIELDS TERMINATED BY '\001'
LINES TERMINATED BY '\n'
STORED AS ORC;



DROP TABLE if exists std.std_bl_eqty_prj_data_d;

-- 产权转让项目数据纠正表
CREATE TABLE std.std_bl_eqty_prj_data_d (
    proj_no STRING COMMENT '项目编号',
    proj_name STRING COMMENT '项目名称',
    deal_date STRING COMMENT '成交日期',
    proj_category STRING COMMENT '项目类别（混改/压减/其他）',
    mix_own_type STRING COMMENT '混改类型',
    red_type STRING COMMENT '压减类型',
    is_related STRING COMMENT '是否涉及债权转让/土地使用权/技术资产',
    bond_amt DECIMAL(24, 6) COMMENT '涉及债权转让金额',
    land_use_amt DECIMAL(24, 6) COMMENT '涉及土地使用权金额',
    tech_asset_amt DECIMAL(24, 6) COMMENT '涉及技术资产金额',
    project_manager STRING COMMENT '项目负责人',
    investor_name STRING COMMENT '投资人名称',
    investor_type STRING COMMENT '投资人类型',
    state_owned_asset_regulator STRING COMMENT '国资监管机构',
    regulator_location STRING COMMENT '监管机构属地',
    udt_user STRING COMMENT '更新人',
    udt_tm TIMESTAMP COMMENT '更新时间'
)
COMMENT '产权转让项目数据纠正表-补录'
PARTITIONED BY (dt STRING COMMENT '日期分区')
ROW FORMAT DELIMITED FIELDS TERMINATED BY '\001'
LINES TERMINATED BY '\n'
STORED AS ORC 
;


CREATE TABLE std.std_bjhl_tcgq_zzzsgpxm_t_gdczcg_d(
  `id` decimal(16,0) COMMENT 'ID', 
  `gdmc` string COMMENT 'GDMC', 
  `cgbl` decimal(11,6) COMMENT 'CGBL', 
  `tcgq_zzzsgpxm_id` decimal(16,0) COMMENT 'TCGQ_ZZZSGPXM_ID', 
  `qtgd` decimal(12,0) COMMENT 'QTGD', 
  `ygdzczb` decimal(15,6) COMMENT 'YGDZCZB')
PARTITIONED BY ( 
  `dt` string)
ROW FORMAT DELIMITED FIELDS TERMINATED BY '\001'
LINES TERMINATED BY '\n'
STORED AS ORC

-- 工作日标记表
 CREATE TABLE `std.std_bjhl_tbid_jyr_d`(
      `rq` decimal(8,0) COMMENT '日期', 
      `jyrbs` decimal(12,0) COMMENT '是否工作日', 
      `cshsj` string COMMENT 'CSHSJ', 
      `spsj` string COMMENT 'SPSJ')
      PARTITIONED BY ( 
      `dt` string)
      ROW FORMAT DELIMITED FIELDS TERMINATED BY '\001'
      LINES TERMINATED BY '\n'
      STORED AS ORC   
