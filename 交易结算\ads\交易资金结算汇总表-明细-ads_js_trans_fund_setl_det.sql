SELECT 
K.proj_no,	--项目编号
K.proj_name,	--项目名称
K.proj_type,	--业务类型
K.cptl_lo,	--资金位置
K.payee_payer_name,	--收款方/付款方名称
K.col_pay_agent_name AS col_pay_agent_name,	--代收方/代付方名称
K.deposit_amt,	--入金发生金额（元）
regexp_replace(K.deposit_date, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS deposit_date,	--入金日期
K.withdrawal_amt,	--出金发生金额（元）
regexp_replace(K.withdrawal_date, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS withdrawal_date,	--出金日期
K.business_center,	--业务中心
K.proj_belong_dept_name,	--业务部门
K.proj_princ_name,	--业务负责人
B.is_bank,	--出入金银行不一致
K.fund_type,	--资金类型
regexp_replace(K.pay_success_date, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS trans_date,	--支付成功日期
regexp_replace(K.bank_toacct_dt, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS bank_toacct_dt,	--银行到账日期
K.ordr_no AS order_no, --订单编号
regexp_replace(K.order_creat_dt, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') AS order_creat_dt,	--订单创建日期
K.ext_ordr_no AS ext_ordr_no, --机构订单号
K.py_aplc_no AS py_aplc_no-- 支付申请号
FROM 
(
SELECT  
A.ordr_no, --订单编号
ordr_prj_no AS proj_no,	--订单项目编号
A.ordr_prj_name AS proj_name,	--订单项目名称
bsn_type AS proj_type,	--业务类型
cptl_lo	AS cptl_lo, --资金位置
cust_name AS payee_payer_name, --客户名称,收款方/付款方名称
sum(CASE WHEN crj_flag = '入金' THEN amt ELSE 0 END) AS deposit_amt, --入金金额
max(CASE WHEN crj_flag = '入金' THEN pay_success_date ELSE '' END) AS deposit_date, --入金日期
sum(CASE WHEN crj_flag = '出金' THEN amt ELSE 0 END) AS withdrawal_amt, --出金金额
max(CASE WHEN crj_flag = '出金' THEN pay_success_date ELSE '' END) AS withdrawal_date, --入金日期
pay_success_date AS pay_success_date,	--支付成功日期
bank_to_acc_date AS bank_toacct_dt,  -- 银行到账日期
prj_optr_org AS business_center, --业务中心
prj_optr_dept AS proj_belong_dept_name, --业务部门
prj_optr AS proj_princ_name, --业务负责人
col_pay_agent_name, --代付方
cptl_type_smlcls AS fund_type, --资金类型 （资金小类）
order_creat_dt, --订单创建时间
ext_ordr_no, --机构订单号
py_aplc_no -- 支付申请号
FROM dws.dws_trans_setl_info A
WHERE A.DT = ${dmp_day} 
AND A.amt != 0
AND coalesce(A.setl_type,'-') != '场外结算'
-- AND `cptl_type_lrgcls` != '退票' -- 资金大类
--AND `cptl_type_lrgcls` != '利息相关'
--2.当订单大类=服务费和分佣时，只取项目类型为珍品、房屋出租、小宗实物和机动车的记录 
--9.项目类型为诉讼，订单大类=服务费，入金，订单小类为服务费交纳，订单类型为受让方服务费的订单类型纳入计算
--10.当项目类型为诉讼，订单大类=分佣，出金，订单小类为服务费划出的订单类型纳入计算
AND (
cptl_type_lrgcls NOT IN ('服务费','分佣')
OR CONCAT(cptl_type_lrgcls,bsn_type) IN ('服务费珍品','服务费房屋出租','服务费小宗实物','服务费机动车',
'分佣珍品','分佣房屋出租','分佣小宗实物','分佣机动车')
OR CONCAT(cptl_type_lrgcls,bsn_type,ast_type,cptl_type_smlcls) IN ('服务费诉讼资产受让方服务费服务费交纳')
OR CONCAT(cptl_type_lrgcls,bsn_type,ast_type,cptl_type_smlcls) IN ('分佣诉讼资产中台出金服务费划出')
)
--3.当订单小类=保证金转价款时，所有记录都不统计  
--5.当订单小类=保证金抵扣服务费（服务费入金）时，均不统计
--7.当订单小类=价款抵扣服务费（服务费入金）时，均不统计
AND cptl_type_smlcls NOT IN  ('保证金抵扣服务费（服务费入金）','价款抵扣服务费（服务费入金）')
AND substr(cptl_type_smlcls,1,6) != '保证金转价款'
--4.当订单小类=保证金抵扣服务费（保证金出金）时，仅取项目类型为企业增资的记录，房屋出租的不统计
AND (
	cptl_type_smlcls != '保证金抵扣服务费（保证金出金）'
	OR concat(cptl_type_smlcls,bsn_type) = '保证金抵扣服务费（保证金出金）企业增资'
)
--6.当订单小类=价款抵扣服务费（价款出金）时，仅取项目类型为产权转让和大宗实物的记录，房屋出租的不统计  
AND (
	cptl_type_smlcls != '价款抵扣服务费（价款出金）'
	OR concat(cptl_type_smlcls,bsn_type) in ('价款抵扣服务费（价款出金）产权转让','价款抵扣服务费（价款出金）大宗实物')
)
GROUP BY A.ordr_no,A.ordr_prj_no,ordr_prj_name,bsn_type,cptl_lo,
cust_name,pay_success_date,bank_to_acc_date,prj_optr_org,prj_optr_dept,prj_optr,cptl_type_smlcls,col_pay_agent_name,order_creat_dt,ext_ordr_no,py_aplc_no
) K
LEFT JOIN (
	--出入金银行是否一致
	SELECT 
	ordr_prj_no,
	CASE 
	   WHEN COUNT(DISTINCT case when cptl_lo is null then '北交所' else cptl_lo end) = 1 THEN '一致'
	   ELSE '不一致' END AS is_bank -- 出入金银行不一致
	FROM dws.dws_trans_setl_info
  WHERE DT = ${dmp_day} 
  AND amt != 0
  AND coalesce(setl_type,'-') != '场外结算'
  --AND cptl_type_lrgcls = '保证金' 
  -- AND `cptl_type_lrgcls` != '退票' 
  --AND `cptl_type_lrgcls` != '利息相关'
  --2.当订单大类=服务费和分佣时，只取项目类型为珍品、房屋出租、小宗实物和机动车的记录 
  --9.项目类型为诉讼，订单大类=服务费，入金，订单小类为服务费交纳，订单类型为受让方服务费的订单类型纳入计算
  --10.当项目类型为诉讼，订单大类=分佣，出金，订单小类为服务费划出的订单类型纳入计算
	AND (
	cptl_type_lrgcls NOT IN ('服务费','分佣')
	OR CONCAT(cptl_type_lrgcls,bsn_type) IN ('服务费珍品','服务费房屋出租','服务费小宗实物','服务费机动车',
	'分佣珍品','分佣房屋出租','分佣小宗实物','分佣机动车')
  OR CONCAT(cptl_type_lrgcls,bsn_type,ast_type,cptl_type_smlcls) IN ('服务费诉讼资产受让方服务费服务费交纳')
  OR CONCAT(cptl_type_lrgcls,bsn_type,ast_type,cptl_type_smlcls) IN ('分佣诉讼资产中台出金服务费划出')
	)
	--3.当订单小类=保证金转价款时，所有记录都不统计  
	--5.当订单小类=保证金抵扣服务费（服务费入金）时，均不统计
	--7.当订单小类=价款抵扣服务费（服务费入金）时，均不统计
	AND cptl_type_smlcls NOT IN  ('保证金抵扣服务费（服务费入金）','价款抵扣服务费（服务费入金）')
  AND substr(cptl_type_smlcls,1,6) != '保证金转价款'
	--4.当订单小类=保证金抵扣服务费（保证金出金）时，仅取项目类型为企业增资的记录，房屋出租的不统计
	AND (
		cptl_type_smlcls != '保证金抵扣服务费（保证金出金）'
		OR concat(cptl_type_smlcls,bsn_type) = '保证金抵扣服务费（保证金出金）企业增资'
	)
	--6.当订单小类=价款抵扣服务费（价款出金）时，仅取项目类型为产权转让和大宗实物的记录，房屋出租的不统计  
	AND (
		cptl_type_smlcls != '价款抵扣服务费（价款出金）'
		OR concat(cptl_type_smlcls,bsn_type) in ('价款抵扣服务费（价款出金）产权转让','价款抵扣服务费（价款出金）大宗实物')
	)
	GROUP BY ordr_prj_no
) B
ON K.proj_no = B.ordr_prj_no
UNION ALL
-- 线下出金数据
SELECT  project_code             AS proj_no --项目编号 
       ,project_name             AS proj_name --项目名称 
       ,depst_ordr_id            AS depst_ordr_id --订单号 
       ,CASE project_type  
	    WHEN 'OA'  THEN '珍品'
		WHEN '1B'  THEN '机动车'
		WHEN '1C'  THEN '企业增资'
		WHEN '1D'  THEN '大宗实物'
		WHEN '1E'  THEN '诉讼资产'
		WHEN '1F'  THEN '小宗实物'
		WHEN '1G'  THEN '房屋出租'
		WHEN '2C'  THEN '企业融资'
		WHEN 'FC'  THEN '房产'
		WHEN 'FP'  THEN '司法拍卖'
		WHEN 'FY'  THEN '诉讼资产-法院'
		WHEN 'GQ'  THEN '产权转让'
		WHEN 'JP'  THEN '京牌小客车'
		WHEN 'PC'  THEN '诉讼资产-破产'
		WHEN 'QT'  THEN '诉讼资产-其他'
		WHEN 'Z1'  THEN '招商'
		WHEN 'ZP'  THEN '房屋租赁'
		WHEN 'ZQ'  THEN '债权转让'
		WHEN 'TJ'  THEN '项目推介'
		WHEN 'TZ'  THEN '投资意向'
		WHEN 'TY'  THEN '体育'
		WHEN 'H1'  THEN '混改意向'
		ELSE NULL
		END            			 AS proj_type --业务类型 
       ,'北交所'                  AS cptl_lo --资金位置 
       ,''                       AS payee_payer_name --收款方/付款方名称 
       ,''                       AS col_pay_agent_name --代收方/代付方名称 
       ,''                       AS deposit_amt --入金发生金额（元） 
       ,''                       AS deposit_date --入金日期 
       ,offline_withdr_amt_yuan  AS withdrawal_amt --线下出金金额（元） 
       ,offline_withdr_time      AS withdrawal_date --出金日期 
       ,''                       AS business_center --业务中心 
	   ,prj_blng_dept            AS busi_dept --业务部门
	   ,prj_pnp                  AS proj_princ_name --业务负责人
	   ,CASE WHEN b.ordr_prj_no IS NULL THEN '一致' ELSE '不一致' END AS is_bank -- 出入金银行不一致
	   ,'线下出金'                AS fund_type --资金类型（资金小类）
       ,offline_withdr_time      AS trans_date --线下出金时间/支付成功日期 
       ,offline_withdr_time      AS bank_toacct_dt --线下出金时间/银行到账日期 
       ,offline_withdr_time      AS order_creat_dt --线下出金时间/订单创建日期
	   ,NULL 					 AS ext_ordr_no -- 机构订单号
	   ,NULL         			 AS py_aplc_no-- 支付申请号
FROM std.std_bjhl_tbid_lsxxcjjl_d a
LEFT JOIN 
(
	SELECT 
		DISTINCT ordr_prj_no
		FROM dws.dws_trans_setl_info 
	WHERE dt = '${dmp_day}' AND cptl_lo = '北京结算' AND amt != 0
) b
ON a.project_code = b.ordr_prj_no
WHERE a.dt = '${dmp_day}' 
AND a.offline_withdr_amt_yuan != 0