SELECT  a.deal_rec_id --成交ID 
       ,C.ORDER_NO                                    AS ORDR_NO -- 订单号 
       ,b.prj_id                                      AS ORDR_PRJ_NO -- 项目编号 
       ,b.prj_nm                                      AS ORDR_PRJ_NAME -- 项目名称 
       ,b.oprtr_nm                                    AS PRJ_OPTR -- 经办人名称 
       ,b.handl_dept_nm                               AS PRJ_OPTR_DEPT -- 经办部门名称 
       ,b.handl_bus_dep_nm                            AS PRJ_OPTR_ORG -- 业务中心 
       ,NULL                                          AS CUST_NO -- 客户号 
       ,NULL                                          AS CUST_NAME -- 客户名称 
       ,'保证金转价款'                                  AS AST_TYPE -- 资产类型 
       ,'企业增资'                                     AS BSN_TYPE -- 业务类型 
       ,'出金'                                         AS CRJ_FLAG -- 出入金标识 
       ,'保证金'                                       AS CPTL_TYPE_LRGCLS -- 资金类型大类 
       ,'保证金转价款（保证金出金）'                      AS CPTL_TYPE_SMLCLS -- 资金类型小类 
       ,A.MRGN_TF_PRC_AMT                             AS AMT -- 发生金额 
       ,A.curr_cd_dsc                                 AS CCY -- 币种 
       ,NULL                                          AS PAY_MODE -- 支付方式 
       ,replace(substr(a.Mrgn_Tf_Prc_Tm,1,10),'-','') AS PAY_SUCCESS_DATE -- 支付成功时间 
       ,NULL                                          AS col_pay_agent_name -- 代付方 
       ,C.SETL_BANK                                   AS SETL_BANK -- 结算银行 
       ,C.CPTL_LO                                     AS CPTL_LO -- 资金位置 
       ,replace(substr(a.Mrgn_Tf_Prc_Tm,1,10),'-','') AS BANK_TO_ACC_DATE -- 银行到账时间 
       ,'场内结算'                                      AS SETL_TYPE -- 结算方式 
       ,'是'                                           AS IS_VIRTUAL_ORDER -- 是否虚拟订单 
       ,NULL                                          AS order_creat_dt
FROM DWD.DWD_EVT_DEAL_REC_FCT A
LEFT JOIN DWD.DWD_PRJ_FCT B
ON A.PRJ_WRD = B.PRJ_WRD AND A.DT = B.DT
LEFT JOIN
(
	SELECT  ORDER_NO
	       ,SETL_BANK
	       ,CASE WHEN SETL_BANK LIKE '%BJDJJS%' THEN '北京结算'  ELSE '北交所' END AS CPTL_LO -- 资金位置
	FROM STD.STD_BJHL_TBID_ZFDD
	WHERE DT = '${dmp_day}' 
) C
ON A.MRGN_TF_PRC_ORDER_ID = C.ORDER_NO
WHERE A.IS_MRGN_OF_TXN_PRC = 1
AND A.DT = '${dmp_day}'
AND B.PRJ_ID IS NOT NULL
AND A.MRGN_TF_PRC_AMT != 0 -- 当交易金额为0时不虚拟订单 
AND A.MRGN_TF_PRC_AMT IS NOT NULL
AND B.prj_bsn_tp_cd = '1C' 
UNION ALL
SELECT  a.deal_rec_id --成交ID 
       ,C.ORDER_NO                                    AS ORDR_NO -- 订单号 
       ,b.prj_id                                      AS ORDR_PRJ_NO -- 项目编号 
       ,b.prj_nm                                      AS ORDR_PRJ_NAME -- 项目名称 
       ,b.oprtr_nm                                    AS PRJ_OPTR -- 经办人名称 
       ,b.handl_dept_nm                               AS PRJ_OPTR_DEPT -- 经办部门名称 
       ,b.handl_bus_dep_nm                            AS PRJ_OPTR_ORG -- 业务中心 
       ,NULL                                          AS CUST_NO -- 客户号 
       ,NULL                                          AS CUST_NAME -- 客户名称 
       ,'保证金转价款'                                  AS AST_TYPE -- 资产类型 
       ,'企业增资'                                     AS BSN_TYPE -- 业务类型 
       ,'入金'                                         AS CRJ_FLAG -- 出入金标识 
       ,'价款'                                         AS CPTL_TYPE_LRGCLS -- 资金类型大类 
       ,'保证金转价款（价款入金）'                        AS CPTL_TYPE_SMLCLS -- 资金类型小类 
       ,A.MRGN_TF_PRC_AMT                             AS AMT -- 发生金额 
       ,A.curr_cd_dsc                                 AS CCY -- 币种 
       ,NULL                                          AS PAY_MODE -- 支付方式 
       ,replace(substr(a.Mrgn_Tf_Prc_Tm,1,10),'-','') AS PAY_SUCCESS_DATE -- 支付成功时间 
       ,NULL                                          AS col_pay_agent_name -- 代付方 
       ,C.SETL_BANK                                   AS SETL_BANK -- 结算银行 
       ,C.CPTL_LO                                     AS CPTL_LO -- 资金位置 
       ,replace(substr(a.Mrgn_Tf_Prc_Tm,1,10),'-','') AS BANK_TO_ACC_DATE -- 银行到账时间 
       ,'场内结算'                                     AS SETL_TYPE -- 结算方式 
       ,'是'                                           AS IS_VIRTUAL_ORDER -- 是否虚拟订单 
       ,NULL                                          AS order_creat_dt
FROM DWD.DWD_EVT_DEAL_REC_FCT A
LEFT JOIN DWD.DWD_PRJ_FCT B
ON A.PRJ_WRD = B.PRJ_WRD AND A.DT = B.DT
LEFT JOIN
(
	SELECT  ORDER_NO
	       ,SETL_BANK
	       ,CASE WHEN SETL_BANK LIKE '%BJDJJS%' THEN '北京结算'  ELSE '北交所' END AS CPTL_LO -- 资金位置 AS YW_ID 
	FROM STD.STD_BJHL_TBID_ZFDD
	WHERE DT = '${dmp_day}' 
) C
ON A.MRGN_TF_PRC_ORDER_ID = C.ORDER_NO
WHERE A.IS_MRGN_OF_TXN_PRC = 1
AND A.DT = '${dmp_day}'
AND B.PRJ_ID IS NOT NULL
AND A.MRGN_TF_PRC_AMT != 0 -- 当交易金额为0时不虚拟订单 
AND A.MRGN_TF_PRC_AMT IS NOT NULL
AND B.prj_bsn_tp_cd = '1C' 