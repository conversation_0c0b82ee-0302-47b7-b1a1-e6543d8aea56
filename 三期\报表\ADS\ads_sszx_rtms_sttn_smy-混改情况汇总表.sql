WITH data_zz AS (
  -- 增资明细
  SELECT
    DISTINCT
    '企业增资'                                AS bus_type,      -- 业务类型 
    T.trans_type                            AS transparency,  -- 公开/非公开 
    B.buyer_name                          AS investor,              -- 投资人 
    B.project_code                        AS project_no,       -- 项目编号
    B.project_name                        AS project_name,      -- 项目名称
    substr(B.`deal_date`,0,7)               AS deal_date,     -- 成交日期 
    CASE WHEN 
        A.investor_type IS NOT NULL THEN A.investor_type 
      ELSE B.investor_economy_type   
    END                                     AS investor_type, -- 投资人类型 
    CASE 
      WHEN B.`investor_site_prov` IN ('天津市','河北省') 
        THEN '津冀地区'  
        ELSE '其他地区' 
    END                                     AS investor_region, -- 投资人所在地区 
    A.`single_investor_cap_inc_amt`         AS total_amt,     -- 交易总金额
    A.`mix_own_type`                        AS prj_lbl     -- 混改类型 
  FROM dwd.dwd_entp_incptl_trsfer_ext_d A -- 增资交易汇总_扩展表
  LEFT JOIN dws.dws_entp_incptl_trsfer_d B -- 增资交易汇总表
  ON A.project_code = B.project_code
  AND A.buyer_name = B.buyer_name
  AND A.dt = B.dt
  LEFT JOIN (
      SELECT DISTINCT
          a.project_code,
          a.project_name,
          a.deal_date,
          CASE 
              WHEN b.project_count > 1 THEN '同时进行'
              ELSE 
                  CASE 
                      WHEN a.project_code LIKE 'G6%' THEN '公开'
                      WHEN a.project_code LIKE 'G7%' OR a.project_code LIKE 'G8%' THEN '非公开'
                  END
          END AS trans_type
      FROM dws.dws_entp_incptl_trsfer_d a
      LEFT JOIN (
          SELECT 
              project_name,
              deal_date,
              COUNT(project_t) AS project_count
          FROM (
              SELECT DISTINCT 
                  project_name,
                  deal_date,
                  CASE 
                      WHEN project_code LIKE 'G6%' THEN '公开'
                      WHEN project_code LIKE 'G7%' OR project_code LIKE 'G8%' THEN '非公开' 
                  END AS project_t
              FROM dws.dws_entp_incptl_trsfer_d
              WHERE dt = '${dmp_day}'
          ) b1
          GROUP BY project_name, deal_date
      ) b 
      ON a.project_name = b.project_name 
      AND a.deal_date = b.deal_date
      WHERE a.dt = '${dmp_day}'
  ) T -- 判断是否同时进行
  ON B.project_code = T.project_code
  AND B.deal_date = T.deal_date
  WHERE A.dt = '${dmp_day}'
  AND A.`custd_org_depdc_prov` LIKE '110%' -- 北京市(包括北京市各区)
  AND B.`fincer_oasset_reg_org` = '省级国资委监管'
  AND B.prj_blng_dept REGEXP '市属'
  AND B.`deal_date` IS NOT NULL
  AND A.mix_own_type IS NOT NULL 
  AND A.mix_own_type != ''
),
data_cq AS (
  -- 主查询（非联合体部分）
SELECT DISTINCT
    '产权转让'                 AS bus_type,      -- 业务类型
    A.project_status      AS transparency,  -- 公开/非公开
    A.project_no          AS project_no,       -- 项目编号
    substr(A.transaction_date,0,7)        AS deal_date,     -- 成交日期
    B.investor_type       AS investor_type, -- 投资人类型
    A.assignee_name                AS assignee_name,   -- 受让方名称
    CASE 
      WHEN A.asassignee_province IN ('天津市','河北省') 
        THEN '津冀地区'  
        ELSE '其他地区' 
    END                   AS investor_region, -- 投资人所在地区
    A.transaction_amount  AS total_amt,     -- 交易总金额
    B.mix_own_type        AS prj_lbl     -- 混改类型
FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_eqty_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) B -- 产权转让项目数据纠正表-补录 
ON A.project_no = B.proj_no AND A.dt = B.dt
WHERE A.dt = '${dmp_day}'
AND COALESCE(B.state_owned_asset_regulator, A.transferor_regulatory) = '省级国资委监管'
AND A.transferor_area_code LIKE '110%'
AND A.department REGEXP '市属'
AND A.transaction_date IS NOT NULL
AND B.proj_category = '混改'

UNION ALL

-- 联合体成员部分
SELECT DISTINCT
    '产权转让'                 AS bus_type,      -- 业务类型
    A.project_status      AS transparency,  -- 公开/非公开
    A.project_no          AS project_no,       -- 项目编号
    substr(A.transaction_date,0,7)        AS deal_date,     -- 成交日期
    B.investor_type       AS investor_type, -- 投资人类型
    A.assignee_name                AS assignee_name,   -- 受让方名称
    CASE 
      WHEN A.asassignee_province IN ('天津市','河北省') 
        THEN '津冀地区'  
        ELSE '其他地区' 
    END                   AS investor_region, -- 投资人所在地区
    (C.fnl_buy_pct/100) * A.transaction_amount AS total_amt, -- 交易总金额(按比例计算)
    B.mix_own_type        AS prj_lbl     -- 混改类型
FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
LEFT JOIN (
    SELECT  'BJHL'||cj.prj||'CQZR' AS bsn_prj_wrd -- 业务项目关键字
           ,cj.buyer -- 受买方ID
           ,cj.buyer_nm -- 受让方名称/联合体代表名称
           ,tyc.mbr_nm -- 联合体成员体名称
           ,ty.is_unite_buy -- 是否联合受让方
           ,tyc.fnl_buy_pct -- 最终受让比例
    FROM std.std_bjhl_tcqzr_yxsrf_cy_d tyc
    LEFT JOIN std.std_bjhl_tcqzr_yxsrfxx_d ty
    ON tyc.buyer_id = ty.id AND tyc.dt = ty.dt
    LEFT JOIN std.std_bjhl_tcqzr_cjjlxx_d cj
    ON cj.buyer = ty.id AND cj.dt = ty.dt
    WHERE cj.prj is not null
    AND ty.is_unite_buy = 1
    AND tyc.fnl_buy_pct IS NOT NULL
    AND tyc.dt = '${dmp_day}' 
) C
ON A.bsn_prj_wrd = C.bsn_prj_wrd AND A.assignee_name = C.buyer_nm
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_eqty_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) B -- 产权转让项目数据纠正表-补录 
ON A.project_no = B.proj_no AND C.mbr_nm = B.investor_name AND A.dt = B.dt
WHERE A.dt = '${dmp_day}'
AND COALESCE(B.state_owned_asset_regulator, A.transferor_regulatory) = '省级国资委监管'
AND A.transferor_area_code LIKE '110%'
AND A.department REGEXP '市属'
AND A.transaction_date IS NOT NULL
AND A.is_unite_buy = 1
AND C.fnl_buy_pct IS NOT NULL
AND B.proj_category = '混改'
)
SELECT  `bus_type`                                                          AS bus_type -- 业务类型 
       ,`deal_date`                                                         AS deal_date -- 成交日期
       ,CASE 
          WHEN transparency = '同时进行' THEN count(distinct project_name)
          ELSE count(distinct project_no)
        END AS deal_qty -- 成交数量 
       ,SUM(case WHEN investor_type = '市属国有' THEN 1 else 0 end)          AS st_own_loc_qty -- 国有市属国企数量 
       ,SUM(case WHEN investor_type = '市属国有' THEN total_amt else 0 end)  AS st_own_loc_amt -- 国有市属国企金额 
       ,SUM(case WHEN investor_type = '央属国有' THEN 1 else 0 end)             AS st_own_cent_qty -- 国有央属数量 
       ,SUM(case WHEN investor_type = '央属国有' THEN total_amt else 0 end)     AS st_own_cent_amt -- 国有央属金额 
       ,SUM(case WHEN investor_type = '其他国有' THEN 1 else 0 end)             AS st_own_oth_qty -- 国有其他数量 
       ,SUM(case WHEN investor_type = '其他国有' THEN total_amt else 0 end)     AS st_own_oth_amt -- 国有其他金额 
       ,SUM(case WHEN investor_type = '民营' THEN 1 else 0 end)             AS priv_qty -- 民营数量 
       ,SUM(case WHEN investor_type = '民营' THEN total_amt else 0 end)     AS priv_amt -- 民营金额 
       ,SUM(case WHEN investor_type = '外资' THEN 1 else 0 end)             AS for_qty -- 外资数量 
       ,SUM(case WHEN investor_type = '外资' THEN total_amt else 0 end)     AS for_amt -- 外资金额 
       ,SUM(case WHEN investor_type = '个人' THEN 1 else 0 end)             AS indv_qty -- 个人数量 
       ,SUM(case WHEN investor_type = '个人' THEN total_amt else 0 end)     AS indv_amt -- 个人金额 
       ,SUM(case WHEN investor_region = '津冀地区' THEN 1 else 0 end)         AS jh_reg_qty -- 津冀地区数量 
       ,SUM(case WHEN investor_region = '津冀地区' THEN total_amt else 0 end) AS jh_reg_amt -- 津冀地区金额 
       ,SUM(case WHEN investor_region = '其他地区' THEN 1 else 0 end)         AS oth_reg_qty -- 其他地区数量 
       ,SUM(case WHEN investor_region = '其他地区' THEN total_amt else 0 end) AS oth_reg_amt -- 其他地区金额 
       ,`prj_lbl`                                                         AS mix_own_type -- 混改类型 
       ,`transparency`                                                    AS proj_type -- 项目类型 
FROM data_zz
GROUP BY `bus_type`,`deal_date`,`prj_lbl`,`transparency`
UNION ALL
SELECT  `bus_type`                                                          AS bus_type -- 业务类型 
       ,`deal_date`                                                         AS deal_date -- 成交日期 
       ,count(distinct project_no)                                          AS deal_qty -- 成交数量
       ,SUM(case WHEN investor_type = '市属国有' THEN 1 else 0 end)          AS st_own_loc_qty -- 国有市属国企数量 
       ,SUM(case WHEN investor_type = '市属国有' THEN total_amt else 0 end)  AS st_own_loc_amt -- 国有市属国企金额 
       ,SUM(case WHEN investor_type = '央属国有' THEN 1 else 0 end)             AS st_own_cent_qty -- 国有央属数量 
       ,SUM(case WHEN investor_type = '央属国有' THEN total_amt else 0 end)     AS st_own_cent_amt -- 国有央属金额 
       ,SUM(case WHEN investor_type = '其他国有' THEN 1 else 0 end)             AS st_own_oth_qty -- 国有其他数量 
       ,SUM(case WHEN investor_type = '其他国有' THEN total_amt else 0 end)     AS st_own_oth_amt -- 国有其他金额 
       ,SUM(case WHEN investor_type = '民营' THEN 1 else 0 end)             AS priv_qty -- 民营数量 
       ,SUM(case WHEN investor_type = '民营' THEN total_amt else 0 end)     AS priv_amt -- 民营金额 
       ,SUM(case WHEN investor_type = '外资' THEN 1 else 0 end)             AS for_qty -- 外资数量 
       ,SUM(case WHEN investor_type = '外资' THEN total_amt else 0 end)     AS for_amt -- 外资金额 
       ,SUM(case WHEN investor_type = '个人' THEN 1 else 0 end)             AS indv_qty -- 个人数量 
       ,SUM(case WHEN investor_type = '个人' THEN total_amt else 0 end)     AS indv_amt -- 个人金额 
       ,SUM(case WHEN investor_region = '津冀地区' THEN 1 else 0 end)         AS jh_reg_qty -- 津冀地区数量 
       ,SUM(case WHEN investor_region = '津冀地区' THEN total_amt else 0 end) AS jh_reg_amt -- 津冀地区金额 
       ,SUM(case WHEN investor_region = '其他地区' THEN 1 else 0 end)         AS oth_reg_qty -- 其他地区数量 
       ,SUM(case WHEN investor_region = '其他地区' THEN total_amt else 0 end) AS oth_reg_amt -- 其他地区金额 
       ,`prj_lbl`                                                         AS mix_own_type -- 混改类型 
       ,`transparency`                                                    AS proj_type -- 项目类型 
FROM data_cq
GROUP BY `bus_type`,`deal_date`,`prj_lbl`,`transparency`
