with temp as (select prj_wrd, bsn_prj_wrd, count(distinct bsn_buyer_id) as final_buyer_count
              from dwd.dwd_ittn_buyer_fct
              where dt = '${dmp_day}'
                and fnl_qua_cfrm_rslt_cd = '1'
              group by bsn_prj_wrd, prj_wrd),
temp_jdc_sl as (select project_id, count(distinct (cust_no)) as final_buyer_count
                     from std.std_bjhl_tbid_ddb_d
                     where ordr_sts in ('1', '2', '3', '11')
                       and dt = '${dmp_day}'
                     group by project_id),
temp_gl as(
    select t.* from dwd.dwd_prj_fct t 
    inner join (
      SELECT  'BJHL'||ID||'CQZR' AS bsn_prj_wrd
      FROM std.std_bjhl_tcqzr_cqzrxxpl_d
      WHERE dt = '${dmp_day}'
      AND xmclass = '0'
    ) ts on t.bsn_prj_wrd = ts.bsn_prj_wrd
    where dt ='${dmp_day}' and t.prj_bsn_tp_cd='GQ' and t.prj_tp_cd='1'
    union all
    select t.* from dwd.dwd_prj_fct t
             left join dwd.dwd_bulk_obj_prj_fct t1 on t.bsn_prj_wrd=t1.bsn_prj_wrd and t1.dt='${dmp_day}'
             where t.dt ='${dmp_day}' and t.prj_bsn_tp_cd='1D' and t1.ast_src in('A17001','A17002') and t1.is_gz='1'
    union all
    select t.* from dwd.dwd_prj_fct t
             left join dwd.dwd_bid_task_list_info t1 on t.prj_wrd=t1.rltv_prj and t1.dt='${dmp_day}'

             where t.dt ='${dmp_day}' and t.prj_bsn_tp_cd='1B' and (t1.ast_src like '企业资产%' or t1.ast_src like '行政事业%'  )
                 and t.txn_mth_cd ='3'
),
temp_prj2 as (select t.*,
                          t1.sown_spvs_org_cd          as sown_spvs_org_cd_gq,
                          t1.sown_spvs_org_cd_dsc      as sown_spvs_org_cd_dsc_gq,
                          t1.cntry_sfep_or_mgr_dept_no as cntry_sfep_or_mgr_dept_no_gq,
                          t1.cntry_sfep_or_mgr_dept_nm as cntry_sfep_or_mgr_dept_nm_gq,
                          t1.spvs_org_prov_cd_dsc      as spvs_org_prov_cd_dsc, -- 监管机构属地(省)代码描述
                          row_number()                    over(partition by t.prj_wrd order by
    (case when t1.sown_spvs_org_cd ='A02001' then 1
    when t1.sown_spvs_org_cd ='A02002' then 2
    when t1.sown_spvs_org_cd ='A02002001' then 3
    when t1.sown_spvs_org_cd ='A02003' then 4
    when t1.sown_spvs_org_cd ='A02004' then 5
    when t1.sown_spvs_org_cd ='A02004001' then 6
    when t1.sown_spvs_org_cd ='A02005' then 7
    when t1.sown_spvs_org_cd ='A02006' then 8
    when t1.sown_spvs_org_cd ='A02006001' then 9
    else 9999 end ) asc, cast(prep_sell_stock_pct as decimal(20,4)) desc) as rn

                   from temp_gl t
                            left join dim.dim_trsfer_info t1 on t.bsn_prj_wrd = t1.bsn_prj_wrd and t1.dt = '${dmp_day}'
                            left join dim.dim_prtrigt_trsfer_info t2
                                      on t1.prj_prt_wrd = t2.prj_prt_wrd and t2.dt = '${dmp_day}'),
     temp_prj as (select t.*,
                         cntry_sfep_or_mgr_dept_nm_gq as prj_prt_cntry_sfep_mgr_dept_new,
                         spvs_org_prov_cd_dsc      as spvs_org_prov_cd_dsc_new, -- 监管机构属地(省)代码描述
                         sown_spvs_org_cd_gq          as prj_prt_sown_spvs_org_cd_new,
                         sown_spvs_org_cd_dsc_gq          as prj_prt_sown_spvs_org_cd_dsc_new                                                     
                  from temp_prj2 t
                  where t.rn = 1),
    temp1 as (
      select a.prj_bsn_tp_cd_dsc                as proj_type,
       a.prj_id                           as proj_no,
       a.prj_nm                           as proj_name,
       f.cust_full_nm                     as transferee_name,
       f.blng_prov_cd_dsc                 as transferee_region,
       b.deal_amt / 10000                 as deal_amt,
       b.delvry_dt                        as deal_date,
       a.txn_mth_cd_dsc                   as deal_way_name,
       d.final_buyer_count,
       a.prj_prt_sown_spvs_org_cd_dsc_new as oasset_custd_org,
       a.spvs_org_prov_cd_dsc_new         as regulatory_province,
       ''                                 as regulatory_city,
       e.ast_src                             asset_source,
       case
           when (e.ast_src like '%中央%' or e.ast_src like '%国务院%') then '央企'
           when (e.ast_src like '%市属,市财政%' or e.ast_src like '%省级%') then '市级'
           when (e.ast_src like '%区县财政%' or e.ast_src like '%市级%') then '区级'
           end
                                          as regulatory_level,
       a.txn_stat_cd_dsc                     prj_status
from temp_prj a
         left join dwd.dwd_evt_deal_rec_fct b on a.prj_wrd = b.prj_wrd and b.dt = '${dmp_day}'
         left join (select distinct bsn_buyer_id, prj_wrd, prov_cd_dsc
                    from dwd.dwd_ittn_buyer_fct
                    where dt = '${dmp_day}') c on b.bsn_buyer_id = c.bsn_buyer_id and a.prj_wrd = c.prj_wrd
         left join temp_jdc_sl d on a.plform_prj_id = cast(d.project_id as String)
         left join dwd.dwd_bid_task_list_info e on a.prj_wrd = e.rltv_prj and e.dt = '${dmp_day}'
         left join dim.dim_pty_cust_info f on f.bsn_stm_cust_no = e.seller_fincer_attr_inves_sbj 
              and f.dt = '${dmp_day}' and  f.edw_star_dt <='${dmp_day}' 
              and '${dmp_day}' <= f.edw_end_dt
        left join std.std_bjhl_tbid_rwdzt_d t1 on e.sts=t1.id and t1.dt='${dmp_day}'
                                      
where a.prj_bsn_tp_cd in ('1B')
and a.txn_mth_cd =3
and e.ast_src is not null
and e.ast_src REGEXP '北京市|中央|国务院|东城区|丰台区|大兴区|宣武区|密云区|崇文区|平谷区|延庆区|怀柔区|房山区|昌平区|朝阳区|海淀区|石景山区|西城区|通州区|门头沟区|顺义区'
union all
select a.prj_bsn_tp_cd_dsc                as proj_type,
       a.prj_id                           as proj_no,
       a.prj_nm                           as proj_name,
       b.trsfee_nm                        as transferee_name,
       c.prov_cd_dsc                      as transferee_region,
       b.deal_amt / 10000                 as deal_amt,
       b.bsn_rec_deal_dt                  as deal_date,
       b.actl_txn_mth_cd_dsc              as deal_way_name,
       d.final_buyer_count,
       a.prj_prt_sown_spvs_org_cd_dsc_new as oasset_custd_org,
       a.spvs_org_prov_cd_dsc_new         as regulatory_province,
       ''                                 as regulatory_city,
       case
           when a.prj_bsn_tp_cd = 'GQ' then '国资产权交易'
           when a.prj_bsn_tp_cd = '1D' then f.ast_src_dsc
           when a.prj_bsn_tp_cd = '1B' then e.zcly end
                                          as asset_source,
       case
           when a.prj_prt_sown_spvs_org_cd_dsc_new in ('国务院国资委监管', '中央其他部委监管', '财政部监管') then '央企'
           when a.prj_prt_sown_spvs_org_cd_dsc_new like '省级%' then '市级'
           when a.prj_prt_sown_spvs_org_cd_dsc_new like '市级%' then '区级'
           end
                                          as regulatory_level,
       a.prj_stat_cd_dsc                     prj_status
from temp_prj a
         left join dwd.dwd_evt_deal_rec_fct b on a.bsn_prj_wrd = b.bsn_prj_wrd and b.dt = '${dmp_day}'
         left join (select distinct bsn_buyer_id, bsn_prj_wrd, prov_cd_dsc
                    from dwd.dwd_ittn_buyer_fct
                    where dt = '${dmp_day}') c on b.bsn_buyer_id = c.bsn_buyer_id and a.bsn_prj_wrd = c.bsn_prj_wrd
         left join temp d on a.bsn_prj_wrd = d.bsn_prj_wrd
         left join dwd.dwd_evt_task_list_fct e on a.prj_wrd = e.prj_wrd and e.dt = '${dmp_day}'
         left join dwd.dwd_bulk_obj_prj_fct f on a.bsn_prj_wrd = f.bsn_prj_wrd and f.dt = '${dmp_day}'
where a.prj_bsn_tp_cd in ('GQ', '1D')
and (a.spvs_org_prov_cd_dsc_new = '北京市' or a.prj_prt_sown_spvs_org_cd_dsc_new in ('国务院国资委监管', '中央其他部委监管', '财政部监管'))
   )
select substr(a.deal_date,1,7) as data_dt,
       case when a.proj_type = '机动车' then '资产转让' else a.proj_type  end proj_type,
       coalesce(a.regulatory_level,b.regulatory_level) as regulatory_level,
       count(1)  as sl,
       sum(deal_amt)/10000 as je
       from temp1 a
       left join (
              SELECT 
              prj_id AS proj_no,
              CASE 
                     WHEN LOCATE('央企', prj_blng_dept_nm) > 0 THEN '央企'
                     WHEN LOCATE('市属', prj_blng_dept_nm) > 0 THEN '市级'
                     ELSE NULL 
              END AS regulatory_level
              FROM dwd.dwd_prj_fct 
              WHERE DT = '${dmp_day}'
              AND prj_id!='' AND  prj_id IS NOT NULL
       ) b
       on a.proj_no = b.proj_no
       where a.proj_no!='' and a.proj_no is not null
group by substr(deal_date,1,7),case when proj_type = '机动车' then '资产转让' else proj_type  end ,coalesce(a.regulatory_level,b.regulatory_level)