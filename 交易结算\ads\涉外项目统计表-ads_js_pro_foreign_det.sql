-- Active: 1725412082987@@10.254.99.145@10000@dwd
WITH T1 AS( 
SELECT 
a.prj_id AS proj_no,	-- 项目编号
a.prj_nm AS proj_name,	-- 项目名称
CASE WHEN a.prj_bsn_tp_cd_dsc='资产转让' THEN '大宗实物' ELSE a.prj_bsn_tp_cd_dsc END AS proj_type,	-- 业务类型
CASE WHEN a.prj_bsn_tp_cd = '1C' THEN a.prj_nm -- 增资
	WHEN a.prj_bsn_tp_cd = 'GQ' THEN b.obj_nm --产权转让
	WHEN a.prj_bsn_tp_cd = '1D' THEN c.obj_nm  --大宗
	ELSE a.prj_nm  END AS trgt_nm,	-- 标的名称
	
CASE WHEN a.prj_bsn_tp_cd = '1C' THEN h.prov_nm -- 增资
	 WHEN a.prj_bsn_tp_cd = 'GQ' THEN b.prov_nm  --产权转让
	 WHEN a.prj_bsn_tp_cd = '1D' THEN c.prov_nm --大宗
	 ELSE NULL END AS udyast_lo_prov_cd_dsc,	-- 标的名称所在地区
a.prj_stat_cd_dsc AS prj_stat_cd_dsc,	-- 项目状态
CASE WHEN a.prj_bsn_tp_cd = 'GQ' THEN b.inf_esr_beg_dt
	 WHEN a.prj_bsn_tp_cd = '1D' THEN c.inf_esr_beg_dt ELSE 
regexp_replace(a.lit_star_dt, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') END AS lit_star_dt,	-- 挂牌开始日期
CASE WHEN a.prj_bsn_tp_cd = 'GQ' THEN b.inf_esr_exp_dt
	 WHEN a.prj_bsn_tp_cd = '1D' THEN c.inf_esr_exp_dt ELSE 
regexp_replace(a.lit_end_dt, '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3')  END AS lit_end_dt,	-- 挂牌截止日期
f.prj_prt_nm AS seller_fincer_name,	-- 转让方/融资方名称
f.prov_cd_dsc AS seller_fincer_region,	-- 转让方/融资方所在地区（省）
g.is_buyer AS is_transferee,	-- 是否受让方
g.buyer_anm AS transferee_name,	-- 受让方或意向受让方名称
g.province_name AS transferee_region,	-- 受让方或意向受让方所在地区（省）
CASE WHEN a.prj_bsn_tp_cd = '1C' THEN e.tfr_prc_zz/10000 ELSE a.tfr_prc/10000 END AS tfr_prc,	-- 转让底价（万元）
a.mrgn_amt/10000 AS margin_amt,	-- 保证金金额（万元）
d.deal_amt/10000 AS deal_amt,	-- 成交金额（万元）
d.deal_dt AS 	deal_date,	-- 成交日期
d.setl_mth_cd_dsc AS pymt_mode_cd_dsc,	-- 结算方式
d.curr_cd_dsc AS currency,	-- 币种
a.prj_blng_dept_nm AS proj_belong_dept_name,	-- 项目所属部门
a.prj_prin_nm AS proj_princ_name	-- 项目负责人
FROM dwd.dwd_prj_fct a 
LEFT JOIN 
(
	SELECT 
	project_code, -- 项目编号
	obj_nm,  -- 标的名称
	prov, --省
	t3.xzqymc AS prov_nm, -- 省名称
	inf_esr_beg_dt, --披露开始日期
	inf_esr_exp_dt --披露截止日期
	FROM std.std_bjhl_tcqzr_cqzrxxpl t1 --产权转让信息正式披露
	LEFT JOIN (SELECT xzqydm  ,-- 代码 
	                  xzqymc  -- 码值
	             FROM ods.ods_bjhl_txzqydm  -- 行政区域代码表
	   WHERE dt='${dmp_day}' 
	         ) t3 
	   ON t1.prov=t3.xzqydm
	WHERE dt = ${dmp_day} 
--	AND project_code = 'G02019BJ1000010'
	GROUP BY 
	project_code, -- 项目名称
	obj_nm,  -- 标的名称
	prov, --省
	t3.xzqymc,inf_esr_beg_dt, --披露开始日期
	inf_esr_exp_dt --披露截止日期
) b 
ON a.prj_id = b.project_code
LEFT JOIN 
(
	SELECT 
	project_code, -- 项目编号
	obj_nm, -- 标的名称
	obj_site_prov, --省
	t3.xzqymc AS prov_nm, -- 省名称
	inf_esr_beg_dt, --披露开始日期
	inf_esr_exp_dt --披露截止日期
	FROM std.std_bjhl_tdzsw_xxpl t1 
	LEFT JOIN (SELECT xzqydm  ,-- 代码 
	                  xzqymc  -- 码值
	             FROM ods.ods_bjhl_txzqydm  -- 行政区域代码表
	   WHERE dt='${dmp_day}' 
	         ) t3 
	   ON t1.obj_site_prov=t3.xzqydm
	WHERE dt = ${dmp_day}
	GROUP BY 
	project_code, -- 项目名称
	obj_nm, -- 标的名称
	obj_site_prov, --省
	t3.xzqymc,
	inf_esr_beg_dt,
	inf_esr_exp_dt
) c -- 大宗实物标的
ON a.prj_id = c.project_code
LEFT JOIN 
(
	SELECT prj_wrd,deal_amt,deal_dt,curr_cd_dsc,setl_mth_cd_dsc FROM dwd.dwd_evt_deal_rec_fct WHERE dt =${dmp_day}
) d  -- 成交记录事实表
ON a.prj_wrd = d.prj_wrd
LEFT JOIN 
(
	SELECT DISTINCT xmbh,CASE 
    WHEN NXZZBZJFS = 1 THEN NMUZJZE
    WHEN NXZZBZJFS = 2 AND NMUZJZEZX = 0 AND NMJZJZEZD != 999999999 THEN CONCAT('不高于', NMJZJZEZD)
    WHEN NXZZBZJFS = 2 AND NMUZJZEZX > 0 AND NMJZJZEZD = 999999999 THEN CONCAT('不低于', NMUZJZEZX)
    WHEN NXZZBZJFS = 2 AND NMUZJZEZX > 0 AND NMJZJZEZD != 999999999 THEN CONCAT(NMUZJZEZX, '-', NMJZJZEZD)
    WHEN NXZZBZJFS = 2 AND NMUZJZEZX = 0 AND NMJZJZEZD = 999999999 THEN '择优而定'
    ELSE NULL END AS tfr_prc_zz
    FROM ods.ods_bjhl_tcgq_zzzsgpxm WHERE dt = ${dmp_day}
	AND xmbh IS NOT NULL
) e -- 增资转让底价
ON a.prj_id = e.xmbh
LEFT JOIN 
(
	SELECT a.prj_wrd,
	concat_ws(',', collect_set(a.prj_prt_nm)) AS prj_prt_nm,
	concat_ws(',', collect_set(a.prov_cd)) AS prov_cd,
	concat_ws(',', collect_set(a.prov_cd_dsc)) AS prov_cd_dsc,
	a.is_Sown
	FROM dim.dim_trsfer_info a
	LEFT JOIN (SELECT prj_wrd, prj_bsn_tp_cd_dsc FROM dwd.dwd_prj_fct WHERE dt = ${dmp_day} GROUP BY prj_wrd, prj_bsn_tp_cd_dsc) b
	ON a.prj_wrd = b.prj_wrd
	WHERE a.dt = ${dmp_day}
	AND concat(b.prj_bsn_tp_cd_dsc, a.prj_tp_cd) IN ('产权转让0', '产权转让1', '企业增资1','资产转让0', '资产转让1')
--	AND a.prj_wrd = 'BJHL6950'
	GROUP BY a.prj_wrd, a.is_Sown
) f 
ON a.prj_wrd = f.prj_wrd
LEFT JOIN 
(SELECT project_code,IF(is_buyer='是','是','否') is_buyer,buyer_anm,province_code,province_name 
FROM dwd.dwd_evt_itrsfee_fct WHERE dt = ${dmp_day}
GROUP BY project_code,IF(is_buyer='是','是','否'),buyer_anm,province_code,province_name
) g
ON a.prj_id=g.project_code
LEFT JOIN 
(
	SELECT t1.xmbh,t1.province,
	t2.xzqymc AS prov_nm -- 省名称
	FROM std.std_bjhl_tcgq_zzzsgpxm t1 -- 增资挂牌项目
	LEFT JOIN (SELECT xzqydm  ,-- 代码 
		                  xzqymc  -- 码值
		             FROM ods.ods_bjhl_txzqydm  -- 行政区域代码表
		   WHERE dt='${dmp_day}' 
		         ) t2
		   ON t1.province=t2.xzqydm
	WHERE t1.dt = ${dmp_day}
	GROUP BY t1.xmbh,t1.province,t2.xzqymc
) h
ON a.prj_id = h.xmbh
WHERE a.prj_bsn_tp_cd IN ('1C','1D','GQ') -- 涉外项目只包括企业增资、产权转让、资产转让（大宗实物）
AND (
	b.prov IN ('710000','810000','820000','999998','999999') 
	OR h.province IN ('710000','810000','820000','999998','999999')
	OR c.obj_site_prov IN ('710000','810000','820000','999998','999999') 
	OR regexp_extract(f.prov_cd, '(710000|810000|820000|999998|999999)', 0) != ''
	OR g.province_code IN ('710000','810000','820000','999998','999999')
	)
AND a.prj_stat_cd_dsc IN ('已披露','已撤牌','已摘牌','交易方式已选择',
'成交待提交','成交已提交','已成交','已中止','已终结','已重新披露','已归')
AND a.dt = ${dmp_day}
)
SELECT 
t1.proj_no,	-- 项目编号
t1.proj_name,	-- 项目名称
t1.proj_type,	-- 业务类型
t1.trgt_nm,	-- 标的名称
t1.udyast_lo_prov_cd_dsc,	-- 标的名称所在地区
t1.prj_stat_cd_dsc,	-- 项目状态
IF(t1.lit_star_dt = '0','',t1.lit_star_dt) AS lit_star_dt,	-- 挂牌开始日期
IF(t1.lit_end_dt = '0','',t1.lit_end_dt) AS lit_end_dt,	-- 挂牌截止日期
t1.seller_fincer_name,	-- 转让方/融资方名称
t1.seller_fincer_region,	-- 转让方/融资方所在地区（省）
t1.is_transferee,	-- 是否受让方
t1.transferee_name,	-- 受让方或意向受让方名称
t1.transferee_region,	-- 受让方或意向受让方所在地区（省）
t1.tfr_prc,	-- 转让底价（万元）
t1.margin_amt,	-- 保证金金额（万元）
sum(deal_amt) AS deal_amt,	-- 成交金额（万元）
t1.deal_date,	-- 成交日期
t1.pymt_mode_cd_dsc,	-- 结算方式
t1.currency,	-- 币种
t1.proj_belong_dept_name,	-- 项目所属部门
t1.proj_princ_name	-- 项目负责人
FROM t1
GROUP BY 
t1.proj_no,	-- 项目编号
t1.proj_name,	-- 项目名称
t1.proj_type,	-- 业务类型
t1.trgt_nm,	-- 标的名称
t1.udyast_lo_prov_cd_dsc,	-- 标的名称所在地区
t1.prj_stat_cd_dsc,	-- 项目状态
t1.lit_star_dt,	-- 挂牌开始日期
t1.lit_end_dt,	-- 挂牌截止日期
t1.seller_fincer_name,	-- 转让方/融资方名称
t1.seller_fincer_region,	-- 转让方/融资方所在地区（省）
t1.is_transferee,	-- 是否受让方
t1.transferee_name,	-- 受让方或意向受让方名称
t1.transferee_region,	-- 受让方或意向受让方所在地区（省）
t1.tfr_prc,	-- 转让底价（万元）
t1.margin_amt,	-- 保证金金额（万元）
t1.deal_date,	-- 成交日期
t1.pymt_mode_cd_dsc,	-- 结算方式
t1.currency,	-- 币种
t1.proj_belong_dept_name,	-- 项目所属部门
t1.proj_princ_name	-- 项目负责人