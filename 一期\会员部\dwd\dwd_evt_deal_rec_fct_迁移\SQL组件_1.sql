SELECT      CAST(T1.MDL_RCRD_NO AS DECIMAL(16,0))   AS DEAL_REC_ID               --成交记录号/成交记录ID
            ,'BJHL'||T1.PROJECT_ID                  AS PRJ_WRD                   --项目ID/项目关键字
            ,T2.BSN_PRJ_WRD                                                      --项目/业务项目关键字
            ,'BJHL'||T1.CUST_NO                     AS CUST_WRD                  --客户号/客户关键字
            ,T2.BSN_BUYER_ID                                                     --受让方/业务买受方ID
            ,T2.BSN_DEAL_REC_ID                                                  --ID/业务成交记录ID
            ,CAST(T1.DEAL_ID AS STRING)             AS DEAL_NO                   --成交编号/成交编号
            ,T1.ENTRST_CGY                          AS ETRS_CGY_CD               --委托类别
            ,T1.ENTRST_CGY_DSC                      AS ETRS_CGY_CD_DSC           --码值/委托类别代码描述
            ,T1.CCY                                 AS CURR_CD                   --币种/币种代码
            ,T1.CCY_DSC                             AS CURR_CD_DSC               --码值/币种代码描述
            ,T1.FUND_ACCT                           AS CPTL_ACC                  --资金账户/资金账户
            ,T1.DEAL_PRICE                          AS TXN_PRC                   --成交价格(元)/成交价格
            ,T1.DEAL_NUM                            AS DEAL_NUM                  --成交数量(手)/成交数量
            ,T1.DEAL_VALUE                          AS DEAL_AMT                  --成交金额/成交金额
            ,REGEXP_REPLACE(T1.DEAL_DATE,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS DEAL_DT--成交日期/成交日期
            ,T1.DEAL_TIME                           AS DEAL_TM                   --成交时间/成交时间
            ,T2.BSN_REC_DEAL_DT                                                  --成交日期/业务记录成交日期
            ,REGEXP_REPLACE(T1.CFM_DT,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS CFRM_DT --确认日期/确认日期
            ,T1.CFM_TM                              AS CFRM_TM                          --确认时间/确认时间
            ,REGEXP_REPLACE(T1.DLV_DT,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS DELVRY_DT --交割日期/交割日期
            ,T1.DLV_TM                              AS DELVRY_TM                        --交割时间/交割时间
            ,T1.MDL_STS                             AS DEAL_STAT_CD                     --成交状态
            ,T1.MDL_STS_DSC                         AS DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
            ,CAST(T1.IS_STM_COLL_CMSN AS STRING)    AS IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
            ,CAST(T1.CMSN AS STRING)                AS BROGE                            --手续费/手续费
            ,CAST(T1.3RD_SIDE_DEPST_JRNL_NO AS STRING) AS THIRD_DEPST_SERIAL_NO         --第三方入金流水号/第三方入金流水号
            ,T1.PY_STS                              AS PAY_STAT_CD                      --付款状态/付款状态代码
            ,T1.PY_STS_DSC                          AS PAY_STAT_CD_DSC                  --码值/付款状态代码描述
            ,CAST(T1.INTDAY_CPTL_DTL_NO AS STRING)  AS CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
            ,CAST(T1.DLV_PSN AS STRING)             AS DELVER_ID                        --交割人/交割人ID
            ,T1.DLV_PSN_NM                          AS DELVER_NM                        --交割人姓名/交割人姓名
            ,T2.IS_SURE_PRNT                                               --是否可以打印/是否可以打印
            ,T2.TXN_VCHR_PRNT_PAGNMB                                       --交易凭证打印页数/交易凭证打印页数
            ,T1.PRT_NUM                             AS PRNT_COPS           --打印份数/打印份数
            ,T2.PRJ_NM                                                     --项目名称/项目名称
            ,T2.TRSFEE_NM                                                  --受让方名称/受让方名称
            ,T2.CONT_SIGN_DT                                               --合同签订日期/合同签订日期
            ,T2.CONT_EFF_DT                                                --合同生效日期/合同生效日期
            ,T2.RSLT_PUBTY_START_DT                                        --结果公示起始时间/结果公示起始日期
            ,T2.RSLT_PUBTY_END_DT                                          --结果公示结束时间/结果公示结束日期
            ,T2.RSLT_PUBTY_PRD_WKDY                                        --结果公示周期（工作日）/结果公示周期（工作日）
            ,T2.FNL_TRNSFR_PCT                                             --最终受让比例/最终受让比例
            ,T2.DEAL_UNIT_PRIC                                             --NULL/成交单价
            ,T2.IS_MRGN_OF_TXN_PRC                                         --保证金是否转交易价款/是否保证金转交易价款
            ,T2.MRGN_OF_TXN_PRC_AMT                                        --NULL/保证金转交易价款金额
            ,T2.MRGN_TF_PRC_AMT                                            --保证金转价款金额(万元)/保证金转价款金额
            ,T2.MRGN_TF_PRC_TMPOT                                          --保证金转价款时点/保证金转价款时点
            ,T2.MRGN_TF_PRC_DT                                             --保证金转价款日期/保证金转价款日期
            ,T2.AVALB_MRGN_AMT                                             --NULL/可使用保证金金额
            ,T2.TOACCT_PRC_AMT                                             --NULL/到账价款金额
            ,T2.MRGN_AMT                                                   --保证金金额（万元）/保证金金额
            ,T2.MRGN_DISPL_MTH_CD                                          --NULL/保证金处置方式代码
            ,T2.MRGN_DISPL_MTH_CD_DSC                                      --NULL/保证金处置方式代码描述
            ,T2.MRGN_TF_PRC_ORDER_ID                                       --保证金转价款订单ID/保证金转价款订单ID
            ,CAST(T2.MRGN_TF_PRC_TM AS STRING) AS MRGN_TF_PRC_TM           --保证金转价款时间/保证金转价款时间
            ,T2.IS_MRGN_ADV_TSFT                                           --保证金提前划出/是否保证金提前划出
            ,T2.PY_MTH_CD                                                  --支付方式/支付方式代码
            ,T2.PY_MTH_CD_DSC                                              --码值/支付方式代码描述
            ,T2.SURPL_PRC_SETL_MTH_CD                                      --剩余价款结算方式/剩余价款结算方式代码
            ,T2.SURPL_PRC_SETL_MTH_CD_DSC                                  --码值/剩余价款结算方式代码描述
            ,T2.DPAMT_AMT                                                  --首付金额(万元)/首付金额
            ,T2.FISSU_PAY_PECT                                             --首期付款百分比/首期付款百分比
            ,T2.BLPT_EXPI_DT                                               --尾款截止日期/尾款截止日期
            ,T2.PAYB_PRC                                                   --应支付价款（万元）/应支付价款
            ,T2.SCROT_PRC_AMT                                              --可划出价款金额（万元）/可划出价款金额
            ,T2.OTHR_REASON                                                --其他原因/其他原因
            ,T2.SETL_MTH_CD                                                --结算方式/结算方式代码
            ,T2.SETL_MTH_CD_DSC                                            --码值/结算方式代码描述
            ,T2.IS_FRN_CUR_SETL                                            --是否外币结算/是否外币结算
            ,T2.FRN_CUR_CURR_CD                                            --外币币种/外币币种代码
            ,T2.FRN_CUR_CURR_CD_DSC                                        --码值/外币币种代码描述
            ,T2.AGDT_EXRT                                                  --约定日汇率/约定日汇率
            ,T2.CONVT_FRN_CUR_AMT                                          --折算外币金额(万元)/折算外币金额
            ,T2.ACTL_TXN_MTH_CD                                            --实际交易方式/实际交易方式代码
            ,T2.ACTL_TXN_MTH_CD_DSC                                        --码值/实际交易方式代码描述
            ,T2.CHAG_TXN_MTH_REASON                                        --更改交易方式原因/更改交易方式原因
            ,T2.TXN_ORG_AUDIT_OPIN                                         --交易机构审核意见/交易机构审核意见
            ,T2.DEAL_TP_CD                                                 --成交类型/成交类型代码
            ,T2.DEAL_TP_CD_DSC                                             --码值/成交类型代码描述
            ,T2.PRC_TSFT_FLG_CD                                            --价款划出标识/价款划出标识代码
            ,T2.PRC_TSFT_FLG_CD_DSC                                        --码值/价款划出标识代码描述
            ,T2.ATSFT_AMT                                                  --已划出金额（万元）/已划出金额
            ,T2.PRC_GOIN_BSN_ORDER_ID                                      --价款划入业务订单ID/价款划入业务订单ID
            ,T2.ORDER_NO_PLFORM                                            --订单编号（平台）/订单编号（平台）
            ,T2.CATR_ID                                                    --创建人/创建人ID
            ,T2.CATR_NM                                                    --名称/创建人名称
            ,CAST(T2.CRT_TM AS STRING) AS crt_tm                           --创建时间/创建时间
            ,T2.UPD_PSN_ID                                                 --更新人/更新人ID
            ,T2.UPD_PSN_NM                                                 --名称/更新人名称
            ,T2.MOD_TM                                                     --更新时间/更新时间
            ,T2.PRJ_PRIN_ID                                                --NULL/项目负责人ID
            ,T2.PRJ_PRIN_NM                                                --NULL/项目负责人名称
            ,T2.PRJ_PRIN_PASS_DT                                           --NULL/项目负责人通过日期
            ,T2.BSN_DEPT_PRIN_ID                                           --NULL/业务部门负责人ID
            ,T2.BSN_DEPT_PRIN_NM                                           --NULL/业务部门负责人名称
            ,T2.BSN_DEPT_PRIN_PASS_DT                                      --NULL/业务部门负责人通过日期
            ,T2.TXN_AUDT_DEP_AUDITOR_ID                                    --NULL/交易审核部审核人ID
            ,T2.TXN_AUDT_DEP_AUDITOR_NM                                    --NULL/交易审核部审核人名称
            ,T2.TXN_AUDT_DEP_AUDITOR_PASS_DT                               --NULL/交易审核部审核人通过日期
            ,T2.TRAS_DEP_PRIN_ID                                           --NULL/交易部负责人ID
            ,T2.TRAS_DEP_PRIN_NM                                           --NULL/交易部负责人名称
            ,T2.TRAS_DEP_PRIN_PASS_DT                                      --NULL/交易部负责人通过日期
            ,T2.EXG_AUDITOR_ID                                             --NULL/交易所审核人ID
            ,T2.EXG_AUDITOR_NM                                             --NULL/交易所审核人名称
            ,T2.EXG_AUDITOR_PASS_DT                                        --NULL/交易所审核人通过日期
            ,T2.CETR_PRIN_ID                                               --NULL/中心负责人ID
            ,T2.CETR_PRIN_NM                                               --NULL/中心负责人名称
            ,T2.CETR_PRIN_PASS_DT                                          --NULL/中心负责人通过日期
            ,T2.CONT_ATCH                                                  --合同附件/合同附件
            ,T2.IS_FST_TM_PRC_TSFT_AUDIT_PASS                              --第一次价款划出是否审核通过/是否第一次价款划出审核通过
            ,T2.DEAL_REC_STAT_CD                                           --NULL/成交记录状态代码
            ,T2.DEAL_REC_SERIAL_NO                                         --NULL/成交记录流水号
            ,T2.DEAL_OVRL_RMRK                                             --NULL/成交总体备注
            ,T1.RMRK  AS RMRK                                              --备注/备注
FROM      STD.STD_BJHL_TBID_CJJL_D         T1               --成交记录表
left JOIN (SELECT *
FROM table1	--各组业务数据
WHERE DT = ${dmp_day}
AND BSN_REC_DEAL_DT IS NOT NULL) T2
--ON  REGEXP_REPLACE(T1.CJRQ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3') = T2.BSN_REC_DEAL_DT
ON T1.PROJECT_ID = T2.DEAL_REC_ID AND T1.CUST_NO=T2.CUST_WRD
WHERE       T1.DT = ${dmp_day}
AND         T1.ENTRST_CGY='1'
--20240104 新增大宗实物业务数据过滤逻辑，需要用T1.MDL_RCRD_NO匹配T2.DEAL_REC_SERIAL_NO（逗号分隔）
AND (
        T2.BSN_PRJ_WRD IS NULL
        OR T2.BSN_PRJ_WRD NOT LIKE '%DZSW%'
        OR (T2.BSN_PRJ_WRD LIKE '%DZSW%' AND T2.DEAL_REC_SERIAL_NO LIKE CONCAT('%', T1.MDL_RCRD_NO, '%'))
    )