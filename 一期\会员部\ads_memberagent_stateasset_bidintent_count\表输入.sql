with temp_cust as (
    SELECT
        b.mdlg_usr_wrd,
        a.agent_no,
        a.compy_name
        FROM (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY a.cust_no ORDER BY a.update_time DESC) AS rn
        FROM ods.ods_bl_agent_info a
        WHERE a.dt = '${dmp_day}'
        ) a
        LEFT JOIN (
        SELECT * FROM dim.dim_pty_cust_usr_rel
        WHERE dt='${dmp_day}'
        AND edw_end_dt='20991231'
        ) b ON 'BJHL'||a.cust_no=b.cust_wrd
        WHERE a.rn = 1 --取最新的一条
),
temp_buyer as (
  SELECT  DISTINCT bsn_prj_wrd
                  ,bsn_buyer_id
                  ,txn_svc_mber_id
                  ,txn_svc_mber_nm
                  ,fnl_qua_cfrm_rslt_cd_dsc
                  ,fnl_qua_cfrm_rslt_cd
                  ,is_fnl_trsfee --是否最终受让方
              FROM dwd.dwd_ittn_buyer_fct
              WHERE dt = '${dmp_day}'
              -- AND mrgn_stat_cd = '1'
                      )
select c.bsn_rec_deal_dt                   as data_dt,
       a.prj_bsn_tp_cd_dsc                 as proj_type,
       coalesce(b.agent_no, '未知')        as agent_no,
       coalesce(b.compy_name, '未知')      as agent_mem,
       count(e.txn_svc_mber_nm)           as bid_participants_count,
       0                                  as investment_amt, -- 投融资参与竞价意向方数量(弃用字段)
       sum(case when e.bsn_buyer_id=c.bsn_buyer_id then 1 else 0 end) as fnl_trsfee_count -- 最终受让方数量
from dwd.dwd_prj_fct a
         left join temp_buyer e on a.bsn_prj_wrd = e.bsn_prj_wrd
         left join dwd.dwd_evt_deal_rec_fct c 
                      on a.bsn_prj_wrd = c.bsn_prj_wrd 
                      and c.bsn_buyer_id = e.bsn_buyer_id
                      and c.dt = '${dmp_day}'
         left join temp_cust b on 'BJHL' || e.txn_svc_mber_id = b.mdlg_usr_wrd
         left join dwd.dwd_bulk_obj_prj_fct f on a.bsn_prj_wrd = f.bsn_prj_wrd and f.dt = '${dmp_day}'
where a.dt = '${dmp_day}'
  and a.prj_bsn_tp_cd in ('1D', 'GQ')
  and c.bsn_rec_deal_dt is not null
  and c.actl_txn_mth_cd in ('2','3','10')
  and (case when a.prj_bsn_tp_cd='1D' then f.is_gz else 1 end)=1 
  and e.fnl_qua_cfrm_rslt_cd = '1'  -- 获得资格
group by a.prj_bsn_tp_cd_dsc, b.agent_no, e.txn_svc_mber_nm,b.compy_name, c.bsn_rec_deal_dt
UNION ALL 
select c.bsn_rec_deal_dt                   as data_dt,
       a.prj_bsn_tp_cd_dsc                 as proj_type,
       coalesce(b.agent_no, '未知')        as agent_no,
       coalesce(b.compy_name, '未知')      as agent_mem,
       count(e.txn_svc_mber_nm)           as bid_participants_count,
       0                                  as investment_amt, -- 投融资参与竞价意向方数量(弃用字段)
       sum(case when e.bsn_buyer_id=c.bsn_buyer_id then 1 else 0 end) as fnl_trsfee_count -- 最终受让方数量
from dwd.dwd_prj_fct a
left join temp_buyer e on a.bsn_prj_wrd = e.bsn_prj_wrd
left join temp_cust b on 'BJHL' || e.txn_svc_mber_id = b.mdlg_usr_wrd
left join dwd.dwd_evt_deal_rec_fct c 
            on a.bsn_prj_wrd = c.bsn_prj_wrd 
            and c.bsn_buyer_id = e.bsn_buyer_id 
            and c.dt = '${dmp_day}'
where a.dt = '${dmp_day}' 
  and a.prj_bsn_tp_cd = '1C' 
  and c.bsn_rec_deal_dt is not null -- 成交日期不为空
  and e.txn_svc_mber_id is not null -- 融资方/受让方经纪会员ID不为空
  and e.fnl_qua_cfrm_rslt_cd = '1' -- 获得资格确认结果代码为1
group by a.prj_bsn_tp_cd_dsc, b.agent_no, e.txn_svc_mber_nm,b.compy_name, c.bsn_rec_deal_dt