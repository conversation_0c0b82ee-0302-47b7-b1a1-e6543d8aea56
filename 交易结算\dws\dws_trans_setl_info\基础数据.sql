SELECT 
    ordr_no,          		 -- 订单号
    ordr_prj_no,      		 -- 订单项目编号
    ordr_prj_name,    		 -- 订单项目名称
    prj_optr,         		 -- 项目经办人
    prj_optr_dept,    		 -- 项目经办部门
    prj_optr_org,     		 -- 业务中心
    cust_no,          		 -- 客户号
    cust_name,        		 -- 客户名称
    ast_type,         		 -- 资产类型
    '诉讼资产'  AS bsn_type,         		 -- 业务类型
    '入金' crj_flag,         		 -- 出入金标识
    '价款' AS CPTL_TYPE_LRGCLS,                        -- 资金类型大类
    '保证金转价款（价款入金）' AS CPTL_TYPE_SMLCLS,         -- 资金类型小类
    amt,              		 -- 发生金额
    ccy,              		 -- 币种
    pay_mode,         		 -- 支付方式
    pay_success_date as pay_success_date, 		 -- 支付成功时间
    col_pay_agent_name, -- 代付方
    setl_bank,        		 -- 结算银行
    cptl_lo,          		 -- 资金位置
    bank_to_acc_date, 		 -- 银行到账时间
    setl_type,        		 -- 结算方式
    '是' AS is_virtual_order, -- 是否虚拟订单
    NULL AS order_creat_dt,
    ext_ordr_no AS ext_ordr_no, -- 机构订单号
    py_aplc_no AS py_aplc_no -- 支付申请号 
FROM 
    dwd.dwd_trans_setl_fct 
    where DT = '${dmp_day}'
    AND ast_type='保证金转价款'
    AND bsn_type LIKE '诉讼资产%' 

UNION ALL

SELECT 
    ordr_no,          		 -- 订单号
    ordr_prj_no,      		 -- 订单项目编号
    ordr_prj_name,    		 -- 订单项目名称
    prj_optr,         		 -- 项目经办人
    prj_optr_dept,    		 -- 项目经办部门
    prj_optr_org,     		 -- 业务中心
    cust_no,          		 -- 客户号
    cust_name,        		 -- 客户名称
    ast_type,         		 -- 资产类型
    CASE WHEN bsn_type LIKE '诉讼资产-%' THEN '诉讼资产'
         WHEN bsn_type = '资产转让' THEN '大宗实物'
              ELSE bsn_type
    END AS bsn_type,         		 -- 业务类型
    crj_flag,         		 -- 出入金标识
    cptl_type_lrgcls, 		 -- 资金类型-大类
    cptl_type_smlcls, 		 -- 资金类型-小类
    amt,              		 -- 发生金额
    ccy,              		 -- 币种
    pay_mode,         		 -- 支付方式
    pay_success_date as pay_success_date, 		 -- 支付成功时间
    col_pay_agent_name, -- 代付方
    setl_bank,        		 -- 结算银行
    cptl_lo,          		 -- 资金位置
    bank_to_acc_date, 		 -- 银行到账时间
    setl_type,        		 -- 结算方式
    CASE WHEN cptl_type_smlcls in ('保证金转价款（价款入金）','保证金转价款（保证金出金）') THEN '是' ELSE '否' END AS is_virtual_order, -- 是否虚拟订单
    order_creat_dt, --订单创建日期
    ext_ordr_no AS ext_ordr_no, -- 机构订单号
    py_aplc_no AS py_aplc_no -- 支付申请号
FROM 
    dwd.dwd_trans_setl_fct 
    where DT = '${dmp_day}'
    and (ast_type != '投资方服务费' OR ast_type IS NULL)
    and cptl_type_smlcls is not null