with prj as(
select
  *
from
  dwd.dwd_prj_fct
where
  dt=${dmp_day}
  and prj_bsn_tp_cd='1C'
  and prj_stat_cd_dsc IN ('已成交', '已归档')
--  and prj_blng_dept_nm in('央企一部','央企二部','央企三部','央企四部','央企五部','央企六部')
  ),
  prj_zz as
  (
	select * from dwd.dwd_entp_incptl_prj_fct where dt =${dmp_day}  
  ),
  
  deal as(
  select * from dwd.dwd_entp_incptl_deal_rec_fct where dt =${dmp_day}  
  ),
  data as(
  select 
    t.prj_wrd,sum(deal.new_pucpl_cptl) as deal_value

  from prj t
   left join prj_zz zz on t.bsn_prj_wrd=zz.bsn_prj_wrd
  left join deal on t.bsn_prj_wrd=deal.bsn_prj_wrd
  group by t.prj_wrd
  ),
  temp_prj2 as(
     select t.*,
            t1.sown_spvs_org_cd as sown_spvs_org_cd_gq,
            t1.sown_spvs_org_cd_dsc as sown_spvs_org_cd_dsc_gq,
            t1.cntry_sfep_or_mgr_dept_no as cntry_sfep_or_mgr_dept_no_gq,
            t1.cntry_sfep_or_mgr_dept_nm as cntry_sfep_or_mgr_dept_nm_gq,
            row_number() over(partition by t.prj_wrd order by
    (case when t1.sown_spvs_org_cd ='A02001' then 1
    when t1.sown_spvs_org_cd ='A02002' then 2
    when t1.sown_spvs_org_cd ='A02002001' then 3
    when t1.sown_spvs_org_cd ='A02003' then 4
    when t1.sown_spvs_org_cd ='A02004' then 5
    when t1.sown_spvs_org_cd ='A02004001' then 6
    when t1.sown_spvs_org_cd ='A02005' then 7
    when t1.sown_spvs_org_cd ='A02006' then 8
    when t1.sown_spvs_org_cd ='A02006001' then 9
    else 9999 end ) asc, cast(prep_sell_stock_pct as decimal(20,4)) desc) as rn

     from dwd.dwd_prj_fct t
              left join dim.dim_trsfer_info t1 on t.bsn_prj_wrd = t1.bsn_prj_wrd and t1.dt = '${dmp_day}'
              left join dim.dim_prtrigt_trsfer_info t2 on t1.prj_prt_wrd = t2.prj_prt_wrd and t2.dt = '${dmp_day}'
     where t.dt = '${dmp_day}'
       and t.prj_bsn_tp_cd in ('1C','1D','GQ')),
  
  
  
  
 temp1 as(
SELECT
    belong_group,
        belong_group1,
        PROJ_BELONG_DEPT_NAME,
        PROJ_PRINC_NAME, 
        udt_tm
FROM (
    SELECT
        belong_group,
        belong_group1,
        PROJ_BELONG_DEPT_NAME,
        PROJ_PRINC_NAME,  
        udt_tm,
        ROW_NUMBER() OVER (PARTITION BY belong_group ORDER BY udt_tm DESC) AS rn
    FROM
        (select * from ods.ods_bl_center_cust_bel_dept where dt='${dmp_day}')
) AS subquery
WHERE
    subquery.rn = 1
),
bl as (
select 
DISTINCT
'BL' || HT_PROJ_NO as bsn_prj_wrd,
x.HT_PROJ_NO as prj_no,
x.SUBJ_MATTER_NAME as prj_nm,
'' as zcly,
'' as prj_prt_sown_spvs_org_cd,
x.CUSTD_TYPE as prj_prt_sown_spvs_org_cd_dsc,
x.INFO_DCLO_BEGIN_DT as info_publ_start_dt,
'' tfr_prc,
'' Eval_Prc,
'' BSN_REC_DEAL_DT,
''txn_mth_cd,
''txn_mth_cd_dsc,
''deal_amt ,
'' Bid_Mth_Cd,
'' Bid_Mth_Cd_Dsc,
'' as Bid_Stat_Cd , --竞价状态代码
'' as Bid_Stat_Cd_Dsc , --竞价状态代码描述
x.BELONG_GROUP as Blng_Org,
x.SELLER_FINCER_NAME as prj_prt_nm,
'' as prj_blng_dept_id, --所属部门ID
x2.PROJ_BELONG_DEPT_NAME as prj_blng_dept_nm, --所属部门
'' as prj_prin_id, --项目负责人ID
x2.PROJ_PRINC_NAME as prj_prin_nm, --项目负责人
'' as Txn_Svc_Mber_Id, --代理会员ID
AGENT_MEM as Txn_Svc_Mber_Nm , --代理会员
'' as Prj_Stat_Cd, --项目状态代码
'' as Prj_Stat_Cd_Dsc, --项目状态代码
''  Prj_Tp_Cd, --项目类型代码
''  Prj_Tp_Cd_Dsc, --项目类型代码描述
'' is_owned_State,-- 是否国资

case when PROJ_TYPE ='产权转让' then 'GQ'
when PROJ_TYPE ='企业增资' then '1C'
when PROJ_TYPE ='实物' then '1D' end  as  Prj_Bsn_Tp_Cd, --项目业务类型代码
case when PROJ_TYPE ='实物' then '资产转让' else PROJ_TYPE end  as  prj_bsn_tp_cd_dsc, --项目业务类型名称
CUST_CLASS as entp_tp, --企业类型
'' as Info_Publ_Exprt_Dt,-- 信息披露期满日期
case when exch ='山东' and (x.HT_PROJ_NO RLIKE 'H[0-9]*$' OR x.HT_PROJ_NO like '%-%') then '是'
     when exch !='山东' and x.HT_PROJ_NO like '%-%' then '是'
     else '否' end  as is_repeat,
case
when exch='上海' then '上海联交所'
when exch='广东' then '广东联交所'
when exch='重庆' then '重庆联交所'
when exch='深圳' then '深圳联交所'
when exch='山东' then '山东交易所' end
as exch,
CASE 
        WHEN HT_AMT_W_YUAN REGEXP '[\\u4e00-\\u9fa5]' THEN 0 
        ELSE HT_AMT_W_YUAN*10000
    END as Lit_Amt, --挂牌金额 
'' as ecn_tp_cd, --经济类型代码
'' as ecn_tp_cd_dsc --经济类型名称
from(
select a.* from 
ods.ods_bl_center_formal_dcl a
join (select HT_PROJ_NO ,max(UDT_TM) as UDT_TM from ods.ods_bl_center_formal_dcl where dt=${dmp_day} group by HT_PROJ_NO) b
on a.HT_PROJ_NO=b.HT_PROJ_NO and a.UDT_TM=b.UDT_TM
where a.dt=${dmp_day}
and (a.is_delete='0' or a.is_delete is null)
)x
left join
temp1 x2
on x.belong_group  =x2.belong_group
where  exch in('上海','广东','重庆','深圳','山东')

),
dwd_prj_fct as(
    
     select
t.*,
 cntry_sfep_or_mgr_dept_nm_gq as prj_prt_cntry_sfep_mgr_dept_new,
sown_spvs_org_cd_gq as prj_prt_sown_spvs_org_cd_new,
 sown_spvs_org_cd_dsc_gq as prj_prt_sown_spvs_org_dsc_new
   from temp_prj2 t
     where t.rn=1
),
all_data(
select 
distinct
case when t.bsn_prj_wrd is null then t.prj_wrd else  t.bsn_prj_wrd end as bsn_prj_wrd, --项目业务关键字
t.prj_id as prj_no, --项目编号
t.prj_nm,--项目名称
'' as zcly,
t.prj_prt_sown_spvs_org_cd_new as prj_prt_sown_spvs_org_cd, --国资监管机构ID
t.prj_prt_sown_spvs_org_dsc_new as prj_prt_sown_spvs_org_cd_dsc, --国资监管机构
t.info_publ_start_dt, --信息披露起始日期
t.tfr_prc, --转让底价
case when t.prj_bsn_tp_cd ='GQ' then t.dwpgz*10000 else t3.eval end Eval_Prc, --评估值
t1.BSN_REC_DEAL_DT,
t1.actl_txn_mth_cd txn_mth_cd,  --成交方式代码
t1.actl_txn_mth_cd_dsc txn_mth_cd_dsc, --成交方式
case when t.prj_bsn_tp_cd ='1C' then t10.deal_value else t1.deal_amt end deal_amt, --成交金额
t.Bid_Mth_Cd, --竞价方式
t.Bid_Mth_Cd_Dsc ,               --是否有效竞价
t.Bid_Stat_Cd , --竞价状态代码
t.Bid_Stat_Cd_Dsc , --竞价状态代码描述
COALESCE(t.prj_prt_cntry_sfep_mgr_dept_new,'未知') as Blng_Org , -- 所属集团
t.prj_prt_nm , --项目方名称
t.prj_blng_dept_id, --所属部门ID
t2.name as prj_blng_dept_nm, --所属部门
t.prj_prin_id, --项目负责人ID
t.prj_prin_nm, --项目负责人
t.Txn_Svc_Mber_Id, --代理会员ID
t.Txn_Svc_Mber_Nm , --代理会员
t.Prj_Stat_Cd, --项目状态代码
t.Prj_Stat_Cd_Dsc, --项目状态代码
t.Prj_Tp_Cd, --项目类型代码
t.Prj_Tp_Cd_Dsc, --项目类型代码描述
t4.Is_Sown is_owned_State,-- 是否国资
t.Prj_Bsn_Tp_Cd, --项目业务类型代码
t.prj_bsn_tp_cd_dsc, --项目业务类型名称
case when t.prj_bsn_tp_cd in('1C','1D','GQ') and (t.prj_prt_sown_spvs_org_dsc_new in('国务院国资委监管') or t.prj_prt_sown_spvs_org_dsc_new is null) then '央企'
	 when t.prj_bsn_tp_cd in('1C','1D','GQ') and t.prj_prt_sown_spvs_org_dsc_new in('中央其他部委监管') then '部委'
	 when t.prj_bsn_tp_cd in('1C','1D','GQ') and t.prj_prt_sown_spvs_org_dsc_new in('省级财政部门监管','省级国资委监管','省级其他部门监管','市级国资委监管','市级其他部门监管') then '地方'
   when t.prj_bsn_tp_cd in('1C','1D','GQ') and t.prj_prt_sown_spvs_org_dsc_new in('财政部监管','市级财政部门或金融办监管') then '金融'
   else '未知'
   end as entp_tp, --企业类型
t.Info_Publ_Exprt_Dt,-- 信息披露期满日期
case when prj_id like '%-%' and  prj_id not like '%-0%' then '是' else '否' end is_repeat, -- 是否重复挂牌
'北交所' exch, --'交易所'
t.Lit_Amt, --挂牌金额 
t4.ecn_tp_cd, --经济类型代码
t4.ecn_tp_cd_dsc --经济类型名称
from dwd_prj_fct t 
left  JOIN (SELECT
            DISTINCT
            PRJ_WRD
          ,BSN_PRJ_WRD
				  ,DEAL_AMT
				  ,BSN_REC_DEAL_DT
          ,actl_txn_mth_cd
          ,actl_txn_mth_cd_dsc
			FROM dwd.dwd_evt_deal_rec_fct  --成交记录事实
			WHERE dt='${dmp_day}'
      
      
      ) t1
	ON t.PRJ_WRD=t1.PRJ_WRD
left join (

select id,org_nm as name FROM std.std_BJHL_LBORGANIZATION_d --组织机构
			WHERE dt='${dmp_day}'
) t2 on t.prj_blng_dept_id=t2.id

left join (

select prj_wrd,CONCAT_WS(',',COLLECT_LIST(ecn_tp_cd)) as ecn_tp_cd,CONCAT_WS(',',COLLECT_LIST(ecn_tp_cd_dsc)) as ecn_tp_cd_dsc,max(Is_Sown) as Is_Sown FROM dim.dim_trsfer_info --项目方信息维
			WHERE dt='${dmp_day}'
group by prj_wrd
) t4 on t.prj_wrd=t4.prj_wrd

left join (select * from  dim.dim_ast_prj_prt_ast where dt='${dmp_day}') t3  on t.prj_wrd=t3.prj_wrd

left join data t10 on t.prj_wrd=t10.prj_wrd


union all

select * from bl)
  -- 增加了资产类型逻辑
SELECT
  bsn_prj_wrd,
  prj_no,
  prj_nm,
  CASE
    WHEN a.prj_bsn_tp_cd='GQ' THEN b.zdmc
    WHEN a.prj_bsn_tp_cd='1D' THEN c.zdmc
    ELSE a.zcly
  END AS zcly,
  prj_prt_sown_spvs_org_cd,
  prj_prt_sown_spvs_org_cd_dsc,
  info_publ_start_dt,
  tfr_prc,
  CASE
    WHEN a.prj_bsn_tp_cd='1D' THEN c.zcpgz*10000
    ELSE eval_prc END AS eval_prc, --评估值
  bsn_rec_deal_dt,
  txn_mth_cd,
  txn_mth_cd_dsc,
  deal_amt,
  bid_mth_cd,
  bid_mth_cd_dsc,
  bid_stat_cd,
  bid_stat_cd_dsc,
  blng_org,
  prj_prt_nm,
  prj_blng_dept_id,
  prj_blng_dept_nm,
  prj_prin_id,
  prj_prin_nm,
  txn_svc_mber_id,
  txn_svc_mber_nm,
  prj_stat_cd,
  prj_stat_cd_dsc,
  prj_tp_cd,
  prj_tp_cd_dsc,
  CASE
    WHEN c.gyzczr IS NOT NULL THEN gyzczr
    ELSE a.is_owned_state
  END AS is_owned_state,
  prj_bsn_tp_cd,
  prj_bsn_tp_cd_dsc,
  entp_tp,
  info_publ_exprt_dt,
  is_repeat,
  exch,
  lit_amt,
  ecn_tp_cd,
  ecn_tp_cd_dsc
FROM
  all_data a
  LEFT JOIN (
    SELECT
      k1.xmbh,
      k1.xmssbm,
      CONCAT_WS(',', COLLECT_LIST (k2.zdmc)) AS zdmc
    FROM
      (
        SELECT
          xmbh,
          xmssbm,
          zrfgzjgjg_element AS zrfgzjgjg
        FROM
          ods.ods_bjhl_tcqzr_cqzrxxpl LATERAL VIEW explode (split (IF(zrfgzjgjg IS NULL, '-', zrfgzjgjg), ',')) zrfgzjgjg_table AS zrfgzjgjg_element
        WHERE
          dt='${dmp_day}'
          AND xmbh IS NOT NULL
        GROUP BY
          xmbh,
          xmssbm,
          zrfgzjgjg_element
      ) k1
      LEFT JOIN (
        SELECT
          zddm,
          zdmc
        FROM
          ods.ods_bjhl_tbzzd
        WHERE
          dt='${dmp_day}'
          AND zdlx IN ('3', '102')
      ) k2 ON k2.zddm=k1.zrfgzjgjg
    GROUP BY
      k1.xmbh,
      k1.xmssbm
  ) b ON b.xmbh=a.prj_no
  LEFT JOIN (
    SELECT
      k1.xmbh,
      k1.xmmc,
      k1.xmssbm,
      k1.zrdj,
      K1.gyzczr,
      K1.zcpgz,  -- 评估值
      CONCAT_WS(',', COLLECT_LIST (k2.zdmc)) AS zdmc
    FROM
      (
        SELECT
          xmbh,
          xmmc,
          xmssbm,
          zrdj,
          gyzczr,
          zcpgz,  -- 评估值
          zcly_element AS zcly
        FROM
          ods.ods_bjhl_tdzsw_xxpl LATERAL VIEW explode (split (IF(zcly IS NULL, '-', zcly), ',')) zcly_table AS zcly_element
        WHERE
          dt='${dmp_day}'
          AND xmbh IS NOT NULL
        GROUP BY
          xmbh,
          xmmc,
          xmssbm,
          zrdj,
          gyzczr,
          zcpgz,
          zcly_element
      ) k1
      LEFT JOIN (
        SELECT
          zddm,
          zdmc
        FROM
          ods.ods_bjhl_tbzzd
        WHERE
          dt='${dmp_day}'
          AND zdlx IN ('202')
      ) k2 ON k2.zddm=k1.zcly
    GROUP BY
      k1.xmbh,
      k1.xmmc,
      k1.xmssbm,
      k1.zrdj,
      K1.gyzczr,
      K1.zcpgz
  ) c ON c.xmbh=a.prj_no