with temp_cust as(
  SELECT
        DISTINCT
			  b.mdlg_usr_wrd,
			  a.agent_no,
			  a.compy_name
			FROM (
			  SELECT
			    *,
			    ROW_NUMBER() OVER (PARTITION BY a.cust_no ORDER BY a.update_time DESC) AS rn
			  FROM ods.ods_bl_agent_info a
			  WHERE a.dt = '${dmp_day}'
			) a
			LEFT JOIN (
			  SELECT * FROM dim.dim_pty_cust_usr_rel
			  WHERE dt='${dmp_day}'
			  AND edw_end_dt='20991231'
			) b ON 'BJHL'||a.cust_no=b.cust_wrd
			WHERE a.rn = 1 --取最新的一条
)

select
  DISTINCT
  a.prj_id as proj_no,
  a.prj_nm as proj_name,
  a.prj_stat_cd_dsc as prj_stat_cd_dsc,
  a.prj_prt_nm as seller_fincer_name,
  x.compy_name as agent_mem,
  f.deal_amt/10000 as investment_amt,
  d.fwfze as fee_amt,
  d.bjssr as cbex_fee_amt,
  a.selt_mth_cd_dsc as selection_method,
  b.bsn_rec_deal_dt as deal_date,
  a.info_publ_start_dt as info_dclo_begin_dt,
  a.info_publ_exprt_dt as info_dclo_expire_dt,
  a.prj_blng_dept_nm as proj_belong_dept_name,
  a.prj_prin_nm as proj_princ_name
from
  dwd.dwd_prj_fct a
  left join dwd.dwd_evt_deal_rec_fct b on a.bsn_prj_wrd=b.bsn_prj_wrd
  and b.dt='${dmp_day}'
  left join dwd.dwd_ittn_buyer_fct c on b.bsn_buyer_id=c.bsn_buyer_id
  and a.bsn_prj_wrd=c.bsn_prj_wrd
  and c.dt='${dmp_day}'
  left join dwd.dwd_prj_fee_fct d on a.bsn_prj_wrd=d.bsn_prj_wrd and a.dt = d.dt
  left join (
    select
      bsn_prj_wrd,
      sum(deal_amt) as deal_amt
    from
      dwd.dwd_evt_deal_rec_fct
    where
      dt='${dmp_day}'
    group by
      bsn_prj_wrd
  ) f on a.bsn_prj_wrd=f.bsn_prj_wrd
  left join temp_cust x on 'BJHL'||a.txn_svc_mber_id=x.mdlg_usr_wrd
where
  a.dt='${dmp_day}'
  and a.prj_bsn_tp_cd='1C'
  and b.bsn_rec_deal_dt is not null and x.agent_no is not null
  