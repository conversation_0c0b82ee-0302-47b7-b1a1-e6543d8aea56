with data_del as (
-- 主查询（非联合体部分）
SELECT DISTINCT 
     A.project_no                 AS proj_no         -- 项目编号
    ,A.assignee_name             AS assignee_name   -- 受让方名称 
    ,A.transaction_date          AS deal_date       -- 成交日期 
    ,A.assets_assessed_value     AS eval_value      -- 资产总计/评估价值(万元) 
    ,CASE WHEN A.is_unite_buy = 1 and fnl_trnsfr_pct IS NOT NULL THEN A.fnl_trnsfr_pct*A.transaction_amount 
     ELSE A.transaction_amount END AS deal_price 
    ,CASE WHEN A.is_unite_buy != 1 or fnl_trnsfr_pct IS NULL THEN 100 ELSE A.fnl_trnsfr_pct END AS fnl_trnsfr_pct   -- 最终受让比例
    ,A.appreciation_amt          AS add_value       -- 增值额(万元) 
    ,B.investor_type             AS investor_type   -- 投资人类型 
    ,B.mix_own_type              AS mix_own_type    -- 混改类型 
    ,CASE 
        WHEN A.asassignee_province IN ('天津市','河北省') 
        THEN '津冀地区'  
        ELSE '其他地区' 
     END                         AS investor_reg    -- 投资人地区
    ,B.red_type                  AS red_type        -- 压减类型 
FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_eqty_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) B -- 产权转让项目数据纠正表-补录 
ON A.project_no = B.proj_no AND A.assignee_name = B.investor_name
WHERE A.dt = '${dmp_day}'
AND COALESCE(B.state_owned_asset_regulator, A.transferor_regulatory) = '省级国资委监管'
AND A.transferor_area_code LIKE '110%'
AND A.department REGEXP '市属'
AND A.proj_type = '正式披露'
AND A.transaction_date IS NOT NULL

UNION ALL

-- 联合体成员部分
SELECT DISTINCT 
     A.project_no                 AS proj_no         -- 项目编号 
    ,A.assignee_name             AS assignee_name   -- 受让方名称 
    ,A.transaction_date          AS deal_date       -- 成交日期 
    ,A.assets_assessed_value     AS eval_value      -- 资产总计/评估价值(万元) 
    ,(C.fnl_buy_pct/100) * A.transaction_amount AS deal_price  -- 成交价格(万元)
    ,C.fnl_buy_pct                   AS fnl_trnsfr_pct   -- 最终受让比例 
    ,(C.fnl_buy_pct/100) * A.appreciation_amt AS add_value    -- 增值额(万元) 
    ,B.investor_type             AS investor_type   -- 投资人类型 
    ,B.mix_own_type              AS mix_own_type    -- 混改类型 
    ,CASE 
        WHEN A.asassignee_province IN ('天津市','河北省') 
        THEN '津冀地区'  
        ELSE '其他地区' 
     END                         AS investor_reg    -- 投资人地区
    ,B.red_type                  AS red_type        -- 压减类型 
FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
LEFT JOIN (
    SELECT  'BJHL'||cj.prj||'CQZR' AS bsn_prj_wrd -- 业务项目关键字 （关联1）
           ,cj.buyer -- 受买方ID
           ,cj.buyer_nm -- 受让方名称(关联2)/联合体代表名称
           ,tyc.mbr_nm --联合体成员体名称
           ,ty.is_unite_buy -- 是否联合受让方
           ,tyc.fnl_buy_pct -- 最终受让比例
    FROM std.std_bjhl_tcqzr_yxsrf_cy_d tyc
    LEFT JOIN std.std_bjhl_tcqzr_yxsrfxx_d ty
    ON tyc.buyer_id = ty.id AND tyc.dt = ty.dt
    LEFT JOIN std.std_bjhl_tcqzr_cjjlxx_d cj
    ON cj.buyer = ty.id AND cj.dt = ty.dt
    WHERE cj.prj is not null
    AND ty.is_unite_buy = 1
    AND tyc.fnl_buy_pct IS NOT NULL
    AND tyc.dt = '${dmp_day}' 
) C
ON A.bsn_prj_wrd = C.bsn_prj_wrd AND A.assignee_name = C.buyer_nm
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_eqty_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) B -- 产权转让项目数据纠正表-补录 
ON A.project_no = B.proj_no AND C.mbr_nm = B.investor_name
WHERE A.dt = '${dmp_day}'
AND COALESCE(B.state_owned_asset_regulator, A.transferor_regulatory) = '省级国资委监管'
AND A.transferor_area_code LIKE '110%'
AND A.department REGEXP '市属'
AND A.proj_type = '正式披露'
AND A.transaction_date IS NOT NULL
AND A.is_unite_buy = 1
AND C.fnl_buy_pct IS NOT NULL
)
SELECT  substr(`deal_date`, 0, 7)                                            AS deal_date    -- 成交日期
       ,count(distinct proj_no)                                              AS deal_qty     -- 成交数量
       ,SUM(CASE WHEN investor_type = '其他国有' THEN 1 ELSE 0 END)           AS oth_own_qty  -- 其他国有成交数量
       ,SUM(CASE WHEN investor_type = '其他国有' THEN deal_price ELSE 0 END)  AS oth_own_amt  -- 其他国有成交金额
       ,SUM(CASE WHEN investor_type = '民营' THEN 1 ELSE 0 END)               AS priv_qty     -- 民营成交数量
       ,SUM(CASE WHEN investor_type = '民营' THEN deal_price ELSE 0 END)      AS priv_amt     -- 民营成交金额
       ,SUM(CASE WHEN investor_type = '外资' THEN 1 ELSE 0 END)               AS for_qty      -- 外资成交数量
       ,SUM(CASE WHEN investor_type = '外资' THEN deal_price ELSE 0 END)      AS for_amt      -- 外资成交金额
       ,SUM(CASE WHEN investor_type = '个人' THEN 1 ELSE 0 END)               AS indv_qty     -- 个人成交数量
       ,SUM(CASE WHEN investor_type = '个人' THEN deal_price ELSE 0 END)      AS indv_amt     -- 个人成交金额
       ,`red_type`                                                            AS red_type     -- 压减类型
       ,''                                                                    AS eval_value   -- 评估值
       ,''                                                                    AS add_value    -- 增值金额
FROM data_del
GROUP BY `deal_date`, `red_type`
