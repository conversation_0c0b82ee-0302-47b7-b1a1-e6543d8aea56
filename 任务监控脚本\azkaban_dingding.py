
## 更换azkaban生产环境地址，project_id,钉钉机器人地址

from datetime import datetime

import requests

# Azkaban URL and credentials
azkaban_url = 'http://10.254.15.132:18081'
username = 'azkaban'
password = 'azkaban'
project_id = 'dataflow_11ef65fb284d3643904dab7855587e1f'
flow='base'


# DingTalk Webhook URL
dingtalk_webhook = 'https://oapi.dingtalk.com/robot/send?access_token=c3e2827131b49ebdfde6a79b0833b736055a6e1d5c67dd5fdd80f05c67dc6d63'

# Function to login to Azkaban and get session ID
# Function to login to Azkaban and get session ID
def azkaban_login(azkaban_url, username, password):
    login_url = f'{azkaban_url}/index'
    data = {
        'action': 'login',
        'username': username,
        'password': password
    }
    response = requests.post(login_url, data=data)
    response.raise_for_status()
    response_json = response.json()
    print("Login Response:", response_json)  # Debugging line
    return response_json['session.id']

# Function to get the latest execution ID
def get_latest_execution_id(azkaban_url, session_id, project_id):
    executions_url = f'{azkaban_url}/manager'
    params = {
        'ajax': 'fetchFlowExecutions',
        'project': project_id,
        'flow': flow,
        'start': 0,
        'length': 16,
        'session.id': session_id
    }
    response = requests.get(executions_url, params=params)
    response.raise_for_status()
    response_json = response.json()
    print("Executions Response:", response_json)  # Debugging line
    executions = response_json.get('executions', [])
    if executions:
        return executions[0]['execId']
    else:
        raise Exception('No executions found for the specified project ID')

# Function to get the result of the latest execution
def get_execution_result(azkaban_url, session_id, exec_id):
    execution_url = f'{azkaban_url}/executor'
    params = {
        'ajax': 'fetchexecflow',
        'execid': exec_id,
        'session.id': session_id
    }
    response = requests.get(execution_url, params=params)
    response.raise_for_status()
    response_json = response.json()
    print("Execution Result Response:", response_json)  # Debugging line
    return response_json

def send_dingtalk_message(webhook_url, message):
    headers = {'Content-Type': 'application/json'}
    payload = {
        "msgtype": "markdown",
        "markdown": {
            "title": "Azkaban Execution Report",
            "text": message
        }
    }
    response = requests.post(webhook_url, json=payload, headers=headers)
    response.raise_for_status()
    print("Message sent to DingTalk")

def convert_timestamp(timestamp):
    if timestamp:
        return datetime.fromtimestamp(timestamp / 1000)
    return None
# Main script
try:
    session_id = azkaban_login(azkaban_url, username, password)
    latest_exec_id = get_latest_execution_id(azkaban_url, session_id, project_id)
    execution_result = get_execution_result(azkaban_url, session_id, latest_exec_id)
    print("Latest Execution Result:")
    print(execution_result)
    start_time = execution_result.get('startTime')
    end_time = execution_result.get('endTime')
    status = execution_result.get('status')
    start_time_formatted = convert_timestamp(start_time)
    end_time_formatted = convert_timestamp(end_time)
    if start_time_formatted and end_time_formatted:
        time_diff = end_time_formatted - start_time_formatted
        duration_minutes = time_diff.total_seconds() / 60
    else:
        duration_minutes = None
    print("Latest Execution Details:")
    print(f"Start Time: {start_time_formatted}")
    print(f"End Time: {end_time_formatted}")
    print(f"Status: {status}")
    print(f"运行时长：{duration_minutes}")

    start_time_str = start_time_formatted.strftime('%Y-%m-%d %H:%M:%S') if start_time_formatted else 'N/A'
    end_time_str = end_time_formatted.strftime('%Y-%m-%d %H:%M:%S') if end_time_formatted else 'N/A'
    duration_str = f"{duration_minutes:.2f} 分" if duration_minutes is not None else 'N/A'
    # Formatting status
    if status == 'SUCCEEDED':
        status_str = f"<font color=\"info\">**成功**</font>"  # Bold for green color simulation

    else:
        status_str = f"<font color=\"warning\">**失败**</font>"   # Bold for red color simulation

    message = (
        f"### 数据流运行监控\n"
        f"- **开始**: {start_time_str}\n"
        f"- **结束**: {end_time_str}\n"
        f"- **时长**: {duration_str}\n"
        f"- **状态**: {status_str}"
    )

    # Send message to DingTalk
    send_dingtalk_message(dingtalk_webhook, message)

except Exception as e:
    print(f"Error: {e}")