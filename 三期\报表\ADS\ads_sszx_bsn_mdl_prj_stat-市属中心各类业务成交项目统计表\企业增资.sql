WITH data_all AS (
    SELECT
    DISTINCT
    A.`transparency`                AS proj_category,              -- 公开/非公开 
    B.`project_code`                AS proj_no,                    -- 项目编号 
    '企业增资'                       AS proj_type,                  -- 业务类型 
    B.prj_sts                       AS proj_status,                -- 项目状态  
    substr(B.`deal_date`,0,7)      AS deal_date,                  -- 成交日期 
    A.`total_investment_funds`      AS deal_value                 -- 投资资金总额合计数 
    FROM dwd.dwd_entp_incptl_trsfer_ext_d A -- 增资交易汇总_扩展表
    LEFT JOIN dws.dws_entp_incptl_trsfer_d B -- 增资交易汇总表
    ON A.project_code = B.project_code
    AND A.buyer_name = B.buyer_name
    AND A.dt = B.dt
    WHERE A.dt = '${dmp_day}'
    AND A.`custd_org_depdc_prov` LIKE '110%' -- 北京市(包括北京市各区)
    AND B.`fincer_oasset_reg_org` IN ('省级国资委监管','省级其他部门监管','省级财政部门监管','市级国资委监管','市级其他部门监管','市级财政部门或金融办监管')
    AND B.prj_blng_dept REGEXP '市属|行政司法'
    AND B.`deal_date` IS NOT NULL    
),
data_all_ss AS (
    SELECT
    DISTINCT
    A.`transparency`                AS proj_category,              -- 公开/非公开 
    B.`project_code`                AS proj_no,                    -- 项目编号 
    '企业增资'                       AS proj_type,                  -- 业务类型 
    B.prj_sts                       AS proj_status,                -- 项目状态  
     substr(B.`deal_date`,0,7)      AS deal_date,                  -- 成交日期 
    A.`total_investment_funds`      AS deal_value                 -- 投资资金总额合计数 
    FROM dwd.dwd_entp_incptl_trsfer_ext_d A -- 增资交易汇总_扩展表
    LEFT JOIN dws.dws_entp_incptl_trsfer_d B -- 增资交易汇总表
    ON A.project_code = B.project_code
    AND A.buyer_name = B.buyer_name
    AND A.dt = B.dt
    WHERE A.dt = '${dmp_day}'
    AND A.`custd_org_depdc_prov` LIKE '110%' -- 北京市(包括北京市各区)
    AND B.`fincer_oasset_reg_org` = '省级国资委监管'
    AND B.prj_blng_dept REGEXP '市属'
    AND B.`deal_date` IS NOT NULL
)
SELECT  proj_type       AS proj_type -- 3 
       ,COUNT(DISTINCT proj_no)  AS deal_num -- 成交数量 
       ,SUM(deal_value) AS deal_value -- 成交金额 
       ,deal_date       AS deal_date -- 成交日期 
       ,proj_category   AS proj_category -- 项目类别 
       ,proj_status     AS proj_status -- 项目状态 
       ,'北京市国企'      AS cagetory -- 类别 
FROM data_all
GROUP BY proj_type,deal_date,proj_category,proj_status,cagetory
UNION ALL 
SELECT  proj_type       AS proj_type -- 3 
       ,COUNT(DISTINCT proj_no)  AS deal_num -- 成交数量 
       ,SUM(deal_value) AS deal_value -- 成交金额 
       ,deal_date       AS deal_date -- 成交日期 
       ,proj_category   AS proj_category -- 项目类别 
       ,proj_status     AS proj_status -- 项目状态 
       ,'市属企业'        AS cagetory -- 类别 
FROM data_all_ss
GROUP BY proj_type,deal_date,proj_category,proj_status,cagetory