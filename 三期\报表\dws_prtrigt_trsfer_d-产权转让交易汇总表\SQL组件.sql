SELECT 
bsn_prj_wrd,
project_no,
project_name,
transferor_name,
approval_institution,
approval_unit,
industry,
audit_year,
audit_net_profit,
report_type,
report_date,
report_net_profit,
held_equity,
proposed_equity,
total_equity,
book_value,
assessed_value,
target_valuation,
ut_target_valuation,
transfer_price,
debt_amount,
transaction_amount ,
transaction_method,
transaction_date,
assignee_name,
department,
project_manager,
department_head,
project_source,
project_status,
target_name,
control_transfer,
assets_book_value,
assets_assessed_value,
transferor_economic_type,
transferor_province,
transferor_city,
transferor_district,
transferor_regulatory,
transferor_department,
transferor_area,
disclosure_start,
disclosure_end,
transaction_mode,
preselect_mode,
pre_disclosure_start,
intended_assignees,
contract_signing,
contract_effective,
payment_method,
asassignee_province,
assignee_economic_type,
assignee_contact,
assignee_phone,
assignee_broker,
assignee_type,
is_original_shareholder,
transaction_increase,
transaction_increase_amount,
disclosure_period,
premium_rate,
price_transfer_date,
assignee_id,
REGEXP_REPLACE(
    COALESCE(D.is_on_stock,'') ||
    CASE WHEN B.is_kgqy = 1 THEN '转让方是科改企业,' ELSE '' END || 
    CASE WHEN B.is_sjylzjtxsfqy = 1 THEN '转让方是世界一流专精特新示范企业,' ELSE '' END || 
    CASE WHEN B.is_sbqy = 1 THEN '转让方是双百企业,' ELSE '' END || 
    CASE WHEN B.is_sjylsfqy = 1 THEN '转让方是世界一流示范企业,' ELSE '' END || 
    COALESCE(C.sjqy_bq,''),
    '^,|,$',
    ''
) AS project_tags,
A.proj_type,
A.deal_type,
A.is_internal_adjust,
A.income,
A.appreciation_amt,
A.fnl_trnsfr_pct,
A.is_unite_buy,
A.industry_tp_dsc,
A.transferor_area_code,
A.target_industry,
A.ut_target_valuation_2,
A.trnsfr_department
FROM table1 A
LEFT JOIN table2 B
ON A.transferor_name = B.en_name
LEFT JOIN table3 C
ON A.transferor_name = C.search_company_name
LEFT JOIN table4 D 
ON A.target_name = D.search_company_name