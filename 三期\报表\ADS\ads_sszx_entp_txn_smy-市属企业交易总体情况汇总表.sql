WITH data_gq AS(
-- 产权
SELECT 
DISTINCT
'产权转让'                       AS bus_type,        -- 业务类型
substr(A.transaction_date,0,7)  AS deal_date,       -- 成交日期
A.project_no                    AS prj_no,      -- 项目编号
A.assignee_name                AS assignee_name,   -- 受让方名称
A.project_status                AS proj_type,       -- （公开/非公开）
B.investor_type                 AS investor_type,   -- 投资人类型
B.mix_own_type                  AS mix_own_type,    -- 混改类型
B.proj_category                 AS proj_category,   -- 混改/压减/其他
B.tech_asset_amt                AS tech_asset_amt,  -- 涉及技术资产金额
B.land_use_amt                  AS land_use_amt,    -- 涉及土地使用权金额
B.bond_amt                      AS bond_amt,        -- 涉及债券转让金额
A.department                    AS proj_belong_dept_name, -- 项目所属部门
CASE WHEN A.is_unite_buy = 1 and fnl_trnsfr_pct IS NOT NULL THEN A.fnl_trnsfr_pct*A.transaction_amount 
  ELSE A.transaction_amount END AS deal_price,      -- 成交价格(万元)
CASE WHEN A.is_unite_buy != 1 or fnl_trnsfr_pct IS NULL THEN 100 ELSE A.fnl_trnsfr_pct END AS fnl_trnsfr_pct,   -- 最终受让比例
C.oasset_reg_org_dsc as oasset_reg_org_dsc, -- 受让方国资监管机构
C.custd_org_depdc_prov as custd_org_depdc_prov --监管机构属地(省)代码
FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
LEFT JOIN std.std_bl_eqty_prj_data_d B -- 产权转让项目数据纠正表-补录 
ON A.project_no = B.proj_no AND B.investor_name = A.assignee_name AND A.dt = B.dt
LEFT JOIN
(
SELECT  'BJHL'||cj.prj||'CQZR' AS bsn_prj_wrd -- 业务项目关键字 （关联1）
       ,cj.buyer AS  buyer -- 受买方ID
       ,cj.buyer_nm AS buyer_nm -- 受让方名称(关联2)/联合体代表名称
       ,tyc.mbr_nm AS mbr_nm --联合体成员体名称
       ,ty.is_unite_buy AS is_unite_buy -- 是否联合受让方
       ,tyc.fnl_buy_pct AS fnl_buy_pct -- 最终受让比例
       ,ty.oasset_reg_org_dsc as oasset_reg_org_dsc -- 受让方国资监管机构
       ,ty.custd_org_depdc_prov as custd_org_depdc_prov --监管机构属地(省)代码
FROM std.std_bjhl_tcqzr_yxsrf_cy_d tyc
LEFT JOIN std.std_bjhl_tcqzr_yxsrfxx_d ty
ON tyc.buyer_id = ty.id AND tyc.dt = ty.dt
LEFT JOIN std.std_bjhl_tcqzr_cjjlxx_d cj
ON cj.buyer = ty.id AND cj.dt = ty.dt
WHERE cj.prj is not null
AND ty.is_unite_buy = 1
AND tyc.fnl_buy_pct IS NOT NULL
AND tyc.dt = '${dmp_day}' 
) C
ON A.bsn_prj_wrd = C.bsn_prj_wrd AND A.assignee_name = C.buyer_nm
WHERE A.dt = '${dmp_day}'
AND COALESCE(B.state_owned_asset_regulator, A.transferor_regulatory) = '省级国资委监管'
AND A.transferor_area_code LIKE '110%'
AND A.department REGEXP '市属'
AND A.transaction_date IS NOT NULL
UNION ALL
-- 成员
SELECT 
DISTINCT
'产权转让'                       AS bus_type,        -- 业务类型
substr(A.transaction_date,0,7)  AS deal_date,       -- 成交日期
A.project_no                    AS prj_no,      -- 项目编号
A.assignee_name                AS assignee_name,   -- 受让方名称
A.project_status                AS proj_type,       -- （公开/非公开）
B.investor_type                 AS investor_type,   -- 投资人类型
B.mix_own_type                  AS mix_own_type,    -- 混改类型
B.proj_category                 AS proj_category,   -- 混改/压减/其他
B.tech_asset_amt                AS tech_asset_amt,  -- 涉及技术资产金额
B.land_use_amt                  AS land_use_amt,    -- 涉及土地使用权金额
B.bond_amt                      AS bond_amt,        -- 涉及债券转让金额
A.department                    AS proj_belong_dept_name, -- 项目所属部门
(C.fnl_buy_pct/100) * A.transaction_amount AS deal_price,       -- 成交价格(万元)
C.fnl_buy_pct                   AS fnl_trnsfr_pct,   -- 最终受让比例
C.oasset_reg_org_dsc as oasset_reg_org_dsc, -- 受让方国资监管机构
C.custd_org_depdc_prov as custd_org_depdc_prov --监管机构属地(省)代码
FROM dws.dws_prtrigt_trsfer_d A -- 产权转让交易汇总表 
LEFT JOIN
(
SELECT  'BJHL'||cj.prj||'CQZR' AS bsn_prj_wrd -- 业务项目关键字 （关联1）
       ,cj.buyer -- 受买方ID
       ,cj.buyer_nm -- 受让方名称(关联2)/联合体代表名称
       ,tyc.mbr_nm --联合体成员体名称
       ,ty.is_unite_buy -- 是否联合受让方
       ,tyc.fnl_buy_pct -- 最终受让比例
       ,ty.oasset_reg_org_dsc as oasset_reg_org_dsc -- 受让方国资监管机构
       ,ty.custd_org_depdc_prov as custd_org_depdc_prov --监管机构属地(省)代码
FROM std.std_bjhl_tcqzr_yxsrf_cy_d tyc
LEFT JOIN std.std_bjhl_tcqzr_yxsrfxx_d ty
ON tyc.buyer_id = ty.id AND tyc.dt = ty.dt
LEFT JOIN std.std_bjhl_tcqzr_cjjlxx_d cj
ON cj.buyer = ty.id AND cj.dt = ty.dt
WHERE cj.prj is not null
AND ty.is_unite_buy = 1
AND tyc.fnl_buy_pct IS NOT NULL
AND tyc.dt = '${dmp_day}' 
) C
ON A.bsn_prj_wrd = C.bsn_prj_wrd AND A.assignee_name = C.buyer_nm
LEFT JOIN std.std_bl_eqty_prj_data_d B -- 产权转让项目数据纠正表-补录 
ON A.project_no = B.proj_no AND C.mbr_nm = B.investor_name AND A.dt = B.dt
WHERE A.dt = '${dmp_day}'
AND COALESCE(B.state_owned_asset_regulator, A.transferor_regulatory) = '省级国资委监管'
AND A.transferor_area_code LIKE '110%'
AND A.department REGEXP '市属'
AND A.transaction_date IS NOT NULL
AND C.fnl_buy_pct IS NOT NULL
AND A.is_unite_buy = 1
)
-- ,data_gq_max AS (
--   SELECT 
--     bus_type,
--     deal_date,
--     project_no,
--     assignee_name,
--     proj_type,
--     investor_type,
--     mix_own_type,
--     proj_category,
--     tech_asset_amt,
--     land_use_amt,
--     bond_amt,
--     proj_belong_dept_name,
--     deal_price
--   FROM (
--     SELECT 
--       *,
--       ROW_NUMBER() OVER (
--         PARTITION BY bus_type, deal_date, project_no, assignee_name, proj_type, 
--                     investor_type, mix_own_type, proj_category, tech_asset_amt, 
--                     land_use_amt, bond_amt, proj_belong_dept_name
--         ORDER BY deal_price DESC
--       ) AS rn
--     FROM data_gq
--   ) ranked
--   WHERE rn = 1
-- )
,data_zz AS (
SELECT
DISTINCT
'企业增资' AS bus_type, -- 业务类型
substr(B.deal_date,0,7)         AS deal_date,                      -- 成交日期
B.`project_code`                AS prj_no,                        -- 项目编号
B.`buyer_name`                  AS investor,                       -- 投资人
A.transparency                  AS proj_type,                      -- 公开/非公开
CASE WHEN 
    A.investor_type IS NOT NULL THEN A.investor_type 
  ELSE B.investor_economy_type   
END                             AS investor_type,                  -- 投资人类型 
A.`bond_amt`                    AS bond_amt,                       -- 涉及债权转让金额 
A.`land_use_amt`                AS land_use_amt,                   -- 涉及土地使用权金额 
A.`tech_asset_amt`              AS tech_asset_amt,                 -- 涉及技术资产金额
A.single_investor_cap_inc_amt   AS total_amt                       -- 交易总金额 
FROM dwd.dwd_entp_incptl_trsfer_ext_d A -- 增资交易汇总_扩展表
LEFT JOIN dws.dws_entp_incptl_trsfer_d B -- 增资交易汇总表
ON A.project_code = B.project_code
AND A.buyer_name = B.buyer_name
AND A.dt = B.dt
WHERE A.dt = '${dmp_day}'
AND A.`custd_org_depdc_prov` LIKE '110%' -- 北京市(包括北京市各区)
AND B.`fincer_oasset_reg_org` = '省级国资委监管'
AND B.prj_blng_dept REGEXP '市属'
AND B.`deal_date` IS NOT NULL
UNION ALL 
-- 同时进行项目再提取展示
SELECT
DISTINCT
'企业增资' AS bus_type, -- 业务类型
substr(B.deal_date,0,7)         AS deal_date,                      -- 成交日期
B.`project_code`                AS prj_no,                        -- 项目编号
B.`buyer_name`                  AS investor,                       -- 投资人
T.trans_type                    AS proj_type,                      -- 公开/非公开
CASE WHEN 
    A.investor_type IS NOT NULL THEN A.investor_type 
  ELSE B.investor_economy_type   
END                             AS investor_type,                  -- 投资人类型 
A.`bond_amt`                    AS bond_amt,                       -- 涉及债权转让金额 
A.`land_use_amt`                AS land_use_amt,                   -- 涉及土地使用权金额 
A.`tech_asset_amt`              AS tech_asset_amt,                 -- 涉及技术资产金额
A.single_investor_cap_inc_amt   AS total_amt                       -- 交易总金额 
FROM dwd.dwd_entp_incptl_trsfer_ext_d A -- 增资交易汇总_扩展表
LEFT JOIN dws.dws_entp_incptl_trsfer_d B -- 增资交易汇总表
ON A.project_code = B.project_code
AND A.buyer_name = B.buyer_name
AND A.dt = B.dt
LEFT JOIN (
    SELECT DISTINCT
        a.project_code,
        a.project_name,
        a.deal_date,
        CASE 
            WHEN b.project_count > 1 THEN '同时进行'
            ELSE 
                CASE 
                    WHEN a.project_code LIKE 'G6%' THEN '公开'
                    WHEN a.project_code LIKE 'G7%' OR a.project_code LIKE 'G8%' THEN '非公开'
                END
        END AS trans_type
    FROM dws.dws_entp_incptl_trsfer_d a
    LEFT JOIN (
        SELECT 
            project_name,
            deal_date,
            COUNT(project_t) AS project_count
        FROM (
            SELECT DISTINCT 
                project_name,
                deal_date,
                CASE 
                    WHEN project_code LIKE 'G6%' THEN '公开'
                    WHEN project_code LIKE 'G7%' OR project_code LIKE 'G8%' THEN '非公开' 
                END AS project_t
            FROM dws.dws_entp_incptl_trsfer_d
            WHERE dt = '${dmp_day}'
        ) b1
        GROUP BY project_name, deal_date
    ) b 
    ON a.project_name = b.project_name 
    AND a.deal_date = b.deal_date
    WHERE a.dt = '${dmp_day}'
) T -- 判断是否同时进行
ON B.project_code = T.project_code
AND B.deal_date = T.deal_date
WHERE A.dt = '${dmp_day}'
AND A.`custd_org_depdc_prov` LIKE '110%' -- 北京市(包括北京市各区)
AND B.`fincer_oasset_reg_org` = '省级国资委监管'
AND B.prj_blng_dept REGEXP '市属'
AND B.`deal_date` IS NOT NULL
AND T.trans_type = '同时进行'
)
-- ,data_zz_max AS (
--   SELECT 
--     bus_type,
--     deal_date,
--     proj_id,
--     proj_type,
--     investor_type,
--     bond_amt,
--     land_use_amt,
--     tech_asset_amt,
--     total_amt
--   FROM (
--     SELECT 
--       d.*,
--       ROW_NUMBER() OVER (
--         PARTITION BY bus_type,deal_date, proj_id, proj_type, investor_type, bond_amt, land_use_amt, tech_asset_amt
--         ORDER BY total_amt DESC
--       ) AS rn
--     FROM data_zz d
--   ) ranked
--   WHERE rn = 1
-- )
-- 产权
SELECT
bus_type, -- 业务类型
deal_date, -- 成交日期
count(distinct prj_no) AS deal_qty, -- 成交数量
SUM(CASE WHEN investor_type = '市属国有' THEN 1 ELSE 0 END) AS st_own_qty, -- 市属国有成交数量
SUM(CASE WHEN investor_type = '市属国有' THEN deal_price ELSE 0 END) AS st_own_amt, -- 市属国有成交金额
SUM(CASE WHEN investor_type IN ('其他国有','央属国有') THEN 1 ELSE 0 END) AS oth_own_qty, -- 其他国有成交数量
SUM(CASE WHEN investor_type IN ('其他国有','央属国有') THEN deal_price ELSE 0 END) AS oth_own_amt, -- 其他国有成交金额
SUM(CASE WHEN investor_type = '民营' THEN 1 ELSE 0 END) AS priv_qty, -- 民营成交数量
SUM(CASE WHEN investor_type = '民营' THEN deal_price ELSE 0 END) AS priv_amt, -- 民营成交金额
SUM(CASE WHEN investor_type = '外资' THEN 1 ELSE 0 END) AS for_qty, -- 外资成交数量
SUM(CASE WHEN investor_type = '外资' THEN deal_price ELSE 0 END) AS for_amt, -- 外资成交金额
SUM(CASE WHEN investor_type = '个人' THEN 1 ELSE 0 END) AS indv_qty, -- 个人成交数量
SUM(CASE WHEN investor_type = '个人' THEN deal_price ELSE 0 END) AS indv_amt, -- 个人成交金额
COUNT(DISTINCT CASE WHEN bond_amt != 0 OR bond_amt IS NOT NULL THEN prj_no END) AS bond_tran_qty, -- 涉及债券转让成交数量
SUM(CASE WHEN bond_amt!= 0 OR bond_amt IS NOT NULL THEN bond_amt ELSE 0 END) AS bond_tran_amt, -- 涉及债券转让成交金额
COUNT(DISTINCT CASE WHEN land_use_amt!= 0 OR land_use_amt IS NOT NULL THEN prj_no END) AS land_use_qty, -- 涉及土地使用权成交数量
SUM(CASE WHEN land_use_amt!= 0 OR land_use_amt IS NOT NULL THEN land_use_amt ELSE 0 END) AS land_use_amt, -- 涉及土地使用权成交金额
COUNT(DISTINCT CASE WHEN tech_asset_amt!= 0 OR tech_asset_amt IS NOT NULL THEN prj_no END) AS tech_asset_qty, -- 涉及技术资产成交数量
SUM(CASE WHEN tech_asset_amt!= 0 OR tech_asset_amt IS NOT NULL THEN tech_asset_amt ELSE 0 END) AS tech_asset_amt, -- 涉及技术资产成交金额
'' AS st_own_trans, -- 其中市属国有受让
proj_type  AS proj_type, -- 项目类型
proj_category AS proj_category -- 项目分类
FROM data_gq
WHERE investor_type IN ('市属国有','其他国有','央属国有','民营','外资','个人')
GROUP BY bus_type, deal_date, proj_belong_dept_name, proj_type,proj_category
UNION ALL
-- 产权(其中市属国有受让):受让方的国资监管机构为省级国资委监管并且监管机构所属省110开头
SELECT
bus_type, -- 业务类型
deal_date, -- 成交日期
count(distinct prj_no) AS deal_qty, -- 成交数量
SUM(CASE WHEN investor_type = '市属国有' THEN 1 ELSE 0 END) AS st_own_qty, -- 市属国有成交数量
SUM(CASE WHEN investor_type = '市属国有' THEN deal_price ELSE 0 END) AS st_own_amt, -- 市属国有成交金额
SUM(CASE WHEN investor_type IN ('其他国有','央属国有') THEN 1 ELSE 0 END) AS oth_own_qty, -- 其他国有成交数量
SUM(CASE WHEN investor_type IN ('其他国有','央属国有') THEN deal_price ELSE 0 END) AS oth_own_amt, -- 其他国有成交金额
SUM(CASE WHEN investor_type = '民营' THEN 1 ELSE 0 END) AS priv_qty, -- 民营成交数量
SUM(CASE WHEN investor_type = '民营' THEN deal_price ELSE 0 END) AS priv_amt, -- 民营成交金额
SUM(CASE WHEN investor_type = '外资' THEN 1 ELSE 0 END) AS for_qty, -- 外资成交数量
SUM(CASE WHEN investor_type = '外资' THEN deal_price ELSE 0 END) AS for_amt, -- 外资成交金额
SUM(CASE WHEN investor_type = '个人' THEN 1 ELSE 0 END) AS indv_qty, -- 个人成交数量
SUM(CASE WHEN investor_type = '个人' THEN deal_price ELSE 0 END) AS indv_amt, -- 个人成交金额
COUNT(DISTINCT CASE WHEN bond_amt != 0 OR bond_amt IS NOT NULL THEN prj_no END) AS bond_tran_qty, -- 涉及债券转让成交数量
SUM(CASE WHEN bond_amt!= 0 OR bond_amt IS NOT NULL THEN bond_amt ELSE 0 END) AS bond_tran_amt, -- 涉及债券转让成交金额
COUNT(DISTINCT CASE WHEN land_use_amt!= 0 OR land_use_amt IS NOT NULL THEN prj_no END) AS land_use_qty, -- 涉及土地使用权成交数量
SUM(CASE WHEN land_use_amt!= 0 OR land_use_amt IS NOT NULL THEN land_use_amt ELSE 0 END) AS land_use_amt, -- 涉及土地使用权成交金额
COUNT(DISTINCT CASE WHEN tech_asset_amt!= 0 OR tech_asset_amt IS NOT NULL THEN prj_no END) AS tech_asset_qty, -- 涉及技术资产成交数量
SUM(CASE WHEN tech_asset_amt!= 0 OR tech_asset_amt IS NOT NULL THEN tech_asset_amt ELSE 0 END) AS tech_asset_amt, -- 涉及技术资产成交金额
'市属国有受让' AS st_own_trans, -- 市属国有受让
proj_type AS proj_type, -- 项目类型 
proj_category AS proj_category -- 项目分类
FROM data_gq
WHERE investor_type IN ('市属国有','其他国有','央属国有','民营','外资','个人')
AND oasset_reg_org_dsc = '省级国资委监管'
AND custd_org_depdc_prov LIKE '110%'
AND proj_belong_dept_name REGEXP '市属'
GROUP BY bus_type, deal_date,proj_type,proj_category

UNION ALL
-- 增资
SELECT
bus_type, -- 业务类型
deal_date, -- 成交日期
count(distinct prj_no) AS deal_qty, -- 成交数量
SUM(CASE WHEN investor_type = '市属国有' THEN 1 ELSE 0 END) AS st_own_qty, -- 市属国有成交数量
SUM(CASE WHEN investor_type = '市属国有' THEN total_amt ELSE 0 END) AS st_own_amt, -- 市属国有成交金额
SUM(CASE WHEN investor_type IN ('其他国有','央属国有') THEN 1 ELSE 0 END) AS oth_own_qty, -- 其他国有成交数量
SUM(CASE WHEN investor_type IN ('其他国有','央属国有') THEN total_amt ELSE 0 END) AS oth_own_amt, -- 其他国有成交金额
SUM(CASE WHEN investor_type = '民营' THEN 1 ELSE 0 END) AS priv_qty, -- 民营成交数量
SUM(CASE WHEN investor_type = '民营' THEN total_amt ELSE 0 END) AS priv_amt, -- 民营成交金额
SUM(CASE WHEN investor_type = '外资' THEN 1 ELSE 0 END) AS for_qty, -- 外资成交数量
SUM(CASE WHEN investor_type = '外资' THEN total_amt ELSE 0 END) AS for_amt, -- 外资成交金额
SUM(CASE WHEN investor_type = '个人' THEN 1 ELSE 0 END) AS indv_qty, -- 个人成交数量
SUM(CASE WHEN investor_type = '个人' THEN total_amt ELSE 0 END) AS indv_amt, -- 个人成交金额
COUNT(DISTINCT CASE WHEN bond_amt != 0 OR bond_amt IS NOT NULL THEN prj_no END) AS bond_tran_qty, -- 涉及债券转让成交数量
SUM(CASE WHEN bond_amt != 0 OR bond_amt IS NOT NULL THEN bond_amt ELSE 0 END) AS bond_tran_amt, -- 涉及债券转让成交金额
COUNT(DISTINCT CASE WHEN land_use_amt!= 0 OR land_use_amt IS NOT NULL THEN prj_no END) AS land_use_qty, -- 涉及土地使用权成交数量
SUM(CASE WHEN land_use_amt != 0 OR land_use_amt IS NOT NULL THEN land_use_amt ELSE 0 END) AS land_use_amt, -- 涉及土地使用权成交金额
COUNT(DISTINCT CASE WHEN tech_asset_amt!= 0 OR tech_asset_amt IS NOT NULL THEN prj_no END) AS tech_asset_qty, -- 涉及技术资产成交数量
SUM(CASE WHEN tech_asset_amt != 0 OR tech_asset_amt IS NOT NULL THEN tech_asset_amt ELSE 0 END) AS tech_asset_amt, -- 涉及技术资产成交金额
'' AS st_own_trans, -- 市属国有受让
proj_type AS proj_type, -- 项目类型
''  AS proj_category
FROM data_zz
WHERE investor_type IN ('市属国有','其他国有','央属国有','民营','外资','个人')
GROUP BY bus_type, deal_date, proj_type