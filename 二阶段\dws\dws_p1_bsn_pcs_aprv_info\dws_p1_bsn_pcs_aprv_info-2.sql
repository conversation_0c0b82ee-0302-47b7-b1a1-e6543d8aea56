SELECT  --  'bjhl'                    AS BSN_SRC                           --业务来源,来源系统编码
         -- ,'livebos.os_historystep'        AS EDW_DT_SRC_TBL                    --EDW数据来源主表名
         -- ,'${dmp_day}'         AS dmp_day                       --EDW数据日期
		 -- ,DATE_FORMAT(current_timestamp(),'yyyy-MM-dd') AS EDW_STM_DT   --EDW系统日期 
         T3.Aprv_Pcs_Id                  AS APRV_PCS_ID                   --审批流程ID/审批流程ID
        ,T3.Prj_Wrd                      AS PRJ_WRD                       --项目关键字/项目关键字
        ,T3.Pcs_Id                       AS PCS_ID                        --流程ID/流程ID
        ,T3.Pcs_Cd                       AS PCS_CD                        --流程代码/流程代码
        ,'房屋出租合同信息审核'             AS PCS_NM                        --流程名称/流程名称
        ,T3.Pcs_Title                    AS PCS_TITLE                     --流程标题/流程标题
        ,T3.Pcs_Stat_Cd                  AS PCS_STAT_CD                   --流程状态代码/流程状态代码
        ,T3.Pcs_Stat_Cd_Dsc              AS PCS_STAT_CD_DSC               --流程状态代码描述/流程状态代码描述
        ,T3.Pmer_Id                      AS PMER_ID                       --发起人ID/发起人ID
        ,T3.Pmer_Code                    AS PMER_CODE                     --发起人编码/发起人编码
        ,T3.Pmer_Nm                      AS PMER_NM                       --发起人名称/发起人名称
        ,T3.Pmer_Blng_Dept_Id            AS PMER_BLNG_DEPT_ID             --发起人所属部门ID/发起人所属部门ID
        ,T3.Pmer_Blng_Dept_Nm            AS PMER_BLNG_DEPT_NM             --发起人所属部门名称/发起人所属部门名称
        ,T3.Pmer_Blng_Cetr_Id            AS PMER_BLNG_CETR_ID             --发起人所属中心ID/发起人所属中心ID
        ,T3.Pmer_Blng_Cetr_Nm            AS PMER_BLNG_CETR_NM             --发起人所属中心名称/发起人所属中心名称
        ,T3.Itt_Tm                       AS ITT_TM                        --发起时间/发起时间
        ,T3.Audit_Tm                     AS AUDIT_PASS_TM                 --审核通过时间/审核通过时间
        ,CASE WHEN T3.MAX_Audit_Tm IS NOT NULL 
		      THEN cast((unix_timestamp(T3.MAX_Audit_Tm)-unix_timestamp(T3.Itt_Tm))/86400 as int)||'天'
                   ||cast((unix_timestamp(T3.MAX_Audit_Tm)-unix_timestamp(T3.Itt_Tm))/3600 as int)%24||'小时'
                   ||cast((unix_timestamp(T3.MAX_Audit_Tm)-unix_timestamp(T3.Itt_Tm))/60 as int)%60||'分'
                   || cast((unix_timestamp(T3.MAX_Audit_Tm)-unix_timestamp(T3.Itt_Tm))%60 as int)||'秒' 
  			  ELSE NULL 
		END                              AS PCS_APRV_TOT_DURT             --NULL/流程审批总时长
        ,T3.Pcs_Node_Id                  AS PCS_NODE_ID                   --流程节点ID/流程节点ID
        ,T3.PCS_NODE_NM                   --流程节点名称/流程节点名称
        ,lag(T3.Auditor_Nm,1)over(order by T3.audit_tm)  AS NODE_STER     --NULL/节点提交人名称
        ,T3.Pcs_Ariv_Tm                  AS PCS_ARIV_TM                   --流程到达时间/流程到达时间
        ,T3.Auditor_Id                   AS AUDITOR_ID                    --审核人ID/审核人ID
        ,T3.Auditor_Code                 AS AUDITOR_CODE                  --审核人编码/审核人编码
        ,T3.Auditor_Nm                   AS AUDITOR_NM                    --审核人名称/审核人名称
        ,T3.Auditor_Blng_Dept_Id         AS AUDITOR_BLNG_DEPT_ID          --审核人所属部门ID/审核人所属部门ID
        ,T3.Auditor_Blng_Dept_Nm         AS AUDITOR_BLNG_DEPT_NM          --审核人所属部门名称/审核人所属部门名称
        ,T3.Auditor_Blng_Cetr_Id         AS AUDITOR_BLNG_CETR_ID          --审核人所属中心ID/审核人所属中心ID
        ,T3.Auditor_Blng_Cetr_Nm         AS AUDITOR_BLNG_CETR_NM          --审核人所属中心名称/审核人所属中心名称
        ,T3.Audit_Tm                     AS AUDIT_TM                      --审核时间/审核时间
        ,T3.Audit_Stat                   AS AUDIT_STAT                    --审核状态/审核状态
        ,T3.EXEC_ACT                                                     --执行动作/执行动作
        ,CASE WHEN T3.Audit_Tm IS NOT NULL 
		      THEN cast((unix_timestamp(T3.Audit_Tm)-unix_timestamp(T3.Pcs_Ariv_Tm))/86400 as int)||'天'
                   ||cast((unix_timestamp(T3.Audit_Tm)-unix_timestamp(T3.Pcs_Ariv_Tm))/3600 as int)%24||'小时'
                   ||cast((unix_timestamp(T3.Audit_Tm)-unix_timestamp(T3.Pcs_Ariv_Tm))/60 as int)%60||'分'
                   || cast((unix_timestamp(T3.Audit_Tm)-unix_timestamp(T3.Pcs_Ariv_Tm))%60 as int)||'秒'
  			  ELSE NULL 
		END                              AS NODE_AUDIT_DURT               --NULL/节点审核时长
        ,T3.Prj_Stg                       AS PRJ_STG                       --项目阶段/项目阶段
        ,T3.Prj_Tp                        AS PRJ_TP                        --项目类型/项目类型
        ,T3.Stm_Tp                        AS STM_TP                        --系统类型/系统类型
        ,T3.Prewer_Id                    AS PREWER_ID                     --待审核人ID/待审核人ID
        ,T3.Prewer_Nm                    AS PREWER_NM                     --待审核人名称/待审核人名称
        ,T3.Audit_Rmrk                   AS AUDIT_RMRK                    --审核备注/审核备注
        ,T4.Prj_Id                       AS PRJ_ID                        --项目编号/项目编号
        ,T4.Prj_Nm                       AS PRJ_NM                        --项目名称/项目名称
		,T4.Prj_Bsn_Tp_Cd_Dsc            AS Prj_Bsn_Tp                    --项目业务类型代码描述/项目业务类型
        ,T4.Org_Nm                       AS PRJ_BLNG_CETR                 --NULL/项目所属中心
        ,T4.Prj_Blng_Dept_Nm             AS PRJ_BLNG_DEPT                 --项目所属部门名称/项目所属部门
        ,T4.Prj_Prin_Nm                  AS PRJ_PRIN                      --项目负责人名称/项目负责人
        ,T6.Org_Nm                       AS PRJ_PRIN_DEPT                 --组织名称/项目负责人部门
        ,CASE WHEN T4.Prj_Tp_Cd_Dsc IS NULL
              THEN '正式披露'
              ELSE T4.Prj_Tp_Cd_Dsc			  
		END                             AS PUBL_TP                       --NULL/披露类型
		,T3.Sort_No                       AS Sort_No                       --排序号
FROM 
(SELECT  DISTINCT 
         T3.Aprv_Pcs_Id                  AS APRV_PCS_ID                   --审批流程ID/审批流程ID
        ,T.Prj_Wrd                      AS PRJ_WRD                       --项目关键字/项目关键字
        ,T.Pcs_Id                       AS PCS_ID                        --流程ID/流程ID
        ,T.Pcs_Cd                       AS PCS_CD                        --流程代码/流程代码
        ,'房屋出租合同信息审核'             AS PCS_NM                        --流程名称/流程名称
        ,T.Pcs_Title                    AS PCS_TITLE                     --流程标题/流程标题
        ,T.Pcs_Stat_Cd                  AS PCS_STAT_CD                   --流程状态代码/流程状态代码
        ,T.Pcs_Stat_Cd_Dsc              AS PCS_STAT_CD_DSC               --流程状态代码描述/流程状态代码描述
        ,T.Pmer_Id                      AS PMER_ID                       --发起人ID/发起人ID
        ,T.Pmer_Code                    AS PMER_CODE                     --发起人编码/发起人编码
        ,T.Pmer_Nm                      AS PMER_NM                       --发起人名称/发起人名称
        ,T.Pmer_Blng_Dept_Id            AS PMER_BLNG_DEPT_ID             --发起人所属部门ID/发起人所属部门ID
        ,T.Pmer_Blng_Dept_Nm            AS PMER_BLNG_DEPT_NM             --发起人所属部门名称/发起人所属部门名称
        ,T.Pmer_Blng_Cetr_Id            AS PMER_BLNG_CETR_ID             --发起人所属中心ID/发起人所属中心ID
        ,T.Pmer_Blng_Cetr_Nm            AS PMER_BLNG_CETR_NM             --发起人所属中心名称/发起人所属中心名称
        ,T.Itt_Tm                       AS ITT_TM                        --发起时间/发起时间
        ,T3.Audit_Tm                     AS AUDIT_PASS_TM                 --审核通过时间/审核通过时间
        ,T3.MAX_Audit_Tm                              --审核通过最大时间
        ,T3.Pcs_Node_Id                  AS PCS_NODE_ID                   --流程节点ID/流程节点ID
        ,CASE T3.Pcs_Node_Id
                       WHEN '1' THEN '结束'
                       WHEN '5' THEN '业务初审'
                       WHEN '8' THEN '填写表单'
                       WHEN '11' THEN '部门负责人审核'
                       WHEN '13' THEN '交易审核部审核员审核'
                       WHEN '18' THEN '交易审核部负责人审核'
                       WHEN '20' THEN '律师审核'
                       ELSE '其他'
          END                            AS PCS_NODE_NM                   --流程节点名称/流程节点名称
        ,T3.Pcs_Ariv_Tm                  AS PCS_ARIV_TM                   --流程到达时间/流程到达时间
        ,T3.Auditor_Id                   AS AUDITOR_ID                    --审核人ID/审核人ID
        ,T3.Auditor_Code                 AS AUDITOR_CODE                  --审核人编码/审核人编码
        ,T3.Auditor_Nm                   AS AUDITOR_NM                    --审核人名称/审核人名称
        ,T3.Auditor_Blng_Dept_Id         AS AUDITOR_BLNG_DEPT_ID          --审核人所属部门ID/审核人所属部门ID
        ,T3.Auditor_Blng_Dept_Nm         AS AUDITOR_BLNG_DEPT_NM          --审核人所属部门名称/审核人所属部门名称
        ,T3.Auditor_Blng_Cetr_Id         AS AUDITOR_BLNG_CETR_ID          --审核人所属中心ID/审核人所属中心ID
        ,T3.Auditor_Blng_Cetr_Nm         AS AUDITOR_BLNG_CETR_NM          --审核人所属中心名称/审核人所属中心名称
        ,T3.Audit_Tm                     AS AUDIT_TM                      --审核时间/审核时间
        ,T3.Audit_Stat                   AS AUDIT_STAT                    --审核状态/审核状态
        ,CASE WHEN T3.Exec_Act_Id  IN ('0') THEN '开始'
              WHEN T3.Exec_Act_Id  IN ('6','12','14','19','21') THEN '通过'
              WHEN T3.Exec_Act_Id  IN ('37') THEN '不通过'
              WHEN T3.Exec_Act_Id  IN ('39','33','31','29','25') THEN '退回'
              WHEN T3.Exec_Act_Id  IN ('9') THEN '提交'
              ELSE '其他'
         END                             AS EXEC_ACT                   --执行动作/执行动作
        ,T3.Prewer_Id                    AS PREWER_ID                     --待审核人ID/待审核人ID
        ,T3.Prewer_Nm                    AS PREWER_NM                     --待审核人名称/待审核人名称
        ,T3.Audit_Rmrk                   AS AUDIT_RMRK                    --审核备注/审核备注
        ,T.PRJ_STG                       AS PRJ_STG                       --项目阶段/项目阶段
        ,T.PRJ_TP                        AS PRJ_TP                        --项目类型/项目类型
        ,T.STM_TP                        AS STM_TP                        --系统类型/系统类型
		    ,T.SORT_NO                       AS SORT_NO                       --排序号
        ,T.PRJ_NM                        AS PRJ_NM                        --项目名称/项目名称
      
FROM 
(SELECT T3.*,MAX(T3.Audit_Tm)OVER(PARTITION BY T3.Pcs_Id) MAX_Audit_Tm
           FROM   dwd.dwd_bsn_pcs_aprv_fct  T3
		    where  T3.DT = '${dmp_day}'
           ) T3     --业务流程审批事实
INNER JOIN 
(SELECT  *
   FROM   dwd.dwd_bsn_pcs_fct   
		    WHERE DT = '${dmp_day}' 
        AND pcs_nm = '房屋出租合同信息审核' -- 只取【房屋出租合同信息审核】流程      
)T   --业务流程事实
ON  T3.PCS_NODE_ID = T.NODE_ID --流程节点ID
AND T3.PCS_ID = T.PCS_ID      --流程ID
AND T3.EXEC_ACT_NM = T.EXEC_ACTION_NM  --执行动作名称
) T3
LEFT JOIN (SELECT 
            t7.prj_id,
            t7.prj_wrd,
            T7.Prj_Nm,
            t7.prj_prin_id,
            t7.prj_bsn_tp_cd_dsc,
            t7.prj_blng_dept_nm,
            t7.prj_prin_nm,
            t7.prj_tp_cd_dsc,
            t9.org_nm
          FROM  dwd.dwd_prj_fct  T7
		  LEFT JOIN (SELECT Org_Id,Supr_Org_Id
                     FROM   dim.dim_org_info  
                     where DT = '${dmp_day}' 
                       AND EDW_STAR_DT <= '${dmp_day}'  
                       AND  '${dmp_day}' <  EDW_END_DT  
                       --AND  EDW_DLT_FLG<> 'D'
                     ) T8   --组织机构维
		 ON T7.Prj_Blng_Dept_Id=T8.Org_Id  --组织机构ID
		LEFT JOIN (SELECT Org_Id,org_nm
                     FROM   dim.dim_org_info  
                     where DT = '${dmp_day}' 
                       AND EDW_STAR_DT <= '${dmp_day}'  
                       AND  '${dmp_day}' <  EDW_END_DT  
                       --AND  EDW_DLT_FLG<> 'D'
                     ) T9   --组织机构维
		 ON T8.Supr_Org_Id=T9.Org_Id  --组织机构ID
          where  DT = '${dmp_day}'
          ) T4   --项目事实
ON t3.prj_wrd=T4.Prj_Wrd  --项目关键字
LEFT JOIN (SELECT *
          FROM  dim.dim_user_info  
          where DT = '${dmp_day}' 
            AND EDW_STAR_DT <= '${dmp_day}'  
            AND   '${dmp_day}' <  EDW_END_DT  
            --AND    EDW_DLT_FLG<> 'D'
			AND   usr_wrd like '%ZT'
          ) T5   --用户维
ON T4.Prj_Prin_Id=T5.Usr_Id  --用户ID
LEFT JOIN (SELECT *
          FROM  dim.dim_org_info  
          where DT = '${dmp_day}' 
            AND EDW_STAR_DT <= '${dmp_day}'  
            AND   '${dmp_day}' <  EDW_END_DT  
            --AND    EDW_DLT_FLG<> 'D'
          ) T6   --组织机构维
ON T5.Blng_Org_Wrd=T6.Org_Wrd  --机构关键字
WHERE T3.Pcs_Id IS NOT NULL