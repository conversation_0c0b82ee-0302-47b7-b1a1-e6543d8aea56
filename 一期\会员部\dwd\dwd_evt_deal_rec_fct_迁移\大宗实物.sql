SELECT      T1.PLTFRM_PRJ_ID                            AS DEAL_REC_ID                      --成交记录号/成交记录ID
            ,NULL                                       AS PRJ_WRD                          --项目ID/项目关键字
            ,'BJHL'||T2.PRJ||'DZSW'                     AS BSN_PRJ_WRD                      --项目/业务项目关键字
            ,TT.RLTV_INVEST                             AS CUST_WRD                         --客户号/客户关键字
            ,T2.BUYER                                   AS BSN_BUYER_ID                     --受让方/业务买受方ID
            ,T2.ID                                      AS BSN_DEAL_REC_ID                  --ID/业务成交记录ID
            ,NULL                                       AS DEAL_NO                          --成交编号/成交编号
            ,NULL                                       AS ETRS_CGY_CD                      --委托类别/委托类别代码
            ,NULL                                       AS ETRS_CGY_CD_DSC                  --码值/委托类别代码描述
            ,NULL                                       AS CURR_CD                          --币种/币种代码
            ,NULL                                       AS CURR_CD_DSC                      --码值/币种代码描述
            ,NULL                                       AS CPTL_ACC                         --资金账户/资金账户
            ,NULL                                       AS TXN_PRC                          --成交价格(元)/成交价格
            ,NULL                                       AS DEAL_NUM                         --成交数量(手)/成交数量
            ,NULL                                       AS DEAL_AMT                         --成交金额/成交金额
            ,NULL                                       AS DEAL_DT                          --成交日期/成交日期
            ,NULL                                       AS DEAL_TM                          --成交时间/成交时间
            ,REGEXP_REPLACE(T2.DEAL_DATE,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3')  AS BSN_REC_DEAL_DT                  --成交日期/业务记录成交日期
            ,NULL                                       AS CFRM_DT                          --确认日期/确认日期
            ,NULL                                       AS CFRM_TM                          --确认时间/确认时间
            ,NULL                                       AS DELVRY_DT                        --交割日期/交割日期
            ,NULL                                       AS DELVRY_TM                        --交割时间/交割时间
            ,NULL                                       AS DEAL_STAT_CD                     --成交状态/成交状态代码
            ,NULL                                       AS DEAL_STAT_CD_DSC                 --码值/成交状态代码描述
            ,NULL                                       AS IS_STM_COLL_BROGE                --是否系统收取手续费/是否系统收取手续费
            ,NULL                                       AS BROGE                            --手续费/手续费
            ,NULL                                       AS THIRD_DEPST_SERIAL_NO            --第三方入金流水号/第三方入金流水号
            ,NULL                                       AS PAY_STAT_CD                      --付款状态/付款状态代码
            ,NULL                                       AS PAY_STAT_CD_DSC                  --码值/付款状态代码描述
            ,NULL                                       AS CDAY_CPTL_DTL_SERIAL_NO          --当日资金明细流水号/当日资金明细流水号
            ,NULL                                       AS DELVER_ID                        --交割人/交割人ID
            ,NULL                                       AS DELVER_NM                        --交割人姓名/交割人姓名
            ,T2.TXN_VCHR_IS_CAN_PRT                     AS IS_SURE_PRNT                     --是否可以打印/是否可以打印
            ,T2.TXN_VCHR_PRT_PAGES_NUM                  AS TXN_VCHR_PRNT_PAGNMB             --交易凭证打印页数/交易凭证打印页数
            ,NULL                                       AS PRNT_COPS                        --打印份数/打印份数
            ,NULL                                       AS PRJ_NM                           --NULL/项目名称
            ,TT.buyer_repre_nm                                       AS TRSFEE_NM                        --NULL/受让方名称
            ,T2.CNTR_SIGN_DT                            AS CONT_SIGN_DT                     --合同签订日期/合同签订日期
            ,T2.CNTR_EFF_DT                             AS CONT_EFF_DT                      --合同生效日期/合同生效日期
            ,REGEXP_REPLACE(T2.RSLT_PBC_BEG_DT ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3') AS RSLT_PUBTY_START_DT --结果公示起始时间/结果公示起始日期
            ,REGEXP_REPLACE(T2.RSLT_PBC_END_DT ,'([0-9]{4})([0-9]{2})([0-9]{2})','\$1-\$2-\$3') AS RSLT_PUBTY_END_DT --结果公示结束时间/结果公示结束日期
            ,T2.RSLT_PBC_CYC_DT                         AS RSLT_PUBTY_PRD_WKDY              --结果公示周期（工作日）/结果公示周期（工作日）
            ,NULL                                       AS FNL_TRNSFR_PCT                   --NULL/最终受让比例
            ,NULL                                       AS DEAL_UNIT_PRIC                   --NULL/成交单价
            ,T2.DEPOSIT_IS_TFR_TXN_AMT                  AS IS_MRGN_OF_TXN_PRC               --保证金是否转交易价款/是否保证金转交易价款
            ,T2.DEPOSIT_TFR_TXN_AMT_AMT_W_YUAN*10000    AS MRGN_OF_TXN_PRC_AMT              --NULL/保证金转交易价款金额
            ,T2.DEPOSIT_TPRICE_AMT_W_YUAN*10000         AS MRGN_TF_PRC_AMT                  --保证金转价款金额(万元)/保证金转价款金额
            ,NULL                                       AS MRGN_TF_PRC_TMPOT                --NULL/保证金转价款时点
            ,NULL                                       AS MRGN_TF_PRC_DT                   --NULL/保证金转价款日期
            ,NULL                                       AS AVALB_MRGN_AMT                   --NULL/可使用保证金金额
            ,T2.TO_ACC_AMT_AMT_W_YUAN*10000             AS TOACCT_PRC_AMT                   --NULL/到账价款金额
            ,T2.DEPOSIT_W_YUAN*10000                    AS MRGN_AMT                         --保证金金额（万元）/保证金金额
            ,NULL                                       AS MRGN_DISPL_MTH_CD                --NULL/保证金处置方式代码
            ,NULL                                       AS MRGN_DISPL_MTH_CD_DSC            --NULL/保证金处置方式代码描述
            ,T2.DEPOSIT_TPRICE_ORDR_ID                  AS MRGN_TF_PRC_ORDER_ID             --保证金转价款订单ID/保证金转价款订单ID
            ,T2.DEPOSIT_TPRICE_TM                       AS MRGN_TF_PRC_TM                   --保证金转价款时间/保证金转价款时间
            ,NULL                                       AS IS_MRGN_ADV_TSFT                 --NULL/是否保证金提前划出
            ,T2.PAY_MODE                                AS PY_MTH_CD --支付方式代码
            ,T2.PAY_MODE_DSC                            AS PY_MTH_CD_DSC                    --码值/支付方式代码描述
            ,T2.SRPLS_AMT_DISPL                         AS SURPL_PRC_SETL_MTH_CD --剩余价款结算方式代码
            ,T2.SRPLS_AMT_DISPL_DSC                     AS SURPL_PRC_SETL_MTH_CD_DSC        --NULL/剩余价款结算方式代码描述
            ,T2.FIRST_PAY_VALUE_W_YUAN*10000            AS DPAMT_AMT                        --首付金额(万元)/首付金额
            ,T2.INIT_PY_PCT*0.01                        AS FISSU_PAY_PECT                   --首期付款百分比/首期付款百分比
            ,T2.REMAIN_ENDTO_DT                         AS BLPT_EXPI_DT                     --尾款截止日期/尾款截止日期
            ,T2.SHLD_PY_AMT_AMT_W_YUAN*10000            AS PAYB_PRC                         --应支付价款（万元）/应支付价款
            ,NULL                                       AS SCROT_PRC_AMT                    --NULL/可划出价款金额
            ,T2.OTHR_RSN                                AS OTHR_REASON                      --其他原因/其他原因
            ,T2.SETTLEMENT                              AS SETL_MTH_CD --结算方式代码
            ,T2.SETTLEMENT_DSC                          AS SETL_MTH_CD_DSC                  --码值/结算方式代码描述
            ,T2.IS_FRN_CY_SETL                          AS IS_FRN_CUR_SETL                  --是否外币结算/是否外币结算
            ,T2.FRN_CY_CCY                              AS FRN_CUR_CURR_CD --外币币种代码
            ,T2.FRN_CY_CCY_DSC                          AS FRN_CUR_CURR_CD_DSC              --码值/外币币种代码描述
            ,T2.APNT_DAY_EXRT                           AS AGDT_EXRT                        --约定日汇率/约定日汇率
            ,T2.CNVR_FRN_CY_AMT_W_YUAN*10000            AS CONVT_FRN_CUR_AMT                --折算外币金额(万元)/折算外币金额
            ,T2.ACT_EXCHANGE_TYPE                       AS ACTL_TXN_MTH_CD --实际交易方式
            ,T2.ACT_EXCHANGE_TYPE_DSC                   AS ACTL_TXN_MTH_CD_DSC              --码值/实际交易方式代码描述
            ,T2.CHANGE_EXCHANGE_TYPE_RSN                AS CHAG_TXN_MTH_REASON              --更改交易方式原因/更改交易方式原因
            ,T2.TXN_ORG_CHK_OPIN                        AS TXN_ORG_AUDIT_OPIN               --交易机构审核意见/交易机构审核意见
            ,NULL                                       AS DEAL_TP_CD                       --NULL/成交类型代码
            ,NULL                                       AS DEAL_TP_CD_DSC                   --NULL/成交类型代码描述
            ,T2.TFR_FLG                                 AS PRC_TSFT_FLG_CD --价款划出标识代码
            ,T2.TFR_FLG_DSC                             AS PRC_TSFT_FLG_CD_DSC              --码值/价款划出标识代码描述
            ,T2.TFR_AMT_W_YUAN*10000                    AS ATSFT_AMT                        --已划出金额（万元）/已划出金额
            ,NULL                                       AS PRC_GOIN_BSN_ORDER_ID            --NULL/价款划入业务订单ID
            ,NULL                                       AS ORDER_NO_PLFORM                  --NULL/订单编号（平台）
            ,T2.CREATE_USER                             AS CATR_ID                          --创建人/创建人ID
            ,T13.NAME                                   AS CATR_NM                          --名称/创建人名称
            ,T2.CREATE_TIME                             AS CRT_TM                           --创建时间/创建时间
            ,T2.UDT_PSN                                 AS UPD_PSN_ID                       --更新人/更新人ID
            ,T14.NAME                                   AS UPD_PSN_NM                       --名称/更新人名称
            ,T2.UDT_TM                                  AS MOD_TM                           --更新时间/更新时间
            ,NULL                                       AS PRJ_PRIN_ID                      --NULL/项目负责人ID
            ,NULL                                       AS PRJ_PRIN_NM                      --NULL/项目负责人名称
            ,NULL                                       AS PRJ_PRIN_PASS_DT                 --NULL/项目负责人通过日期
            ,NULL                                       AS BSN_DEPT_PRIN_ID                 --NULL/业务部门负责人ID
            ,NULL                                       AS BSN_DEPT_PRIN_NM                 --NULL/业务部门负责人名称
            ,NULL                                       AS BSN_DEPT_PRIN_PASS_DT            --NULL/业务部门负责人通过日期
            ,NULL                                       AS TXN_AUDT_DEP_AUDITOR_ID          --NULL/交易审核部审核人ID
            ,NULL                                       AS TXN_AUDT_DEP_AUDITOR_NM          --NULL/交易审核部审核人名称
            ,NULL                                       AS TXN_AUDT_DEP_AUDITOR_PASS_DT     --NULL/交易审核部审核人通过日期
            ,NULL                                       AS TRAS_DEP_PRIN_ID                 --NULL/交易部负责人ID
            ,NULL                                       AS TRAS_DEP_PRIN_NM                 --NULL/交易部负责人名称
            ,NULL                                       AS TRAS_DEP_PRIN_PASS_DT            --NULL/交易部负责人通过日期
            ,NULL                                       AS EXG_AUDITOR_ID                   --NULL/交易所审核人ID
            ,NULL                                       AS EXG_AUDITOR_NM                   --NULL/交易所审核人名称
            ,NULL                                       AS EXG_AUDITOR_PASS_DT              --NULL/交易所审核人通过日期
            ,NULL                                       AS CETR_PRIN_ID                     --NULL/中心负责人ID
            ,NULL                                       AS CETR_PRIN_NM                     --NULL/中心负责人名称
            ,NULL                                       AS CETR_PRIN_PASS_DT                --NULL/中心负责人通过日期
            ,NULL                                       AS CONT_ATCH                        --NULL/合同附件
            ,NULL                                       AS IS_FST_TM_PRC_TSFT_AUDIT_PASS    --NULL/是否第一次价款划出审核通过
            ,NULL                                       AS DEAL_REC_STAT_CD                 --NULL/成交记录状态代码
            ,T2.MDL_RCRD_ID                             AS DEAL_REC_SERIAL_NO               --NULL/成交记录流水号
            ,NULL                                       AS DEAL_OVRL_RMRK                   --NULL/成交总体备注
            ,NULL                                       AS RMRK                             --备注/备注
            ,T1.DT                                      AS DT
  FROM STD.STD_BJHL_TDZSW_XXPL_D    T1       --大宗实物信息披露
  LEFT JOIN (SELECT *
               FROM STD.STD_BJHL_TDZSW_CJJL_D
			  WHERE DT = ${dmp_day}  )   T2       --大宗实物成交记录信息
    ON T1.ID=T2.PRJ
  LEFT JOIN (SELECT *
               FROM STD.STD_BJHL_TDZSW_YXSRFXX_D	--意向受让方信息
			  WHERE DT = ${dmp_day}  )   TT       
    ON T2.BUYER=TT.ID
  LEFT JOIN (SELECT ID  --代码 
                    ,USR_NM AS NAME  --码值
               FROM STD.STD_BJHL_TUSER_D  --用户管理
  			WHERE DT = ${dmp_day} ) T13	   		  		   
    ON T2.CREATE_USER=T13.ID
  LEFT JOIN (SELECT ID  --代码 
                    ,USR_NM AS NAME  --码值
               FROM STD.STD_BJHL_TUSER_D  --用户管理
  			WHERE DT = ${dmp_day} ) T14	   		  		   
    ON T2.UDT_PSN=T14.ID
  --------------------------------------------------------------
 WHERE T1.DT = ${dmp_day}