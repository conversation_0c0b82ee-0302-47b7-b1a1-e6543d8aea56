SELECT T1.ID            AS aprv_pcs_id                               --审批流程ID
       ,T1.PCS_ID     AS pcs_id                                    --流程ID
       ,T1.PCS_NODE_ID      AS pcs_node_id                               --流程节点ID
       ,T1.EXEC_ACTION_ID    AS exec_act_id                               --执行动作ID
       ,CASE WHEN T1.EXEC_ACTION_ID='-10002' THEN '退回'    
             WHEN T1.EXEC_ACTION_ID='-10001' THEN '收回'
             WHEN T1.EXEC_ACTION_ID='-1'	 THEN '通过'
             WHEN T1.EXEC_ACTION_ID='10005638'	 THEN '退回' 
             WHEN T2.ACTIONID IS NULL AND T1.EXEC_ACTION_ID  IN ('6','12','14','19','21') THEN '通过'
             WHEN T2.ACTIONID IS NULL AND T1.EXEC_ACTION_ID  IN ('39','33','31','29','25') THEN '退回'
             ELSE T2.ALIAS END  AS exec_act_nm --执行动作名称
       ,T1.PCS_ARRV_TM   AS pcs_ariv_tm                                         --流程到达时间
       ,T1.CHK_TM  AS audit_tm                                            --审核时间
       ,T1.WAT_IRCA_ID        AS prewer_id                                           --待审核人ID
       ,T3.NAME         AS prewer_nm                                           --待审核人名称
       ,T1.IRCA_ID       AS auditor_id                                          --审核人ID
       ,T3.USERID       AS auditor_code                                        --审核人编码
       ,T3.NAME         AS auditor_nm                                          --审核人名称
       ,T3.ORGID        AS auditor_blng_dept_id                                --审核人所属部门ID
       ,T4.NAME         AS auditor_blng_dept_nm                                --审核人所属部门名称
       ,T4.FID          AS auditor_blng_cetr_id                                --审核人所属中心ID
       ,T5.NAME         AS auditor_blng_cetr_nm                                --审核人所属中心名称
       ,T1.CHK_STS      AS audit_stat                                          --审核状态
       ,T1.CHK_RMRK     AS audit_rmrk                                          --审核备注
       --,NULL            AS                                 --业务流程节点配置ID
FROM STD.STD_BJHL_OS_HISTORYSTEP_D T1 --审批表
LEFT JOIN (SELECT DISTINCT C.EXEC_ACTION_ID AS ACTIONID,C.EXEC_ACTION_NM AS ALIAS
FROM 
(SELECT *
FROM STD.STD_BJHL_LBAGILEWFRUNTIMESTEP_D  --节点表
WHERE DT = ${dmp_day}
) A
LEFT JOIN (
SELECT * 
FROM STD.STD_BJHL_LBAGILEWFSCHEMERTDEF_D   --流程名称表
WHERE DT = ${dmp_day}
) B
ON A.PCS_CFG_ID = B.ID
INNER JOIN (
SELECT * 
FROM STD.STD_BJHL_LBAGILEWFSTEPRUNTIMEACTION_D  --执行动作
WHERE DT = ${dmp_day}
) C
ON A.ID = C.BSN_PCS_NODE_CFG_ID
-- WHERE C.EXEC_ACTION_NM IN ('退回','收回','通过')   --由于交易审核部数据使用单一，所有过滤掉除 '退回'、'收回'、'通过'三种外的其他执行动作名称
) T2
ON  T1.EXEC_ACTION_ID=T2.ACTIONID

LEFT JOIN (SELECT  ID
				  ,USERID
				  ,USR_NM AS NAME
				  ,SUB_ORG AS ORGID
FROM STD.STD_BJHL_TUSER_D  --用户管理
WHERE DT = ${dmp_day}) T3
ON T1.IRCA_ID =T3.ID

LEFT JOIN (SELECT  ID
				  ,ORG_NM AS NAME
				  ,SUPR_NODE AS FID
FROM STD.STD_BJHL_LBORGANIZATION_D  --组织机构
WHERE DT = ${dmp_day}) T4
ON T3.ORGID =T4.ID 

LEFT JOIN (SELECT  ID
				  ,ORG_NM AS NAME
FROM STD.STD_BJHL_LBORGANIZATION_D  --组织机构
WHERE DT = ${dmp_day}) T5
ON T4.FID =T5.ID

WHERE T1.DT = ${dmp_day}
-- 由于交易审核部数据使用单一，所有过滤掉除 '退回'、'收回'、'通过'三种外的其他执行动作名称
AND (T2.ALIAS IN ('退回','通过') OR T1.EXEC_ACTION_ID IN ('-10002','-10001','-1','10005638')
OR (T2.ACTIONID IS NULL AND T1.EXEC_ACTION_ID  IN ('6','12','14','19','21','39','33','31','29','25'))
)