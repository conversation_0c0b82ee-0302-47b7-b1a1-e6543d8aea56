-- Active: 1725412082987@@10.254.99.145@10000@dws
SELECT 
	etl_date, -- ETL 载入日期 
    COALESCE(aprv_pcs_id,'') AS aprv_pcs_id,                   -- 审批流程ID
    COALESCE(pcs_nm,'') AS pcs_nm,                             -- 流程名称
    COALESCE(pcs_stat_cd,'') AS pcs_stat_cd,                   -- 流程状态
    COALESCE(pmer_nm,'') AS pmer_nm,                           -- 发起人名称
    COALESCE(pmer_blng_dept_nm,'') AS pmer_blng_dept_nm,       -- 发起人所属部门名称
    COALESCE(pmer_blng_cetr_nm,'') AS pmer_blng_cetr_nm,       -- 发起人所属中心名称
    COALESCE(itt_tm,'') AS itt_tm,                             -- 发起时间
    COALESCE(audit_pass_tm,'') AS audit_pass_tm,               -- 审核通过时间
    COALESCE(pcs_aprv_tot_durt,'') AS pcs_aprv_tot_durt,       -- 流程审批总时长
    COALESCE(pcs_node_nm,'') AS pcs_node_nm,                   -- 流程节点名称
    COALESCE(node_ster,'') AS node_ster,                       -- 节点提交人名称
    COALESCE(pcs_ariv_tm,'') AS pcs_ariv_tm,                   -- 流程到达时间
    COALESCE(auditor_nm,'') AS auditor_nm,                     -- 审核人名称
    COALESCE(auditor_blng_dept_nm,'') AS auditor_blng_dept_nm, -- 审核人所属部门名称
    COALESCE(auditor_blng_cetr_nm,'') AS auditor_blng_cetr_nm, -- 审核人所属中心名称
    COALESCE(audit_tm,'') AS audit_tm,                         -- 审核时间
    COALESCE(exec_act,'') AS exec_act,                         -- 执行动作
    COALESCE(node_audit_durt,'') AS node_audit_durt,           -- 节点审核时长
    COALESCE(prj_stg,'') AS prj_stg,                           -- 项目阶段
    COALESCE(stm_tp,'') AS stm_tp,                             -- 系统类型
    COALESCE(audit_rmrk,'') AS audit_rmrk,                     -- 审核备注
    COALESCE(prj_id,'') AS prj_id,                             -- 项目编号
    COALESCE(prj_nm,'') AS prj_nm,                             -- 项目名称
    COALESCE(prj_bsn_tp,'') AS prj_bsn_tp,                     -- 项目业务类型
    COALESCE(prj_blng_cetr,'') AS prj_blng_cetr,               -- 项目所属中心
    COALESCE(prj_blng_dept,'') AS prj_blng_dept,               -- 项目所属部门
    COALESCE(prj_prin,'') AS prj_prin,                         -- 项目负责人
    COALESCE(prj_prin_dept,'') AS prj_prin_dept,               -- 项目负责人部门
    COALESCE(prj_tp,'') AS prj_tp,                             -- 项目类型
    COALESCE(publ_tp,'') AS publ_tp,                           -- 披露类型
    COALESCE(sort_no,'') AS sort_no                            -- 排序号
FROM 
(
SELECT ROW_NUMBER()OVER(PARTITION BY no_zd ORDER BY pcs_ariv_tm DESC) rn,*
FROM (
SELECT 
	regexp_replace(replace('${dmp_day}','-',''), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3')  AS etl_date, -- ETL 载入日期 
    aprv_pcs_id AS aprv_pcs_id,                   -- 审批流程ID
    pcs_nm AS pcs_nm,                             -- 流程名称
    pcs_stat_cd AS pcs_stat_cd,                   -- 流程状态
    pmer_nm AS pmer_nm,                           -- 发起人名称
    pmer_blng_dept_nm AS pmer_blng_dept_nm,       -- 发起人所属部门名称
    pmer_blng_cetr_nm AS pmer_blng_cetr_nm,       -- 发起人所属中心名称
    itt_tm AS itt_tm,                             -- 发起时间
    audit_pass_tm AS audit_pass_tm,               -- 审核通过时间
    pcs_aprv_tot_durt AS pcs_aprv_tot_durt,       -- 流程审批总时长
    pcs_node_nm AS pcs_node_nm,                   -- 流程节点名称
    node_ster AS node_ster,                       -- 节点提交人名称
    pcs_ariv_tm AS pcs_ariv_tm,                   -- 流程到达时间
    auditor_nm AS auditor_nm,                     -- 审核人名称
    auditor_blng_dept_nm AS auditor_blng_dept_nm, -- 审核人所属部门名称
    auditor_blng_cetr_nm AS auditor_blng_cetr_nm, -- 审核人所属中心名称
    substr(audit_tm,1,10) AS audit_tm,            -- 审核时间
    exec_act AS exec_act,                         -- 执行动作
    node_audit_durt AS node_audit_durt,           -- 节点审核时长
    prj_stg AS prj_stg,                           -- 项目阶段
    stm_tp AS stm_tp,                             -- 系统类型
    audit_rmrk AS audit_rmrk,                     -- 审核备注
    prj_id AS prj_id,                             -- 项目编号
    prj_nm AS prj_nm,                             -- 项目名称
    prj_bsn_tp AS prj_bsn_tp,                     -- 项目业务类型
    prj_blng_cetr AS prj_blng_cetr,               -- 项目所属中心
    prj_blng_dept AS prj_blng_dept,               -- 项目所属部门
    prj_prin AS prj_prin,                         -- 项目负责人
    prj_prin_dept AS prj_prin_dept,               -- 项目负责人部门
    prj_tp AS prj_tp,                             -- 项目类型
    publ_tp AS publ_tp,                           -- 披露类型
    sort_no AS sort_no,                            -- 排序号
    pcs_title,                                      --流程标题
    COALESCE(pcs_nm,'') || COALESCE(auditor_nm,'') || COALESCE(prj_id,'') || COALESCE(prj_nm,'') || COALESCE(exec_act,'') || COALESCE(pcs_ariv_tm,'') || COALESCE(node_ster,'') || COALESCE(pcs_title,'')  as no_zd
FROM 
    dws.dws_p1_bsn_pcs_aprv_info
    WHERE pcs_node_nm NOT IN ('开始','结束')
    AND dt = '${dmp_day}') K
) T
WHERE T.RN =1