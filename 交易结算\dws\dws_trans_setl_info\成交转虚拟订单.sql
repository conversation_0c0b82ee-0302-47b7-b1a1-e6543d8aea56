SELECT 
    a.deal_rec_id,                                    --成交ID
    CONCAT('xn_', b.prj_id, '_1') AS ORDR_NO,         -- 订单号
    b.prj_id AS ORDR_PRJ_NO,                          -- 项目编号
    b.prj_nm AS ORDR_PRJ_NAME,                        -- 项目名称
    b.oprtr_nm AS PRJ_OPTR,                           -- 经办人名称
    b.handl_dept_nm AS PRJ_OPTR_DEPT,                 -- 经办部门名称
    b.handl_bus_dep_nm AS PRJ_OPTR_ORG,               -- 业务中心
    NULL AS CUST_NO,                                  -- 客户号
    NULL AS CUST_NAME,                                -- 客户名称
    '保证金转价款' AS AST_TYPE,                       -- 资产类型    
    CASE WHEN b.prj_bsn_tp_cd_dsc LIKE '诉讼资产-%' THEN '诉讼资产'
         WHEN b.prj_bsn_tp_cd_dsc = '资产转让' THEN '大宗实物'
              ELSE b.prj_bsn_tp_cd_dsc
    END AS BSN_TYPE,                                   -- 业务类型
    '出金' AS CRJ_FLAG,                                -- 出入金标识
    '保证金' AS CPTL_TYPE_LRGCLS,                      -- 资金类型大类
    '保证金转价款（保证金出金）' AS CPTL_TYPE_SMLCLS,       -- 资金类型小类
    a.mrgn_tf_prc_amt AS AMT,                         -- 发生金额
    a.curr_cd_dsc AS CCY,                             -- 币种
    NULL AS PAY_MODE,                                 -- 支付方式
    replace(substr(a.Mrgn_Tf_Prc_Tm,1,10),'-','') AS PAY_SUCCESS_DATE,                         -- 支付成功时间
    NULL as col_pay_agent_name, -- 代付方
    NULL AS SETL_BANK,                                -- 结算银行
    NULL AS CPTL_LO,                                  -- 资金位置
    replace(substr(a.Mrgn_Tf_Prc_Tm,1,10),'-','') AS BANK_TO_ACC_DATE,                         -- 银行到账时间
    '场内结算' AS SETL_TYPE,                           -- 结算方式
    '是' AS IS_VIRTUAL_ORDER,                          -- 是否虚拟订单
    NULL AS order_creat_dt 
FROM 
    DWD.DWD_EVT_DEAL_REC_FCT A
LEFT JOIN 
    DWD.DWD_PRJ_FCT B
ON 
    A.PRJ_WRD = B.PRJ_WRD 
    AND A.DT = B.DT 
WHERE 
    A.IS_MRGN_OF_TXN_PRC = 1 
    AND A.DT = '${dmp_day}'
    AND B.PRJ_ID IS NOT NULL
    AND A.mrgn_tf_prc_amt != 0 -- 当交易金额为0时不虚拟订单
    AND B.prj_bsn_tp_cd IN ('1D','GQ')
UNION ALL 
SELECT 
    a.deal_rec_id,                                    --成交ID
    CONCAT('xn_', b.prj_id, '_2') AS ORDR_NO,         -- 订单号
    b.prj_id AS ORDR_PRJ_NO,                          -- 项目编号
    b.prj_nm AS ORDR_PRJ_NAME,                        -- 项目名称
    b.oprtr_nm AS PRJ_OPTR,                           -- 经办人名称
    b.handl_dept_nm AS PRJ_OPTR_DEPT,                 -- 经办部门名称
    b.handl_bus_dep_nm AS PRJ_OPTR_ORG,               -- 业务中心
    NULL AS CUST_NO,                                  -- 客户号
    NULL AS CUST_NAME,                                -- 客户名称
    '保证金转价款' AS AST_TYPE,                  -- 资产类型    
    CASE WHEN b.prj_bsn_tp_cd_dsc LIKE '诉讼资产-%' THEN '诉讼资产'
         WHEN b.prj_bsn_tp_cd_dsc = '资产转让' THEN '大宗实物'
              ELSE b.prj_bsn_tp_cd_dsc
    END AS BSN_TYPE,                                   -- 业务类型
    '入金' AS CRJ_FLAG,                                -- 出入金标识
    '价款' AS CPTL_TYPE_LRGCLS,                        -- 资金类型大类
    '保证金转价款（价款入金）' AS CPTL_TYPE_SMLCLS,         -- 资金类型小类
    a.mrgn_tf_prc_amt AS AMT,                         -- 发生金额
    a.curr_cd_dsc AS CCY,                             -- 币种
    NULL AS PAY_MODE,                                 -- 支付方式
    replace(substr(a.Mrgn_Tf_Prc_Tm,1,10),'-','') AS PAY_SUCCESS_DATE,                         -- 支付成功时间
    NULL as  col_pay_agent_name, -- 代付方
    NULL AS SETL_BANK,                                -- 结算银行
    NULL AS CPTL_LO,                                  -- 资金位置
    replace(substr(a.Mrgn_Tf_Prc_Tm,1,10),'-','') AS BANK_TO_ACC_DATE,                         -- 银行到账时间
    '场内结算' AS SETL_TYPE,                            -- 结算方式
    '是' AS IS_VIRTUAL_ORDER,                          -- 是否虚拟订单
    NULL AS order_creat_dt 
FROM 
    dwd.dwd_evt_deal_rec_fct a
LEFT JOIN 
    dwd.dwd_prj_fct b
ON 
    a.prj_wrd = b.prj_wrd 
    and a.dt = b.dt 
where 
    a.is_mrgn_of_txn_prc = 1 
    and a.DT = '${dmp_day}'
    and b.prj_id is not null
    AND a.mrgn_tf_prc_amt != 0 -- 当交易金额为0时不虚拟订单
    AND B.prj_bsn_tp_cd IN ('1D','GQ')