WITH ranked_data AS (
    SELECT
        SearchCompanyName,  -- 查询公司名
        BusinessDateFrom,   -- 营业起始日期
        Authority,          -- 登记机关
        CompanyStatus,      -- 公司状态
        BusinessScope,      -- 经营范围
        IssueDate,          -- 发照日期
        BusinessDateTo,     -- 营业终止日期
        Capital,            -- 注册资本
        CompanyType,        -- 公司类型
        LegalPerson,        -- 法人代表
        EstablishDate,      -- 成立日期
        Province,           -- 省份代码
        KeyNo,              -- 唯一标识码
        CompanyAddress,     -- 公司地址
        CompanyName,        -- 公司名称
        OrgCode,            -- 组织机构代码
        IsOnStock,          -- 是否上市（0: 否, 1: 是）
        CreditNo,           -- 统一社会信用代码
        CompanyCode,        -- 公司代码
        UpperCompanies,     -- 上级公司
        DataSource,         -- 数据来源接口
        ETLDate,            -- ETL处理日期
        ROW_NUMBER() OVER (PARTITION BY SearchCompanyName ORDER BY ETLDate DESC) AS row_num  -- 按ETLDate降序排序
    FROM ods.ods_api_full_company_info
    WHERE dt = '${dmp_day}'
)
SELECT
    SearchCompanyName,  -- 查询公司名
    BusinessDateFrom,   -- 营业起始日期
    Authority,          -- 登记机关
    CompanyStatus,      -- 公司状态
    BusinessScope,      -- 经营范围
    IssueDate,          -- 发照日期
    BusinessDateTo,     -- 营业终止日期
    Capital,            -- 注册资本
    CompanyType,        -- 公司类型
    LegalPerson,        -- 法人代表
    EstablishDate,      -- 成立日期
    Province,           -- 省份代码
    KeyNo,              -- 唯一标识码
    CompanyAddress,     -- 公司地址
    CompanyName,        -- 公司名称
    OrgCode,            -- 组织机构代码
    IsOnStock,          -- 是否上市（0: 否, 1: 是）
    CreditNo,           -- 统一社会信用代码
    CompanyCode,        -- 公司代码
    UpperCompanies,     -- 上级公司
    DataSource,         -- 数据来源接口
    ETLDate             -- ETL处理日期
FROM ranked_data
WHERE row_num = 1