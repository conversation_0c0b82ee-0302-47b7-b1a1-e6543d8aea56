with temp1 as(
SELECT
DISTINCT
x.exch, -- 挂牌交易所
x.prj_no as proj_no, -- 项目编号
x.prj_nm as proj_name, --项目名称
  substr(x.info_publ_start_dt,1,7) as data_dt,
x.info_publ_start_dt as info_dclo_begin_dt, --信息披露起始日期
x.Info_Publ_Exprt_Dt as info_dclo_end_dt, --信息披露期满日期
COALESCE(x.Lit_Amt,0)/10000 as Lit_Amt, --挂牌金额（万元）
x.prj_blng_dept_nm as proj_belong_dept_name, --所属部门
x.Blng_Org as belong_group,--所属集团
x.prj_prin_nm as proj_princ_name, --项目负责人
x.entp_tp
FROM dws.dws_all_trsfer_info x
where x.prj_bsn_tp_cd_dsc='企业增资' 
      and prj_blng_dept_nm in('央企一部','央企二部','央企三部','央企四部','央企五部','央企六部')
      and blng_org not in (select cty_contri_corp_lead_dept from ods.ods_bl_yqzx_fin_proj_list  where dt = '${dmp_day}')
      and dt = '${dmp_day}'
)
select 
data_dt,entp_tp as entp_type,belong_group,exch,proj_belong_dept_name,count(1) as lit_prj_num1
from temp1 
group by data_dt,entp_tp,belong_group,exch,proj_belong_dept_name