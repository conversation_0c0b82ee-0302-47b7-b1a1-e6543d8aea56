-- Active: 1725412082987@@10.254.99.145@10000@dws
SELECT 
		T.USR_CGY_CD_DSC  AS  user_type                        --用户类型代码描述
		,T.USR_NM   AS  user_name                              --用户全称
		,CASE WHEN T1.CUST_TP_CD= 0 THEN T1.CUST_FULL_NM ELSE T2.OPRTR_NM END AS contact_person_name --联系人名称
		,T.PHON  AS contact_person_phone                       --手机号码
		,T1.BLNG_PROV_CD_DSC   AS    province                  --省代码描述
		,T1.LCT_CITY_CD_DSC  AS   city                         --市代码描述
		,T1.LCT_REGN_CD_DSC  AS district                       --区县代码描述
		,T1.POST_ADDR  AS address                              --详细地址
		,T3.CREATE_TIME  AS reg_date                           --注册时间
FROM 
DIM.DIM_PTY_USR T
LEFT JOIN 
(
	SELECT CUST_WRD                           
		 ,BLNG_PROV_CD                       
		 ,BLNG_PROV_CD_DSC
		 ,LCT_CITY_CD
		 ,LCT_CITY_CD_DSC
		 ,LCT_REGN_CD
		 ,LCT_REGN_CD_DSC
		 ,POST_ADDR
		 ,CUST_TP_CD
		 ,CUST_FULL_NM
	FROM DIM.DIM_PTY_CUST  --客户维
	WHERE DT = '${dmp_day}'
) T1
ON T.CUST_WRD = T1.CUST_WRD
LEFT JOIN 
(SELECT 'BJHL'||CUST_NO AS CUST_WRD
				 ,OPTR_NM AS OPRTR_NM	 
			FROM STD.STD_BJHL_TJGKHXX_D
			WHERE DT = '${dmp_day}'
			) T2
ON T1.CUST_WRD = T2.CUST_WRD
LEFT JOIN 
(
	SELECT 'BJHL'||usr_id||'QD' AS USR_WRD ,CREATE_TIME FROM STD.STD_BJHL_T_GIFAX_USER_D 
	WHERE DT = '${dmp_day}'
) T3
ON T.USR_WRD = T3.USR_WRD
WHERE T.DT = '${dmp_day}'