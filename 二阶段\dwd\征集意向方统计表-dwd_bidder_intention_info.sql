-- Active: 1725412082987@@10.254.99.145@10000@std
--产权转让
SELECT 
buyer_nm AS cust_name,	--	客户名称
ctc_psn AS contact_person_name,	--	联系人
contact_tel AS phone,	--	联系电话
email AS email,	--	电子邮件
CASE WHEN b.project_code IS NOT NULL THEN '预披露' 
WHEN c.project_code IS NOT NULL THEN '正式披露' 
END AS disclosure_type,	--	披露类型
'产权转让' AS prj_type,	--	项目类型
CASE WHEN b.project_code IS NULL THEN c.project_code ELSE b.project_code END AS prj_no,	--	项目编号
CASE WHEN b.project_name IS NULL THEN c.project_name ELSE b.project_name END AS prj_name,	--	项目名称
CASE WHEN b.pre_esr_beg_dt IS NULL THEN c.inf_esr_beg_dt ELSE b.pre_esr_beg_dt END AS Disclosure_start_time,  --信息披露起始日期
CASE WHEN b.pre_esr_exp_dt IS NULL THEN c.inf_esr_exp_dt ELSE b.pre_esr_exp_dt END AS Disclosure_end_time,  --信息披露结束日期
CASE WHEN usr1.usr_nm IS NULL THEN usr2.usr_nm ELSE usr1.usr_nm END AS prj_responsible_person,	--	项目负责人
CASE WHEN bm1.org_nm IS NULL THEN bm2.org_nm ELSE bm1.org_nm END AS prj_dep,	--	项目所属部门
buy_intnt_cmnt AS intention_desc,	--	意向描述
NULL AS increase_in_registered_capital,	--	拟新增注册资本（万元）
NULL AS investment_amt,	--	拟投资金额（万元）
NULL AS investment_pct	--	拟投资比例（%）
 FROM std.std_bjhl_tcqzr_cbyxsrfxx_d a
 LEFT JOIN 
 (
 	SELECT 
	id,
	project_name, --项目名称
	project_code, --项目编号
	pre_esr_beg_dt, --信息披露起始日期
	pre_esr_exp_dt, --信息披露结束日期
	prj_pnp, --项目负责人 【转码】
	prj_blng_dept --项目所属部门 【转码】
	FROM std.std_bjhl_tcqzr_cqzrxxypl_d 
	WHERE dt= '${dmp_day}'
 ) b  --预披露
 ON a.project_code = b.project_code
 LEFT JOIN 
 (
 select id,
	project_name, --项目名称
	project_code, --项目编号
	inf_esr_beg_dt, --信息披露起始日期
	inf_esr_exp_dt, --信息披露结束日期
	prj_pnp, --项目负责人 【转码】
	prj_blng_dept --项目所属部门 【转码】
	FROM std.std_bjhl_tcqzr_cqzrxxpl_d
 WHERE dt= '${dmp_day}') c  --正式披漏
 ON a.project_code = c.project_code
 LEFT JOIN (
	select org_id,org_nm from dim.dim_org_info WHERE  dt='${dmp_day}' AND org_tp_cd_dsc = '部门' and edw_end_dt = '20991231'
)  bm1 
ON bm1.org_id = b.prj_blng_dept
 LEFT JOIN (
	select org_id,org_nm from dim.dim_org_info WHERE  dt='${dmp_day}' AND org_tp_cd_dsc = '部门' and edw_end_dt = '20991231'
)  bm2 
ON bm2.org_id = c.prj_blng_dept
LEFT JOIN 
(SELECT DISTINCT usr_id,usr_nm FROM dim.dim_user_info WHERE  dt='${dmp_day}' AND usr_cgy_cd = 0) usr1
ON usr1.usr_id = b.prj_pnp
LEFT JOIN 
(SELECT DISTINCT usr_id,usr_nm FROM dim.dim_user_info WHERE  dt='${dmp_day}' AND usr_cgy_cd = 0) usr2
ON usr2.usr_id = c.prj_pnp
WHERE a.dt = '${dmp_day}'

UNION ALL 

--企业增资（正式披漏）
SELECT 
nm AS cust_name,	--	客户名称
ctc_psn AS contact_person_name,	--	联系人
contact_tel AS phone,	--	联系电话
email AS email,	--	电子邮件
'正式披露' AS disclosure_type,	--	披露类型
'企业增资' AS prj_type,	--	项目类型
b.project_code AS prj_no,	--	项目编号
b.project_name AS prj_name,	--	项目名称
b.listg_beg_dt AS disclosure_start_time,  --挂牌开始日期
b.expire_date AS disclosure_end_time,  --挂牌结束日期
usr.usr_nm AS Prj_responsible_person,	  --项目负责人
bm.org_nm AS Prj_dep,	  --项目所属部门
ivs_apply_remark AS intention_desc,	--	意向描述
prep_new_reg_capital_w_yuan AS increase_in_registered_capital,	--	拟新增注册资本（万元）
prep_invest_total_w_yuan AS investment_amt,	--	拟投资金额（万元）
new_hold_shr_pct AS investment_pct	--	拟投资比例（%）
FROM std.std_bjhl_tcgq_qdbmtzfxx_d a 
LEFT JOIN 
(
SELECT 
id,
prj_pnp, --项目负责人
prj_blng_dept, --项目所属部门
project_name,
project_code,
listg_beg_dt, --挂牌开始日期
expire_date  --挂牌结束日期
FROM std.std_bjhl_tcgq_zzzsgpxm_d
WHERE  dt='${dmp_day}'
) b
ON a.project_id = b.id
LEFT JOIN (
	select org_id,org_nm,supr_org_id from dim.dim_org_info WHERE  dt='${dmp_day}' AND org_tp_cd_dsc = '部门' and edw_end_dt = '20991231'
)  bm 
ON bm.org_id = b.prj_blng_dept
LEFT JOIN 
(SELECT DISTINCT usr_id,usr_nm FROM dim.dim_user_info WHERE  dt='${dmp_day}' AND usr_cgy_cd = 0) usr
ON usr.usr_id = b.prj_pnp
WHERE a.dt = '${dmp_day}'
UNION ALL 
--信息披漏【在dws层实现】
-- 房屋出租
select 
buyer_nm AS cust_name,	--	客户名称
ctc_psn AS contact_person_name,	--	联系人
contact_tel AS phone,	--	联系电话
email AS email,	--	电子邮件
'预披露' AS disclosure_type, --披漏类型
'房屋出租' AS prj_type, --项目类型
b.project_code AS prj_no,	--	项目编号
b.project_name AS prj_name,	--	项目名称
b.pre_esr_strt_dt AS disclosure_start_time,	--	披露起始日期
b.pre_esr_end_dt AS disclosure_end_time,	--	披露结束日期
usr.usr_nm AS Prj_responsible_person,	  --项目负责人
bm.org_nm AS Prj_dep,	  --项目所属部门
rent_apply_remark AS intention_desc,	--	意向描述
NULL AS increase_in_registered_capital,	--	拟新增注册资本（万元）
NULL AS investment_amt,	--	拟投资金额（万元）
NULL AS investment_pct	--	拟投资比例（%）
from std.std_bjhl_tzccz_yxczfxx_cb_d a -- 房屋出租 
LEFT JOIN 
(
	SELECT 
	id,
	prj_pnp, --项目负责人
	prj_blng_dept, --项目所属部门
	project_name,
	project_code,
	pre_esr_strt_dt, --披漏开始日期
	pre_esr_end_dt  --披漏结束日期
	FROM std.std_bjhl_tzccz_zcczxm_ypl_d 
	where dt = '${dmp_day}'
) b
ON a.project_id = b.id
LEFT JOIN (
	select org_id,org_nm,supr_org_id from dim.dim_org_info WHERE  dt='${dmp_day}' AND org_tp_cd_dsc = '部门' and edw_end_dt = '20991231'
)  bm 
ON bm.org_id = b.prj_blng_dept
LEFT JOIN 
(SELECT DISTINCT usr_id,usr_nm FROM dim.dim_user_info WHERE  dt='${dmp_day}' AND usr_cgy_cd = 0) usr
ON usr.usr_id = b.prj_pnp
WHERE a.dt = '${dmp_day}'