-- Active: 1725412082987@@10.254.99.145@10000@dws
SELECT  CASE WHEN prj_bsn_tp_cd_dsc = '资产转让' THEN '大宗实物'  ELSE prj_bsn_tp_cd_dsc END        AS business_type -- 业务类型 
       ,bm2.org_nm                                                                               AS proj_belong_center --所属中心 
       ,deal_amt/10000                                                                           AS Deal_amt -- 成交金额 
       ,CASE WHEN eval_prc = '' OR eval_prc IS NULL THEN 0  ELSE (deal_amt - eval_prc)/10000 END AS total_price_incre --增值金额 
       ,prj_nm                                                                                   AS proj_name -- 项目名称 
       ,prj_no                                                                                   AS proj_no -- 项目编码 
       ,txn_mth_cd_dsc                                                                           AS trans_type --交易方式 
       ,BSN_REC_DEAL_DT 																		 AS deal_date --成交日期
--eval_prc                                         AS appraised_amt, --评估值
--tfr_prc, -- 转让底价
--lit_amt, -- 挂牌金额
--Prj_Blng_Dept_Id,
--prj_blng_dept_nm, 
       ,CASE WHEN prj_bsn_tp_cd = 'GQ' THEN prj_prt_sown_spvs_org_cd_dsc  ELSE zcly END          AS asset_origin -- 国资监管机构/资产来源 
FROM dws.dws_all_trsfer_info a
LEFT JOIN
(
	SELECT  org_id
	       ,org_nm
	       ,supr_org_id
	FROM dim.dim_org_info
	WHERE dt = '${dmp_day}'
	AND org_tp_cd_dsc = '部门'
	AND edw_end_dt = '20991231' 
) bm
ON bm.org_id = a.Prj_Blng_Dept_Id
LEFT JOIN
(
	SELECT  org_id
	       ,org_nm
	       ,supr_org_id
	FROM dim.dim_org_info
	WHERE dt = '${dmp_day}'
	AND org_tp_cd_dsc = '部门'
	AND edw_end_dt = '20991231' 
) bm2
ON bm.supr_org_id = bm2.org_id
WHERE ( CONCAT(prj_bsn_tp_cd, prj_tp_cd) = 'GQ1' --企业产权只取正式披露的 
 OR (CONCAT(prj_bsn_tp_cd, is_owned_state) = '1D1' AND zcly IN ('企业实物资产', '行政事业单位实物资产')) OR (CONCAT(prj_bsn_tp_cd, txn_mth_cd_dsc) = '1B动态报价' AND (zcly LIKE '行政事业%' OR zcly LIKE '企业资产%')) )
AND bsn_rec_deal_dt IS NOT NULL -- 成交日期不为空 
AND a.exch = '北交所'
AND dt = '${dmp_day}'