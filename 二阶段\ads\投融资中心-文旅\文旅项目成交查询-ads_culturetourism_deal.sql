-- Active: 1725412082987@@10.254.99.145@10000@dws
SELECT  
a.prj_no as proj_no, --项目编码
a.prj_nm as proj_name, --项目名称
a.prj_stat_cd_dsc as proj_status, --项目状态
COALESCE(d.plan_new_cptl_mth_cd_dsc,'固定值') AS sell_price_compute_mth, --转让底价计算方式
CASE WHEN a.prj_bsn_tp_cd = '1C' THEN COALESCE(d.plan_raise_cptl_amt,0)/10000 ELSE COALESCE(a.lit_amt,0)/10000 END AS sell_price, --转让底价
COALESCE(d.plan_raise_cptl_amt_min,0)/10000 AS sell_price_min, --转让底价最小值
COALESCE(d.plan_raise_cptl_amt_max,0)/10000 AS sell_price_max, --转让底价最大值
a.info_publ_start_dt as info_dclo_begin_dt, --信息披露起始日期
a.info_publ_exprt_dt as info_dclo_expire_dt, --信息披露截止日期
CASE WHEN prj_bsn_tp_cd = '1D' THEN '大宗实物' ELSE prj_bsn_tp_cd_dsc END AS source, --来源系统
c.prj_tp_cd_dsc as disclosure_type, --披漏类型
c.industry_tp_dsc as industry_type, --所属行业类型
c.obj_prov_dsc as proj_location, --项目所属地
a.deal_amt/10000 as deal_amt, --成交金额
a.bsn_rec_deal_dt as deal_date, --成交日期
CONCAT_WS(',', COLLECT_SET(b.trsfee_nm)) AS buyer_name -- 受让方/投资方名称
FROM dws.dws_all_trsfer_info a
LEFT JOIN 
(
	SELECT bsn_prj_wrd,trsfee_nm FROM dwd.dwd_evt_deal_rec_fct 
	WHERE trsfee_nm IS NOT NULL AND dt = '${dmp_day}'
	AND etrs_cgy_cd = '1'
) b 
ON a.bsn_prj_wrd = b.bsn_prj_wrd
LEFT JOIN 
(
	SELECT prj_id,industry_tp_dsc,obj_prov_dsc,prj_tp_cd,prj_tp_cd_dsc FROM dwd.dwd_prj_fct WHERE dt = '${dmp_day}' 
	AND prj_id IS NOT NULL
) c
ON a.prj_no = c.prj_id
LEFT JOIN 
(
	SELECT 
	bsn_prj_wrd,
  plan_new_cptl_mth_cd,
  plan_new_cptl_mth_cd_dsc,
	plan_raise_cptl_amt,
  plan_raise_cptl_amt_min,
  plan_raise_cptl_amt_max 
	FROM dim.dim_incptl_info 
	WHERE dt = '${dmp_day}' AND edw_end_dt = '20991231'
) d
ON d.bsn_prj_wrd = a.bsn_prj_wrd
WHERE a.prj_bsn_tp_cd IN ('1D','1C','GQ')
AND (
 prj_nm RLIKE '文化|文物|文体|旅游|旅行|体育|运动|休闲|景区|酒店|宾馆|民宿|度假|康养|养老|乡村|传媒|影视|影业|影院|影城|电影|影音|公园|作品|活动|动漫'
 OR industry_tp_dsc IN (
 	'文化、体育和娱乐业','住宿和餐饮业','住宿业','餐饮业',
 	'广播、电视、电影和影视录音制作业','新闻和出版业','广播、电视、电影和录音制作业',
 	'文化艺术业','体育','娱乐业'
 )
)
AND a.bsn_rec_deal_dt IS NOT NULL and a.bsn_rec_deal_dt != '' -- 只取有成交日期的
AND a.prj_stat_cd_dsc not in ('待认领','待业务经办提交','已退回','已提交','待提交')
AND a.exch = '北交所'
AND dt = '${dmp_day}'
GROUP BY 
  a.prj_no, 
  a.prj_nm, 
  a.prj_stat_cd_dsc, 
  a.lit_amt, 
  a.info_publ_start_dt, 
  a.info_publ_exprt_dt, 
  a.prj_bsn_tp_cd_dsc, 
  a.prj_bsn_tp_cd, 
  c.prj_tp_cd_dsc, 
  c.industry_tp_dsc, 
  c.obj_prov_dsc, 
  a.deal_amt, 
  a.bsn_rec_deal_dt,
  COALESCE(d.plan_new_cptl_mth_cd_dsc,'固定值'),
  CASE WHEN a.prj_bsn_tp_cd = '1C' THEN COALESCE(d.plan_raise_cptl_amt,0)/10000 ELSE COALESCE(a.lit_amt,0)/10000 END,
  COALESCE(d.plan_raise_cptl_amt_min,0),
  COALESCE(d.plan_raise_cptl_amt_max,0)
UNION ALL
select 
a.project_code as proj_no,    --项目编号
a.project_name as proj_name,    --项目名称
a.prj_sts_dsc as proj_status, --项目状态
'固定值' as sell_price_compute_mth, --转让底价计算方式
NULL as sell_price, --转让低价，信息披露的转让底价都留空
NULL as sell_price_min, --转让低价最小值，信息披露的转让底价都留空
NULL as sell_price_max, --转让低价最大值，信息披露的转让底价都留空
regexp_replace(cast(a.esr_beg_dt as string), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') as info_dclo_begin_dt, --信息披露起始日期
regexp_replace(cast(a.esr_exp_dt as string), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') as info_dclo_expire_dt, --信息披露截止日期
'统一信息披露' as source,    --来源系统
case when a.esr_tp = 1 then '预披露'
 when a.esr_tp = 2 then '正式披露'
 when a.esr_tp = 3 then '综合招商'
 when a.esr_tp = 4 then '项目推介'
end as disclosure_type,  --披露类型
a.industry_tp_dsc as industry_type, --所属行业类型
b.admn_rgon_nm as proj_location,    --项目所属地
c.deal_price_w_yuan as deal_amt, --成交金额
regexp_replace(cast(c.cntr_eff_dt as string), '^(\\d{4})(\\d{2})(\\d{2})$', '$1-$2-$3') as deal_date, --成交日期
d.cust_full_nm as buyer_name -- 受让方/投资方名称
from dwd.dwd_information_disclosure_info a 
LEFT JOIN (
    SELECT region_code,  -- 代码 
   admn_rgon_nm  -- 码值
    FROM std.std_bjhl_txzqydm_d  -- 行政区域代码表
    WHERE dt='${dmp_day}'
) b 
ON a.wbt_prov = b.region_code
left join (
    select 
project_code,       --项目编号
deal_price_w_yuan,  --成交价格
cntr_eff_dt,        --成交日期
buyer_repre         --客户编码
from dwd.dwd_bjhl_txxpl_cjjl 
where dt = '${dmp_day}'
) c
on a.project_code = c.project_code
left join (
    select 
        DISTINCT
        bsn_stm_cust_no,
        cust_full_nm 
    from dim.dim_pty_cust 
    where dt = '${dmp_day}'
) d 
on c.buyer_repre = d.bsn_stm_cust_no
where c.cntr_eff_dt is not NULL
and (
 project_name RLIKE '文化|文物|文体|旅游|旅行|体育|运动|休闲|景区|酒店|宾馆|民宿|度假|康养|养老|乡村|传媒|影视|影业|影院|影城|电影|影音|公园|作品|活动|动漫'
 OR industry_tp_dsc IN (
 	'文化、体育和娱乐业','住宿和餐饮业','住宿业','餐饮业',
 	'广播、电视、电影和影视录音制作业','新闻和出版业','广播、电视、电影和录音制作业',
 	'文化艺术业','体育','娱乐业'
 )
)
and prj_sts_dsc not in ('待认领','待业务经办提交','已退回','已提交','待提交')
and dt = '${dmp_day}'