WITH TMP_A AS ( 
-- 20241104 根据北郊互联前端代码改造优化
-- 房屋出租正式披露
SELECT	
	 T1.INSTID AS pcs_id
	,T2.XMID AS plform_prj_id  -- 项目编号
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_flcZCCZ_CJXXLR_D T1
	INNER JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_TZCCZ_ZCCZXM_D
	WHERE DT = '${dmp_day}') T2
	ON T1.XM = T2.ID
WHERE T1.DT = '${dmp_day}'

UNION
-- pcs_cd 关联
SELECT	
	 T1.PCS_EXMP_ID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_tLCZXDZ_LCXXJL_D T1
	LEFT JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_TZCCZ_ZCCZXM_D
	WHERE DT = '${dmp_day}') T2
	ON T1.PROJECT_ID = T2.ID
INNER JOIN STD.STD_BJHL_TXXTJ_JYSHBSJTJPZ_D T3
ON T3.pcs_cd = T1.pcs_cd
AND T3.PRJ_BLNG_TBL = 'PRODUCT.TZCCZ_ZCCZXM'
AND T3.DT= T1.DT
WHERE T1.DT = '${dmp_day}'
AND T1.project_category= '1G'

UNION
-- pcs_cd 关联
SELECT	
	 T1.PCS_EXMP_ID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_tLCZXDZ_LCXXJL_D T1
	LEFT JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_TZCCZ_ZCCZXM_YPL_D
	WHERE DT = '${dmp_day}') T2
	ON T1.PROJECT_ID = T2.ID
INNER JOIN STD.STD_BJHL_TXXTJ_JYSHBSJTJPZ_D T3
ON T3.pcs_cd = T1.pcs_cd
AND T3.PRJ_BLNG_TBL = 'PRODUCT.TZCCZ_ZCCZXM_YPL'
AND T3.DT= T1.DT
WHERE T1.DT = '${dmp_day}'
AND T1.project_category= '1G'

UNION
-- pcs_nm 关联
SELECT	
	 T1.PCS_EXMP_ID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_tLCZXDZ_LCXXJL_D T1
	LEFT JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_TZCCZ_ZCCZXM_YPL_D
	WHERE DT = '${dmp_day}') T2
	ON T1.PROJECT_ID = T2.ID
INNER JOIN STD.STD_BJHL_TXXTJ_JYSHBSJTJPZ_D T3
ON T3.pcs_nm = T1.pcs_nm
AND T3.PRJ_BLNG_TBL = 'PRODUCT.TZCCZ_ZCCZXM_YPL'
AND T3.DT= T1.DT
WHERE T1.DT = '${dmp_day}'
AND T1.project_category= '1G'


--  4 大宗实物 项目审核通过才有项目编号
UNION

SELECT	
	 T1.PCS_EXMP_ID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_tLCZXDZ_LCXXJL_D T1
	LEFT JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_TDZSW_XXPL_D
	WHERE DT = '${dmp_day}') T2
	ON T1.PROJECT_ID = T2.ID
INNER JOIN STD.STD_BJHL_TXXTJ_JYSHBSJTJPZ_D T3
ON T3.pcs_nm = T1.pcs_nm
AND T3.PRJ_BLNG_TBL = 'DZSW.TDZSW_XXPL'
AND T3.DT= T1.DT
WHERE T1.DT = '${dmp_day}'
AND T1.project_category= '1D'

--  5 产权转让预披露 项目审核通过才有项目编号,keyid

UNION

SELECT	
	 T1.PCS_EXMP_ID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_tLCZXDZ_LCXXJL_D T1
	LEFT JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_TCQZR_CQZRXXYPL_D
	WHERE DT = '${dmp_day}') T2
	ON T1.PROJECT_ID = T2.ID
INNER JOIN STD.STD_BJHL_TXXTJ_JYSHBSJTJPZ_D T3
ON T3.pcs_cd = T1.PCS_CD
AND T3.PRJ_BLNG_TBL = 'CQZR.TCQZR_CQZRXXYPL'
AND T3.DT= T1.DT
WHERE T1.DT = '${dmp_day}'
AND T1.project_category= 'GQ'

--  6
-- 产权转让正式披露 项目审核通过才有项目编号,keyid

UNION

SELECT	
	 T1.INSTID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_FLCCQZR_FGKXYZR_D T1
	INNER JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_TCQZR_CQZRXXPL_D
	WHERE DT = '${dmp_day}' ) T2
	ON T1.PROJECT_ID = T2.ID
WHERE T1.DT = '${dmp_day}'

UNION

SELECT	
	 T1.PCS_EXMP_ID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_tLCZXDZ_LCXXJL_D T1
	LEFT JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_TCQZR_CQZRXXPL_D
	WHERE DT = '${dmp_day}') T2
	ON T1.PROJECT_ID = T2.ID
INNER JOIN STD.STD_BJHL_TXXTJ_JYSHBSJTJPZ_D T3
ON T3.pcs_nm = T1.pcs_nm
AND T3.PRJ_BLNG_TBL = 'CQZR.TCQZR_CQZRXXPL'
AND T3.DT= T1.DT
WHERE T1.DT = '${dmp_day}'
AND T1.project_category= 'GQ'

--  8 企业增资

UNION
-- pcs_nm匹配
SELECT	
	 T1.PCS_EXMP_ID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_tLCZXDZ_LCXXJL_D T1
	LEFT JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_tCGQ_ZZZSGPXM_D
	WHERE DT = '${dmp_day}') T2
	ON T1.PROJECT_ID = T2.ID
INNER JOIN STD.STD_BJHL_TXXTJ_JYSHBSJTJPZ_D T3
ON T3.pcs_nm = T1.pcs_nm
AND T3.PRJ_BLNG_TBL = 'BID.tCGQ_ZZZSGPXM'
AND T3.DT= T1.DT
WHERE T1.DT = '${dmp_day}'
AND T1.project_category= '1C'

UNION

-- pcs_cd匹配
SELECT	
	 T1.PCS_EXMP_ID AS pcs_id
	,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
	FROM STD.STD_BJHL_tLCZXDZ_LCXXJL_D T1
	LEFT JOIN (SELECT ID,PLTFRM_PRJ_ID AS XMID,PROJECT_NAME AS XMMC
	FROM STD.STD_BJHL_tCGQ_ZZZSGPXM_D
	WHERE DT = '${dmp_day}') T2
	ON T1.PROJECT_ID = T2.ID
INNER JOIN STD.STD_BJHL_TXXTJ_JYSHBSJTJPZ_D T3
ON T3.pcs_cd = T1.pcs_cd
AND T3.PRJ_BLNG_TBL = 'BID.tCGQ_ZZZSGPXM'
AND T3.DT= T1.DT
WHERE T1.DT = '${dmp_day}'
AND T1.project_category= '1C'


--  9 车类资产 解体车辆审核tcp_bdxx中swdyxm无值
UNION

SELECT	
       T1.INSTID AS pcs_id
       ,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
       FROM STD.STD_BJHL_flc_JTXXSHLC_D T1
       INNER JOIN (
              SELECT a.keyid AS ID,c.project_id AS XMID,a.obj_nm AS XMMC
              FROM std.STD_BJHL_tbid_rwdxx_d b
              LEFT JOIN std.std_bjhl_tcp_bdxx_d a
              ON a.rel_id = b.keyid and a.dt = b.dt
              LEFT JOIN std.std_bjhl_tbid_bdwxx_d c
              ON c.mtrl_obj_id = a.keyid and a.dt = c.dt
              where b.dt = '${dmp_day}'
              
) T2
ON T1.RLTV_OBJ = T2.ID
LEFT JOIN STD.STD_BJHL_OS_WFENTRY_D T3
ON T3.ID = T1.INSTID
WHERE T1.DT = '${dmp_day}'
AND SUBSTRING_INDEX(T3.PCS_CD,'.',1) = 'AW_JTXXSH'

--  10 车类资产 项目发布审核tcp_bdxx中的bdbh与tbid_gpxmxx值不同
UNION

SELECT	
       T1.INSTID AS PCS_ID
       ,T1.PRJ_INF AS PLFORM_PRJ_ID
       ,T1.PROJECT_NAME AS PRJ_NM -- 项目名称
       FROM STD.STD_BJHL_FLC_XM_CLXMFB_D T1
LEFT JOIN STD.STD_BJHL_OS_WFENTRY_D T3
ON T3.ID = T1.INSTID AND T1.DT = T3.DT
WHERE T1.DT = '${dmp_day}'
AND SUBSTRING_INDEX(T3.PCS_CD,'.',1) = 'AW_CLZCXMFBZYLC'

-- 11 车类资产 重新挂牌审核tcp_bdxx中的bdbh与tbid_gpxmxx值不同
UNION

SELECT	
       T1.INSTID AS pcs_id
       ,T1.pltfrm_prj_id AS plform_prj_id
       ,T1.project_name AS prj_nm -- 项目名称
       FROM STD.STD_BJHL_flc_XM_CLZCCXGPLC_D T1
LEFT JOIN STD.STD_BJHL_OS_WFENTRY_D T3
ON T3.ID = T1.INSTID AND T1.DT = T3.DT
WHERE T1.DT = '${dmp_day}'
AND SUBSTRING_INDEX(T3.PCS_CD,'.',1) = 'AW_CLZCCXGPLCZYLC'

-- 12 小宗实物
UNION

SELECT	
       T1.INSTID AS pcs_id
       ,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
       FROM STD.STD_BJHL_flcXZSW_PMGGFB_D T1
INNER JOIN (
       SELECT 
       a.keyid AS ID
       ,a.keyid AS XMID
       ,project_type
       ,a.project_name AS XMMC
       FROM 
       std.std_bjhl_tbid_gpxmxx_d a
       INNER JOIN std.std_bjhl_tbid_bdwxx_d e
       on e.project_id  = a.keyid and e.dt = a.dt
       INNER JOIN 
       std.std_bjhl_tcp_bdxx_d b ON e.mtrl_obj_id = b.keyid and e.dt = b.dt
       INNER JOIN 
       std.std_bjhl_txzsw_bdxx_d c ON b.keyid = c.id and b.dt = c.dt
       INNER JOIN 
       std.std_bjhl_tuser_d d ON c.inpt_psn = d.id and c.dt = d.dt
       WHERE a.dt = '${dmp_day}'
       AND a.project_type = '1F'
) T2
ON T1.RLTV_PRJ = T2.ID 
LEFT JOIN STD.STD_BJHL_OS_WFENTRY_D T3
ON T3.ID = T1.INSTID
WHERE T1.DT = '${dmp_day}'
AND SUBSTRING_INDEX(T3.PCS_CD,'.',1) = 'AW_XZSW_PMGGFB'

-- 13
UNION

SELECT	
       T1.INSTID AS pcs_id
       ,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
       FROM STD.STD_BJHL_flcXZSW_XMFBSHZTC_D T1
INNER JOIN (
       SELECT 
       a.keyid AS ID
       ,a.keyid AS XMID
       ,project_type
       ,a.project_name AS XMMC
       FROM 
       std.std_bjhl_tbid_gpxmxx_d a
       INNER JOIN std.std_bjhl_tbid_bdwxx_d e
       on e.project_id  = a.keyid and e.dt = a.dt
       INNER JOIN 
       std.std_bjhl_tcp_bdxx_d b ON e.mtrl_obj_id = b.keyid and e.dt = b.dt
       INNER JOIN 
       std.std_bjhl_txzsw_bdxx_d c ON b.keyid = c.id and b.dt = c.dt
       INNER JOIN 
       std.std_bjhl_tuser_d d ON c.inpt_psn = d.id and c.dt = d.dt
       WHERE a.dt = '${dmp_day}'
       AND a.project_type = '1F'
) T2
ON T1.PRJ_INF = T2.ID 
LEFT JOIN STD.STD_BJHL_OS_WFENTRY_D T3
ON T3.ID = T1.INSTID
WHERE T1.DT = '${dmp_day}'
AND SUBSTRING_INDEX(T3.PCS_CD,'.',1) = 'AW_XZSWXMFBSHZTCZY'

-- 14
UNION

SELECT	
       T1.INSTID AS pcs_id
       ,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
       FROM STD.STD_BJHL_flcXZSW_XMFBSH_D T1
INNER JOIN (
       SELECT 
       a.keyid AS ID
       ,a.keyid AS XMID
       ,project_type
       ,a.project_name AS XMMC
       FROM 
       std.std_bjhl_tbid_gpxmxx_d a
       INNER JOIN std.std_bjhl_tbid_bdwxx_d e
       on e.project_id  = a.keyid and e.dt = a.dt
       INNER JOIN 
       std.std_bjhl_tcp_bdxx_d b ON e.mtrl_obj_id = b.keyid and e.dt = b.dt
       INNER JOIN 
       std.std_bjhl_txzsw_bdxx_d c ON b.keyid = c.id and b.dt = c.dt
       INNER JOIN 
       std.std_bjhl_tuser_d d ON c.inpt_psn = d.id and c.dt = d.dt
       WHERE a.dt = '${dmp_day}'
       AND a.project_type = '1F'
) T2
ON T1.PRJ_INF = T2.ID 
LEFT JOIN STD.STD_BJHL_OS_WFENTRY_D T3
ON T3.ID = T1.INSTID
WHERE T1.DT = '${dmp_day}'
AND SUBSTRING_INDEX(T3.PCS_CD,'.',1) = 'AW_XZSW_XMFBSHLC'

-- 15 珍品 (按照JS文档无法正常关联，根据STD_BJHL_TXXTJ_JYSHBSJTJPZ_D 表中配置信息（PRJ_BLNG_TBL = 'BID.TCP_BDXX'）直接从流程表中获取相关信息)
UNION

SELECT  instid AS pcs_id
       ,xmid AS plform_prj_id
       ,xmmc AS prj_nm -- 项目名称
FROM ods.ods_bjhl_flc_xm_xmfb
WHERE class = '0A'
AND dt = '${dmp_day}'

-- 16 诉讼资产
UNION

SELECT	
       T1.INSTID AS pcs_id
       ,T2.XMID AS plform_prj_id
       ,T2.XMMC AS prj_nm -- 项目名称
       FROM STD.STD_BJHL_flcSSZC_GGJBDWLR_D T1
       LEFT JOIN (
              SELECT a.id AS ID,a.project_id AS XMID,a.obj_nm AS XMMC
              FROM  std.std_bjhl_tsszc_bdxx_d a
              WHERE a.dt = '${dmp_day}'
) T2
ON T1.XMID = T2.ID 
WHERE T1.DT = '${dmp_day}'
  
)

SELECT  DISTINCT T1.ID                                                                  AS pcs_id --流程ID 
       ,'BJHL'||T2.PLFORM_PRJ_ID                                                        AS prj_wrd --项目关键字 
       ,T1.PCS_CD                                                                       AS pcs_cd --流程代码 
       ,CASE WHEN shb_pz.Pcs_Cd = 'WF_FWCZHTXXSHGZL' THEN '房屋出租合同信息审核' 
              ELSE T3.RMK END                                                           AS pcs_nm --流程名称 
       ,T1.PCS_TTL                                                                      AS pcs_title --流程标题 
       ,T1.PCS_STS_CD                                                                   AS pcs_stat_cd --流程状态代码 
       ,CASE WHEN T1.PCS_STS_CD = 1 THEN '执行中'
             WHEN T1.PCS_STS_CD = 3 THEN '终止'
             WHEN T1.PCS_STS_CD = 4 THEN '完成' END                                      AS pcs_stat_cd_dsc --流程状态代码描述 
       ,NULL                                                                            AS pcs_cfgt_id --流程方案配置ID 
       ,T1.ITT_PSN_ID                                                                   AS pmer_id --发起人ID 
       ,T4.USERID                                                                       AS pmer_code --发起人编码 
       ,T4.NAME                                                                         AS pmer_nm --发起人名称 
       ,T4.ORGID                                                                        AS pmer_blng_dept_id --发起人所属部门ID 
       ,T5.NAME                                                                         AS pmer_blng_dept_nm --发起人所属部门名称 
       ,T5.FID                                                                          AS pmer_blng_cetr_id --发起人所属中心ID 
       ,T6.NAME                                                                         AS pmer_blng_cetr_nm --发起人所属中心名称 
       ,T1.ITT_TM                                                                       AS itt_tm --发起时间 
       ,T2.PRJ_NM                                                                       AS prj_nm --项目名称 
       ,oh.pcs_node_id                                                                  AS node_id --节点ID 
       ,t3.node_nm                                                                      AS node_nm --节点名称 
       ,t3.node_lvl                                                                     AS node_lvl --节点等级 
       ,NULL                                                                            AS exec_action_id --节点动作ID（后期需要删除20241211） 
       ,shb_pz.exec_action                                                              AS exec_action_nm --节点动作名称 
       ,shb_pz.pcs_nm                                                                   AS jyshb_pcs_nm -- 审核部流程配置名称（后期移到DWS） 
       ,shb_pz.prj_node                                                                 AS prj_stg --项目阶段/项目阶段（后期移到DWS） 
       ,shb_pz.project_type                                                             AS prj_tp --项目类型/项目类型（后期移到DWS） 
       ,NULL                                                                            AS stm_tp --系统类型/系统类型（后期移到DWS） 
       ,shb_pz.seq_no                                                                   AS sort_no --排序号（后期移到DWS） 
FROM STD.STD_BJHL_OS_WFENTRY_D T1 --流程表
LEFT JOIN (SELECT DISTINCT PLFORM_PRJ_ID,PCS_ID,PRJ_NM
FROM TMP_A) T2   --前置临时表
ON T1.ID = T2.PCS_ID
-- 关联过滤出执行动作记录
INNER JOIN std.std_bjhl_os_historystep_d oh   -- 审批-审批记录表（动作执行记录）
on oh.pcs_id = T1.id
and oh.dt = T1.dt
LEFT JOIN (
       SELECT  
               l.node_id --节点编号 
              ,l.node_nm --节点名称 
              ,l.tbl_nm --流程存储表单名称 
              ,l.node_lvl --节点等级 
              ,l.pcs_cd     AS pcs_cd --流程代码 
              ,l.rmk AS rmk --流程描述/名称 
       FROM (
              -- 同一个流程表单，节点ID，取最大的配置ID的节点名称(取节点名称、节点等级)
                     SELECT 
                            tbl_nm,
                            node_id,
                            node_nm,
                            node_lvl,
                            pcs_cfg_id,
                            pcs_cd,
                            rmk
                     FROM (
                            SELECT 
                            l.tbl_nm,
                            l.node_id,
                            l.node_nm,
                            l.node_lvl,
                            l.pcs_cfg_id,
                            l2.pcs_cd,
                            l2.`describe` AS rmk,
                            ROW_NUMBER() OVER (PARTITION BY l.tbl_nm, l.node_id, l.node_lvl,l2.pcs_cd,l2.`describe` ORDER BY pcs_cfg_id DESC) AS rank
                            FROM std.std_bjhl_lbagilewfruntimestep_d l
                            INNER JOIN std.std_bjhl_lbagilewfschemertdef_d l2
                            ON l.pcs_cfg_id = l2.id AND l2.dt = l.dt --定义-流程方案定义（含历史方案）
                            WHERE l.dt = '${dmp_day}' 
                     ) t
                     WHERE rank = 1
       ) l --定义-流程节点定义表（含历史方案） 
       
) T3
ON oh.pcs_node_id = t3.node_id 
AND SUBSTRING_INDEX(T1.PCS_CD,'.',1) =T3.pcs_cd
INNER JOIN std.std_bjhl_txxtj_jyshbsjtjpz_d shb_pz    -- 交易审核部流程配置表(dim.dim_pcs_cfgt来源)
ON shb_pz.Pcs_Cd = SUBSTRING_INDEX(T1.PCS_CD,'.',1)
AND shb_pz.dt = '${dmp_day}'
LEFT JOIN (SELECT  ID
				  ,USERID
				  ,USR_NM AS NAME
				  ,SUB_ORG AS ORGID
FROM STD.STD_BJHL_TUSER_D  --用户管理
WHERE DT = ${dmp_day}) T4
ON T1.ITT_PSN_ID =T4.ID 
LEFT JOIN (SELECT  ID
				  ,ORG_NM AS NAME
				  ,SUPR_NODE AS FID
FROM STD.STD_BJHL_LBORGANIZATION_D  --组织机构
WHERE DT = ${dmp_day}) T5
ON T4.ORGID =T5.ID 
LEFT JOIN (SELECT  ID
				  ,ORG_NM AS NAME
FROM STD.STD_BJHL_LBORGANIZATION_D  --组织机构
WHERE DT = ${dmp_day}) T6
ON T5.FID =T6.ID
WHERE T1.DT = ${dmp_day}
AND T2.PRJ_NM IS NOT NULL
AND (T3.RMK IS NOT NULL OR shb_pz.Pcs_Cd = 'WF_FWCZHTXXSHGZL')